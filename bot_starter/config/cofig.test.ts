import { ClientAccountConfig } from './config'

describe('Test', function () {
  beforeAll(() => {

  })

  it('allConfig', async () => {
    console.log(JSON.stringify(await ClientAccountConfig.pullAllConfig(), null, 4))
    // console.log(JSON.stringify(ClientAccountConfig.idServerAddressMap.size, null, 4))
    console.log(await ClientAccountConfig.getServerAddressByWechatId('****************'))
  }, 60000)

  it('singleConfig', async () => {
    console.log(JSON.stringify(await ClientAccountConfig.pullEnterpriseConfig(), null, 4))
    console.log(JSON.stringify(ClientAccountConfig.wechatNameMap.size, null, 4))
    console.log(JSON.stringify(await ClientAccountConfig.getAccountByName('syq'), null, 4))
  }, 60000)
})