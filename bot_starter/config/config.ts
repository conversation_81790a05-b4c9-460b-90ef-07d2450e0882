import { PrismaMongoClient } from '../../bot/model/mongodb/prisma'

export interface Account {
    orgToken: string
    nickname: string
    wechatId: string
    botUserId: string
    address: string
    port: number
    notifyGroupId: string
    counselors: string[]
    xbbId?: string
}

interface IBaoshuEnterpriseConfig {
    notifyGroupId: string
    counselors: string[]
    xbbId?: string
}

export class ClientAccountConfig {
  public static idServerAddressMap: Map<string, string> = new Map() // wechatId -> serverAddress
  public static wechatNameMap: Map<string, Account> = new Map() // nickname -> account
  private static lastCacheUpdateTime: number = 0 // 上次缓存更新时间

  public static async pullAllConfig() {
    const configs = await PrismaMongoClient.getConfigInstance().config.findMany()

    for (const config of configs) {
      this.idServerAddressMap.set(config.wechatId, config.address)
    }
    this.lastCacheUpdateTime = Date.now()
  }

  public static async pullEnterpriseConfig() {
    const configs = await PrismaMongoClient.getConfigInstance().config.findMany(
      {
        where: {
          enterpriseName: 'baoshu'
        }
      }
    )
    for (const config of configs) {
      const enterpriseConfig = config.enterpriseConfig as any as IBaoshuEnterpriseConfig

      this.wechatNameMap.set(config.accountName, {
        orgToken: config.orgToken,
        nickname: config.accountName,
        wechatId: config.wechatId,
        botUserId: config.botUserId,
        address: config.address,
        port: Number(config.port),
        notifyGroupId: enterpriseConfig.notifyGroupId,
        counselors: enterpriseConfig.counselors,
        xbbId: enterpriseConfig.xbbId
      })
    }
  }

  public static async getServerAddressByWechatId(wechatId: string) {
    // 如果本地没有，从数据库尝试读取
    if (this.idServerAddressMap.has(wechatId)) {
      return this.idServerAddressMap.get(wechatId)
    }

    await this.pullAllConfig()
    return this.idServerAddressMap.get(wechatId)
  }

  /**
   * 清除服务器地址缓存
   * 当数据库配置更新时调用此方法
   */
  public static async clearServerAddressCache() {
    await this.pullAllConfig()
    return {
      success: true,
      timestamp: this.lastCacheUpdateTime,
      message: '服务器地址缓存已清除并重新加载'
    }
  }

  /**
   * 获取缓存最后更新时间
   */
  public static getCacheUpdateTime() {
    return this.lastCacheUpdateTime
  }

  public static async getAccountByName(name: string) {
    if (this.wechatNameMap.has(name)) {
      return this.wechatNameMap.get(name)
    }

    await this.pullEnterpriseConfig()
    return this.wechatNameMap.get(name)
  }
}