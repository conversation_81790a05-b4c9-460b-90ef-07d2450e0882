import express from 'express'
import chalk from 'chalk'
import { IReceivedMessage, ISendMessageResult } from '../../bot/lib/juzi/type'
import { EventDispatcher } from './event_dispatch'
import { DelayedTask } from '../../bot/lib/schedule/delayed_task'
import { LRUCache } from 'lru-cache'
import axios from 'axios'
import { ClientAccountConfig } from '../config/config'

// 详细状态类型
type DetailStatus =
    | 11  // PC扫码登录
    | 12  // PC上线
    | 21  // PC掉线
    | 31  // PC关闭客户号离线
    | 32  // PC关闭客户端离线
    | 33  // PC触发监控下线
    | 41; // 封号

// 状态类型
type Status = 10 | 20 | 30 | 40; // 在线、掉线、离线、死号

// 平台类型
type PlatformType = 1 | 2; // 1=PC, 2=Android

// 单个 WhatsApp 状态对象
interface WhatsAppItem {
  whatsId: string         // whatsapp 手机号
  status: Status          // 状态
  detailStatus?: DetailStatus // 详细状态（可选，示例中没有出现）
  account: string         // 坐席账号
  platformType: PlatformType // 平台类型
  time: string            // 时间，格式如 "2025-04-01T16:57:52.879"
}

// 最外层请求体
interface WhatsAppStatusRequest {
  whatsAppList: WhatsAppItem[] // 最多50条
}

const app = express()
app.use(express.json())

app.get('/', (req, res) => {
  res.send('Hello World!')
})

// API端点清除服务器地址缓存
app.post('/api/clear-server-address-cache', async (req, res) => {
  try {
    const result = await ClientAccountConfig.clearServerAddressCache()
    res.json(result)
  } catch (error) {
    console.error('清除缓存失败:', error)
    res.status(500).json({
      success: false,
      message: '清除缓存失败',
      error: error instanceof Error ? error.message : String(error)
    })
  }
})

// 获取缓存状态
app.get('/api/cache-status', (req, res) => {
  try {
    const lastUpdateTime = ClientAccountConfig.getCacheUpdateTime()
    const cacheSize = ClientAccountConfig.idServerAddressMap.size

    res.json({
      success: true,
      lastUpdateTime,
      lastUpdateTimeFormatted: new Date(lastUpdateTime).toLocaleString(),
      cacheSize
    })
  } catch (error) {
    console.error('获取缓存状态失败:', error)
    res.status(500).json({
      success: false,
      message: '获取缓存状态失败',
      error: error instanceof Error ? error.message : String(error)
    })
  }
})

// 对回调事件进行统一处理
app.post('/wecom/message', async (req, res) => {
  // 接收消息
  const data = req.body as IReceivedMessage
  EventDispatcher.dispatchMessage(data)

  res.send('ok')
})


app.post('/wecom/event', async (req, res) => {
  const data = req.body
  EventDispatcher.dispatchEvent(data)
  res.send('ok')
})


// 对消息发送失败。进行报警处理
app.post('/wecom/sendResult', async (req, res) => {
  // 接收消息
  const data = req.body as ISendMessageResult
  const chat_id = `${data.imContactId}_${data.imBotId}`

  if (data.sendCode !== 0) {
    console.log('消息发送失败', data, chat_id, data.errorcode, data.errormsg)
  } else {
    EventDispatcher.dispatchSendResult(data)
  }

  res.send('ok')
})


app.post('/aochuang/event', async (req, res) => {
  // 接收消息
  const data = req.body

  console.log(JSON.stringify(data, null, 4), 'aochuang')

  EventDispatcher.dispatchAoChuangEvent(data)

  res.send({
    'code': 200,
    'message': 'success!',
    'data': ''
  })
})

const accountStatus = new LRUCache<string, number>({ max: 100 })

app.post('/aochuang/onlineStatus', async (req, res) => {
  // 接收消息
  const data = req.body as WhatsAppStatusRequest

  for (const whatsAppItem of data.whatsAppList) {
    accountStatus.set(whatsAppItem.whatsId, whatsAppItem.status)

    if (whatsAppItem.account.includes('AI') && whatsAppItem.status !== 10) {
      // 掉线通知
      const delayedTask = new DelayedTask({ m: 2 }, async () => {
        await axios.get(`https://fwalert.com/e99c7039-aba2-4ee3-8c40-f4fd5ecbf0fa?account=${encodeURIComponent(whatsAppItem.account)}`)
      }, async () => {
        return accountStatus.get(whatsAppItem.whatsId) !== 10
      })

      delayedTask.start()
    }
  }

  console.log('傲创在线状态：', JSON.stringify(data, null, 4))

  res.send('ok')
})


function catchGlobalError() {
  // 捕获未捕获的同步异常
  process.on('uncaughtException', (err) => {
    console.error(err, 'Uncaught Exception')
  })

  // 捕获未处理的 Promise 拒绝
  process.on('unhandledRejection', (reason, promise) => {
    console.error({ promise, reason }, 'Unhandled Rejection at: Promise')
  })
}

catchGlobalError()

app.listen(6001, '0.0.0.0', () => {
  console.log(chalk.green('句子消息/事件分发服务器已启动'), chalk.red('原') +
        chalk.greenBright('神') +
        chalk.blueBright('启') +
        chalk.yellowBright('动') +
        chalk.magentaBright('！'))
  console.log('Server is running on port 6001')
})