import { FileHelper } from '../../bot/lib/file'
import path from 'path'
import { JSONHelper } from '../../bot/lib/json/json'
import { ClientAccountConfig } from '../config/config'
import axios from 'axios'
import { EventDispatcher } from './event_dispatch'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    // 读取配置文件，构建 map，选择对应的服务地址
    const serverFile = await FileHelper.readFile(path.join(__dirname, 'server.json'))
    const serverMap = JSONHelper.parse(serverFile)

    console.log(serverMap['abc'])
  })

  it('test', async () => {
    console.log(ClientAccountConfig.getServerAddressByWechatId('****************'))
  }, 60000)

  it('test1', async () => {
    const data = {
      'token': 'e0d70927040a4efa92b79b7279ecb1c1',
      'eventType': 4,
      'logoutReason': '登录态已过期，请重新登录',
      'bot': {
        'wecomUserId': 'BaoShu_1',
        'partnerOrgId': '',
        'botCorpId': '****************',
        'orgCorpId': 'wwab5bea5790454612',
        'botId': '66f66f100d9b8efae5dda702',
        'imBotId': '****************',
        'orgId': '661ce5985ed60835eb133f49',
        'status': 4,
        'name': '躺着数钱1（暴叔）',
        'avatar': 'https://wework.qpic.cn/wwpic/259363_GuumOzuOTueKIh1_1695011145/0',
        'type': 2,
        'group': {
          'groupId': '661ce59f5ed60835eb1343ff',
          'groupName': '船奇渔业'
        }
      }
    }

    // 掉线通知
    if (data.eventType && data.eventType === 4 && data.logoutReason && data.bot) {
      await axios.get(`https://fwalert.com/32622b0f-9455-43ea-8f0d-cc3ece5a4ab2?account=${encodeURIComponent(data.bot.name)}&reason=${encodeURIComponent(data.logoutReason)}`)
    }
  }, 60000)

  it('aochuang msg', async () => {
    const event = {
      'accessToken': 'bba498388b4b63768138addd42e4dd6742e6e0be4b284930297b541032754cd3',
      'callBackUrl': 'http://wascrm.socialepoch.com/wscrm-bus-api/part/callback/message',
      'tenantId': 544999,
      'timestamp': *************,
      'data': [
        {
          'id': '3138e841a87e405d9aa33c113b54e6d8',
          'messageId': '3EB0512EFB08046B3270A7',
          'userName': 'zhangxiaoxin',
          'whatsId': '***********',
          'friendWhatsId': '***********',
          'currentWhatsId': '***********',
          'actionType': 1,
          'chatType': 1,
          'messageStatus': 3,
          'content': '同学下午好，咱们今晚8点就是第一节课程。老师正在统计大家最关注的问题，方便唐宁老师在今晚的直播中重点去讲✨3.提高专注力\n✨4.财富收获\n✨5.情感纠纷\n✨6.睡眠状态',
          'contentType': 0,
          'originType': 1,
          'sendTime': '2025-02-24 15:12:28',
          'userProfile': {
            'id': 8581501346685772000,
            'name': '14期Victor Ng5809',
            'whatsApp': '***********',
            'username': 'xiaoyuchen',
            'extendsMap': '',
            'income': '',
            'profession': '',
            'desc': '',
            'tags': [
              '五天营14期',
              '14期群发2'
            ],
            'email': '<EMAIL>',
            'address': ''
          }
        }
      ]
    }

    const eventData = event.data as any[]

    if (eventData.every(EventDispatcher.isAoChuangMsg)) {
      console.log('hi')
    }
  }, 60000)

  it('123123', async () => {
    const options: any = {
      // isMsg: true,
      isAoChuang: true,
    }

    let subRoute = 'event'
    if (options?.isMsg) {
      subRoute = 'message'
    }

    if (options?.isAoChuang) {
      subRoute = 'aochuang/event'
    }


    console.log(subRoute)
  }, 60000)
})