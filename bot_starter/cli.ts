const inquirer = require('inquirer')

import { execSync } from 'node:child_process'

// 定义问题列表
const questions = [
  {
    type: 'list',
    name: 'environment',
    message: '请选择您想要启动的环境：',
    choices: ['测试环境(test)', '生产环境(prd)'],
  },
]

// 使用 inquirer 提问
inquirer.prompt(questions).then((answers) => {
  // 根据用户的选择执行不同的操作
  switch (answers.environment) {
    case '生产环境(prd)':
      console.log('正在启动生产环境...')
      // 这里添加生产环境的启动命令或逻辑
      execSync('npm run prd')
      break
    case '测试环境(test)':
      console.log('正在启动测试环境...')
      // 这里添加测试环境的启动命令或逻辑
      execSync('npm run test')
      break
    default:
      console.log('未知环境')
      break
  }
})