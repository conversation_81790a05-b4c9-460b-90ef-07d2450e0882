import { IChatroomMessage } from '../../bot/lib/auchuang/event/type'
import { HumanTransfer, HumanTransferType } from '../../bot/service/baoshu/components/human_transfer'
import { AoChuangWechatContact } from '../../bot/lib/auchuang/openapi/contact'
import { getChatId } from '../../bot/config/chat_id'
import { ChatStateStore } from '../../bot/service/baoshu/storage/chat_state_store'
import { GroupDB } from '../../bot/service/baoshu/database/group'
import logger from '../../bot/model/logger/logger'

export interface PendingAddFriendToGroup {
    chatroomId: string
    wxId: string
}

export class GroupMemberJoinHandler {
  private static queue: PendingAddFriendToGroup[] = []

  // 添加事件到队列
  public static listen(pendingAddFriendToGroup: PendingAddFriendToGroup): void {
    this.queue.push(pendingAddFriendToGroup)
  }

  // 处理事件，尝试从队列中匹配并删除对应事件
  public static async handleEvent(groupMessageEvent: IChatroomMessage): Promise<void> {
    // 将content字符串拆分为chatroomId和JSON字符串
    const splitIndex = groupMessageEvent.content.indexOf(':\n')
    if (splitIndex === -1) {
      console.log(`群消息：${groupMessageEvent.content}`)
      return
    }

    const jsonStr = groupMessageEvent.content.substring(splitIndex + 2)

    try {
      const contentJson = JSON.parse(jsonStr)

      if (!contentJson.hasOwnProperty('members')) { // 只检查群邀请消息
        return
      }
      const members: { nickname: string; wechatId: string }[] = contentJson.members

      for (const member of members) {
        const index = this.queue.findIndex((event) =>
          event.chatroomId === groupMessageEvent.chatroomId &&
                    event.wxId === member.wechatId
        )

        if (index !== -1) {
          // 事件匹配成功，处理事件
          this.processEvent(this.queue[index])

          // 从队列中移除事件
          this.queue.splice(index, 1)
          return
        }

        // 还有种情况，member 是我们手动拉群进的，这个时候只需要更新下标签
        const currentGroup = await GroupDB.getCurrentGroup()
        if (currentGroup && currentGroup.id === groupMessageEvent.chatroomId) {
          const contact = await AoChuangWechatContact.getContactByWxId(member.wechatId)
          if (contact) {
            const chat_id = getChatId(contact.id)
            // ChatStateStore.setIsJoinGroup(chat_id, true)
            // 更改备注
            await AoChuangWechatContact.updateAlias({ id: contact.id, alias: '[B]', leading: true, replace: /\[on]|\[off]|\[B]/ })
            logger.debug(chat_id, '手动拉群进的，更新下标签')
          }
        }
      }
    } catch (error) {
      if (error instanceof SyntaxError) {
        // 忽略无法解析的JSON字符串，一般为群文本消息
      } else {
        console.error('Error parsing the event content:', error)
      }

    }
  }

  private static async processEvent(event: PendingAddFriendToGroup) {
    // 用户进群通知
    const contact = await AoChuangWechatContact.getContactByWxId(event.wxId)
    if (contact) {
      const chat_id = getChatId(contact.id)

      // ChatStateStore.setIsJoinGroup(chat_id, true)
      await HumanTransfer.transfer(chat_id, contact.id, HumanTransferType.JoinedGroup)
    }
  }

  static queueHasTask(chatroomId: string, wechatId: string) {
    return this.queue.some((event) =>
      event.chatroomId === chatroomId &&
            event.wxId === wechatId
    )
  }

  static isUserNotJoined(chatroomId: string, wechatId: string) {
    for (const pendingAddFriendToGroup of this.queue) {
      if (pendingAddFriendToGroup.chatroomId === chatroomId && pendingAddFriendToGroup.wxId === wechatId) {
        return true
      }
    }

    return false
  }
}