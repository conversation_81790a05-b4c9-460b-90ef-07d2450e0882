import { IFriendMessageDownloadOriginImageResultEvent } from '../../bot/lib/auchuang/event/type'
import { MessageReplyService } from '../../bot/service/message/message_reply'

export class HDImageHandler {
  private static taskMap: Map<string, { userId: string; wechatId: string }> = new Map()

  public static async handle(msg: IFriendMessageDownloadOriginImageResultEvent) {
    if (this.taskMap.has(msg.taskId)) {
      const value = this.taskMap.get(msg.taskId) as { userId: string; wechatId: string}
      this.taskMap.delete(msg.taskId)
      // MessageReplyService.replyImage(msg.content, value.userId)
    }
  }

  static addTask(param: { userId: string; taskId: string; wechatId: string }) {
    this.taskMap.set(param.taskId, { userId: param.userId, wechatId: param.wechatId })
  }
}