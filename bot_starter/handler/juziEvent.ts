import { z } from 'zod'
import { getChatId } from '../../bot/config/chat_id'
import { LRUCache } from 'lru-cache'
import { WechatMessageSender } from '../../bot/service/baoshu/components/message_send'
import { sleep } from '../../bot/lib/schedule/schedule'
import { SilentReAsk } from '../../bot/service/baoshu/components/silent_requestion'
import { IntentionCalculateNode } from '../../bot/service/baoshu/components/flow/nodes/intentionCalculate'
import { UUID } from '../../bot/lib/uuid/uuid'


// 定义 FollowUser 接口
interface FollowUser {
    wecomUserId: string
}

// 定义 ImInfo 接口
interface ImInfo {
    externalUserId: string
    followUser: FollowUser
}

// 定义 BotInfo 接口
interface BotInfo {
    botId: string
    imBotId: string
    name: string
    avatar: string
}

// 定义主接口 Contact
interface FriendAcceptedEvent {
    imContactId: string
    name: string
    avatar: string
    gender: number
    createTimestamp: number
    imInfo: ImInfo
    botInfo: BotInfo
}

export class JuziEvent {
  private static readonly eventSet = new LRUCache<string, any>({ max: 3000 })


  public static async handle(data: any) {
    // 使用 zod 校验
    if (this.isFriendAcceptedEvent(data)) {
      return await this.handleFriendAcceptedEvent(data as FriendAcceptedEvent)
    }
  }

  private static isFriendAcceptedEvent(data: any) {
    const schema = z.object({
      imContactId: z.string(),
      name: z.string(),
      avatar: z.string().url(),
      createTimestamp: z.number(),
      imInfo: z.object({
        externalUserId: z.string(),
        followUser: z.object({
          wecomUserId: z.string()
        })
      }),
      botInfo: z.object({
        botId: z.string(),
        imBotId: z.string(),
        name: z.string(),
        avatar: z.string().url()
      })
    })

    const result = schema.safeParse(data)

    return result.success
  }

  private static async handleFriendAcceptedEvent(data: FriendAcceptedEvent) {
    if (this.eventSet.has(data.imContactId)) {
      return
    }
    this.eventSet.set(data.imContactId, true)

    // 只处理一次加好友事件
    const userId = data.imContactId
    const chatId = getChatId(userId)

    await SilentReAsk.schedule(chatId, async () => {
      await WechatMessageSender.sendById({
        user_id: userId,
        chat_id: chatId,
        ai_msg: '你好，请说',
      })
    }, 3, false)

  }
}