import { IAcceptFriendTaskResultEvent, WeChatFriend } from '../../bot/lib/auchuang/event/type'
import { WechatMessageSender } from '../../bot/service/baoshu/components/message_send'
import { sleep } from 'openai/core'
import { AoChuang } from '../../bot/lib/auchuang/openapi/aochuang'
import { NewFriendDB } from '../../bot/service/baoshu/database/new_friend'
import { getChatId } from '../../bot/config/chat_id'
import { AoChuangWechatContact } from '../../bot/lib/auchuang/openapi/contact'
import { randomSleep } from '../../bot/lib/schedule/schedule'
import { SilentReAsk } from '../../bot/service/baoshu/components/silent_requestion'
import { EventTracker } from '../../bot/model/logger/data_driven'

import { IMessageType } from '../../bot/service/message/message'
import { Config } from '../../bot/config/config'
import { ABTest } from '../../bot/model/a_btest/a_b_test'

export class newFriendRequestHandler {
  public static async handle(wechatFriend: WeChatFriend): Promise<void> {
    if (!wechatFriend.isPassed) {
      await randomSleep(5000, 10000)

      // 自动通过好友请求
      const res = await AoChuang.acceptFriendRequest(wechatFriend.id)
      // 存储到数据库
      await NewFriendDB.create({
        id: res.taskId,
        friendId: wechatFriend.id,
        name: wechatFriend.nickname,
        wechatId: Config.setting.wechatConfig?.id as string
      })
    }
  }
}

export class NewFriendAcceptHandler {
  public static async getWechatId(event: IAcceptFriendTaskResultEvent) {
    // 确认下 taskId 是否匹配
    const newFriend = await NewFriendDB.getByTaskId(event.taskId)

    if (!newFriend) {
      return
    }

    return newFriend.wechatId
  }


  public static async handle(event: IAcceptFriendTaskResultEvent): Promise<void> {
    await sleep(2000) // 等待下存库

    // 确认下 taskId 是否匹配
    const newFriend = await NewFriendDB.getByTaskId(event.taskId)

    // 发送欢迎语
    if (!newFriend) {
      console.log('newFriendAcceptHandler: 找不到新好友')
      return
    }

    const friendId = newFriend.friendId

    const chat_id = getChatId(friendId)

    EventTracker.track(chat_id, '好友请求通过')

    // AB Test
    EventTracker.track(chat_id, 'AB Test', { user_group: ABTest.getStrategyForUser(friendId, 2) })

    await sleep(3 * 1000)
    await WechatMessageSender.sendById({ user_id: friendId, chat_id, ai_msg: 'hihi，我是up老师的小助教，拥有up所有的资料库，叫我小爱就好' })
    await sleep(1.5 * 1000)
    await WechatMessageSender.sendById({ user_id: friendId, chat_id, ai_msg: '同学是来领资料的吧，可以【一键转发】给我，我好去给你找哦~[机智]' })

    // 设置备注
    const date = new Date()
    const year = date.getFullYear()
    const month = date.getMonth() + 1
    const day = date.getDate()
    await AoChuangWechatContact.updateAlias({
      id: friendId,
      alias: `[on]「${year - 2000}${month < 10 ? `0${  month}` : month}${day < 10 ? `0${  day}` : day}」`,
      leading: true
    })

    // SilentReAsk.startTimer(chat_id, friendId)
  }
}