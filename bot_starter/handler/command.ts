import { IChatroomMessage, WechatGroup } from '../../bot/lib/auchuang/event/type'
import { AoChuangWechatContact } from '../../bot/lib/auchuang/openapi/contact'
import { AoChuang } from '../../bot/lib/auchuang/openapi/aochuang'
import { ChatDB } from '../../bot/service/baoshu/database/chat'
import { getChatId } from '../../bot/config/chat_id'
import { AoChuangWechatGroupContact } from '../../bot/lib/auchuang/openapi/group_contact'
import { GroupDB } from '../../bot/service/baoshu/database/group'
import { ObjectUtil } from '../../bot/lib/object'
import { CacheDecorator } from '../../bot/lib/cache/cache'
import { GroupNotification } from '../../bot/service/baoshu/notification/group'
import { Config } from '../../bot/config/config'
import { EventTracker } from '../../bot/model/logger/data_driven'
import { HumanTransferType } from '../../bot/service/baoshu/components/human_transfer'

export enum CommandType {
    HELP = '帮助',
    USER_STATUS = '用户状态',
    OPEN_AI = '开启AI',
    CLOSE_AI = '关闭AI',
    QUERY_GROUP = '查询社群',
    SET_GROUP = '设置社群',
    GET_ALL_GROUP = '所有社群',
}

export class CommandHandler {
  public static async handle(chatroomMessage: IChatroomMessage) {
    const msg = chatroomMessage.content.split(':\n')
    if (msg.length < 2) {
      return
    }

    const commandAndArgs = msg[1].split('】')
    let command = commandAndArgs[0]
    if (command && command.length > 0) {
      command = command.slice(1)
    }
    switch (command) {
      case CommandType.HELP:
        let helpMsg = '命令列表：\n'
        helpMsg += ObjectUtil.enumValues(CommandType).map((v) => `【${v}】`).join('\n')
        GroupNotification.notify(helpMsg)
        break
      case CommandType.USER_STATUS:
        break
      case CommandType.OPEN_AI:
      case CommandType.CLOSE_AI:
        const wechatId = commandAndArgs[1].trim()
        let user = await AoChuangWechatContact.getContactByWechatId(wechatId)
        if (!user) {
          // 再尝试下使用用户名查找
          user = await AoChuangWechatContact.getContactByName(wechatId)
          if (!user) {
            GroupNotification.notify(`未找到用户：${wechatId}`)
            return
          }
        }


        const chat_id = getChatId(user.id)
        const chat = await ChatDB.getById(chat_id)
        if (!chat) {
          GroupNotification.notify(`用户暂未聊过天，无法操作：${wechatId}`)
          return
        }

        const isHumanInvolved = command === CommandType.CLOSE_AI
        await AoChuangWechatContact.updateAlias({ id: user.id, alias: isHumanInvolved ? '[off]' : '[on]', leading: true, replace: /\[on]|\[off]/ })

        EventTracker.track(chat_id, isHumanInvolved ? '转交人工' : '转交机器人', { reason: '手动关闭' })

        await ChatDB.setHumanInvolvement(chat_id, isHumanInvolved)
        GroupNotification.notify(command === CommandType.OPEN_AI ? `${user.nickname} 已开启AI` : `${user.nickname} 已切换人工`)
        break
      case CommandType.QUERY_GROUP:
        const currentGroup = await GroupDB.getCurrentGroup()
        if (!currentGroup) {
          GroupNotification.notify('当前未设置社群')
        } else {
          GroupNotification.notify(`当前社群：${currentGroup.name}`)
        }
        break
      case CommandType.SET_GROUP:
        CacheDecorator.clear(AoChuangWechatGroupContact.getContactByName)
        const groupName = commandAndArgs[1].trim()

        console.log(`查询的社群名称：${  groupName}`)
        let group: WechatGroup | undefined
        if (groupName.endsWith('@chatroom')) {
          group = await AoChuangWechatGroupContact.getContactByChatRoomId(groupName)
        } else {
          group = await AoChuangWechatGroupContact.getContactByName(groupName)
        }


        if (!group) {
          GroupNotification.notify(`未找到社群：${groupName}`)
          return
        }

        await GroupDB.switchGroup(group.chatroomId, group.nickname)
        GroupNotification.notify(`已切换社群：${group.nickname}`)
        break
      case CommandType.GET_ALL_GROUP:
        CacheDecorator.clear(AoChuangWechatGroupContact.getContactByName)
        const allGroup = (await AoChuang.getAllGroups()).filter((group) =>  group.wechatAccountId === Config.setting.wechatConfig?.id)

        const obj = {}
        allGroup.forEach((group) => {
          obj[group.chatroomId] = group.conRemark ? group.conRemark : group.nickname
        })
        GroupNotification.notify(`所有社群：\n ${JSON.stringify(obj, null, 4)}`)
        break
      default:
        // 忽略机器人自己的消息
        console.log('未知命令：', commandAndArgs[0])
    }
  }
}