import express from 'express'
import { IEventData } from '../../bot/lib/auchuang/event/type'
import { initConfig } from './init'
import { ClientAccountConfig } from '../config/config'
import { IReceivedMessage } from '../../bot/lib/juzi/type'
import { GlobalMessageHandlerService } from '../../bot/service/message/message_merge'
import { CacheDecorator } from '../../bot/lib/cache/cache'
import { JuziAPI } from '../../bot/lib/juzi/api'
import { XbbHelper } from '../../bot/service/baoshu/components/flow/nodes/helper/xbbHelper'
import { getUserId } from '../../bot/config/chat_id'
import { JuziEvent } from '../handler/juziEvent'

const app = express()
app.use(express.json())

app.get('/', (req, res) => {
  res.send('Hello Client!')
})

app.post('/message', async (req, res) => {
  // 接收消息
  const msg: IReceivedMessage = req.body

  GlobalMessageHandlerService.addMessage(msg) // 添加到消息队列
  res.send('ok')
})

app.post('/event', async (req, res) => {
  // 接收消息
  const data: IEventData = req.body

  console.log(JSON.stringify(data, null, 4))

  JuziEvent.handle(data)
  res.send('ok')
})


app.post('/tags', async (req, res) => {
  // 接收消息
  const data = req.body

  // await HumanTransfer.updateTags(data.userId, data.tag)
  res.send('ok')
})


app.post('/sendResult', async (req, res) => {
  // 接收消息
  const data = req.body

  res.send('ok')
})


app.post('/label', async (req, res) => {
  try {
    // 接收消息
    const data = req.body
    if (!data.chatId) {
      return res.send('ok') // 这里加了 return，避免继续执行
    }

    const userId = getUserId(data.chatId)

    // 调用 XbbHelper.labelClue 并捕获可能的错误
    await XbbHelper.labelClue(data.chatId, userId)

    res.send('ok')
  } catch (error) {
    console.error('Error in /label route:', error) // 打印错误日志，便于调试

    // 返回错误响应，避免应用崩溃
    res.status(500).send('An error occurred while labeling clue')
  }
})

async function startServer() {
  const name = process.env.WECHAT_NAME
  if (!name) {
    console.error('请设置环境变量 WECHAT_NAME')
    process.exit(1)
  }

  const account = await ClientAccountConfig.getAccountByName(name)
  if (!account) {
    console.error(`找不到${name}对应的账号`)
    process.exit(1)
  }

  app.listen(account.port, '0.0.0.0', () => {
    console.log(`Server is running on port ${account.port}`)
  })

  // 缓存 API，提高性能
  JuziAPI.getCustomerInfo = CacheDecorator.decorateAsync(JuziAPI.getCustomerInfo)
  JuziAPI.externalIdToWxId = CacheDecorator.decorateAsync(JuziAPI.externalIdToWxId)
  initConfig()
}

startServer()