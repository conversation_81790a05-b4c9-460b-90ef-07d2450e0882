# 暴叔留学接量项目

要开始使用此项目，请按照以下步骤进行操作：

## 步骤1：安装依赖项
运行下面的命令来安装所有必要的依赖项：
`yarn install`
生成 prisma client
`prisma generate`
## 步骤2: 创建新的客户端
首先，创建一个新的客户端，并将从句子后台获取的对应的企业微信配置插入到`bot_starter/config/account.dev.json`。

在`address`部分，输入客户端调用的地址（您可以输入配置内网穿透后的地址），在`port`部分，输入启动本地客户端的端口（确保不与现有端口重复）

### 启动现有的客户端
配置中的`syq`客户端目前使用`natapp.cn`进行内网穿透。

从 `https://natapp.cn/#download` 下载对应的客户端，然后使用 `-authtoken=98992eb2883bc316` 启动内网穿透

使用以下命令启动本地服务：
`npm run start:client:syq`


### 创建新的客户端
1. 购买企微账号，添加配置到`bot_starter/config/account.dev.json`中
2. package.json 添加命令 "npm run client:xxx", 注意将对应的账号名注入
3. 本地启动内网穿透， 从 `https://natapp.cn/#download` 下载对应的客户端， 然后使用 `./natapp -authtoken=xxx` 启动内网穿透
4. 服务器添加消息转发配置，复制 bot_starter/config/account.dev.json 给 钺清来添加
5. 本地启动 npm run client:xxx 客户端，就可以给微信发送并接收消息了

## 注意：
1. 每个模块尽量进行单元测试，方法的参数尽可能少，使单元测试变的简单。
2. 尽量使用封装后的 LLM 类进行模型调用，有模型轮转机制，以及配置了 Langsmith 方便查看日志
3. 日志不要使用 console.log, 尽量使用 logger.log 使用，方便日志持久化后，进行错误排查
4. 推荐的 Prompt 编写流程 
   1. 首先深入理解业务，弄清自己要编写的内容的运行规则 (去抽象化) （参考实习生原理，假设实习生能一眼看懂，并不需要额外信息补充直接执行）。 
   2. 使用 MetaPrompt 方法调用生成
   3. 编写单元测试，进行测试。
   4. 如果效果不好，可以尝试修改 Prompt 或 使用 PromptPerfect 交互式进行优化
   5. 如修改任务描述不起作用，可以尝试将不好的 Case 加入到 few shots 中