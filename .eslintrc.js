/**
 * 详细规则文档 https://cloud.tencent.com/developer/section/1135580
 * 注意：此文件不允许随意修改
 **/
module.exports = {
  root: true,
  env: {
    node: true,
    jest: true,
  },
  extends: [
    'plugin:@typescript-eslint/recommended',
    'prettier',
  ],
  parser: '@typescript-eslint/parser',
  plugins: ['@typescript-eslint'],
  rules: {
    'semi': [2, 'never'],
    'radix': [2, 'always'],
    'indent': [2, 2, { 'SwitchCase': 1 }],
    'no-var': 2,
    'no-with': 2,
    'no-eval': 2,
    'no-useless-return': 2,
    'no-useless-rename': 2,
    'no-unreachable': 2,
    'no-trailing-spaces': 2,
    'no-this-before-super': 2,
    'no-self-compare': 2,
    'no-redeclare': 2,
    'no-path-concat': 2,
    'no-return-assign': 2,
    'prefer-const': 2,
    'getter-return': 2,
    'quotes': [2, 'single'],
    'arrow-parens': [2, 'always'],
    'block-scoped-var': 2,
    'block-spacing': 2,
    'space-before-blocks': 2,
    'max-lines': [2, { 'max': 1000, 'skipBlankLines': true, 'skipComments': true, }],
    'object-curly-spacing': [2, 'always'],
    'arrow-spacing': [2, { 'before': true, 'after': true }],
    'comma-spacing': ['error', { 'before': false, 'after': true }],
    'no-array-constructor': 0,
    'no-class-assign': 2,
    'no-cond-assign': 2,
    'no-constant-condition': 2,
    'no-delete-var': 2,
    'no-dupe-keys': 2,
    'no-duplicate-case': 2,
    'no-duplicate-imports': 2,
    'no-global-assign': 2,
    'no-implicit-coercion': 2,
    'no-new-func': 2,
    'prefer-template': 2,
    'space-in-parens': [2, 'never'],
    'prefer-spread': 2,
    'prefer-rest-params': 0,
    'space-infix-ops': [2, { 'int32Hint': false }],
    'space-before-function-paren': 0,
    '@typescript-eslint/prefer-for-of': 0,
    '@typescript-eslint/no-this-alias': 0,
    '@typescript-eslint/prefer-as-const': 2,
    '@typescript-eslint/no-var-requires': 0,
    '@typescript-eslint/no-unused-vars': 0,
    '@typescript-eslint/no-explicit-any': 0,
    '@typescript-eslint/no-dynamic-delete': 2,
    '@typescript-eslint/no-empty-interface': 0,
    '@typescript-eslint/no-use-before-define': 0,
    '@typescript-eslint/prefer-enum-initializers': 2,
    '@typescript-eslint/explicit-module-boundary-types': 0,
    '@typescript-eslint/member-delimiter-style': [2, {
      multiline: {
        delimiter: 'none',
        requireLast: true,
      },
    }],
    '@typescript-eslint/ban-types': 0,
    '@typescript-eslint/no-shadow': 'off',
    'max-classes-per-file': [2,
      20,
    ],
    "@typescript-eslint/ban-ts-comment": [
      "error",
      {
        "ts-ignore": "allow-with-description",  // 允许带有描述的 ts-ignore
        "ts-nocheck": true,
        "ts-check": false,
        "minimumDescriptionLength": 3  // 最小描述长度为 3 个字符
      }
    ]
  },
}
