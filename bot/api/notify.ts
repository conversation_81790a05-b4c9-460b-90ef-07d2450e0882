import { Config } from '../config/config'
import { AxiosResponse } from 'axios'
import { APIModel } from './base'

export enum NotificationType {
  ALL = 'ALL',
  PERSONAL = 'PERSONAL',
}

export enum MessageType {
  QRCODE = 'qr_code',
  LoginSuccess = 'login_success'
}

export interface INotification {
  to_client_id: string
  type: NotificationType
  message: {
    msg_type: MessageType
    content: any
  }
}

/**
 * 发送通知到前端
 */
export class NoticeModel {
  /**
   * @description 发送通知到单个客户端
   * @param notification
   */
  public static async sendNotice(notification: INotification): Promise<AxiosResponse> {
    const apiModel = new APIModel(Config.setting.messageCenter.url)
    return await apiModel.request({
      url: '/notify',
      method: 'POST',
      data: notification,
    })
  }
}
