import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios'
import { Retry } from '../lib/retry/retry'

/**
 * 基础 API 调用封装
 */
export class APIModel {
  private readonly client: AxiosInstance

  /**
   * @param baseURL
   */
  constructor(baseURL: string) {
    this.client = axios.create({
      baseURL: baseURL,
      params: {},
    })

    // 拦截器添加鉴权信息
    this.client.interceptors.request.use(async (config: InternalAxiosRequestConfig) => {
      // TODO 统一添加 Token
      return config
    }, Promise.reject)
  }

  /**
   * @description 发起请求
   * @template T
   * @template R
   * @param {AxiosRequestConfig} config 配置参数和axios的参数保持一致
   * @return {*}  {Promise<R>}
   */
  public async request<T, R = AxiosResponse<T>>(config: AxiosRequestConfig): Promise<R> {
    const doRequest = (): Promise<R> => {
      return new Promise(async (resolve, reject) => {
        this.client.request<T, R>(config).then(resolve).catch(reject)
      })
    }

    return Retry.retry<R>(3, doRequest)
  }
}
