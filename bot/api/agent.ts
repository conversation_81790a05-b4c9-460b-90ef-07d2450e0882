import { APIModel } from './base'
import { Config } from '../config/config'

interface IAPICommonInterface<T> {
    code: number
    result?: T
    message?: string
    type: 'success' | 'error' | 'warning'
}

export interface IAgentConfig {
    goal: string
    profile: string
    saler_desc: string
    saler_name: string
    constraints: string
    personality: string
    company_info: string
    styleExample: string
}

export interface IAgentProduct {
    name: string
    description: string
}

export interface IAgentWorkflow {
    name: string
    goal: string
    example: string
}

export interface IAgentDetail {
    id: string
    config: IAgentConfig
    product: IAgentProduct[]
    workflow: IAgentWorkflow[]
}

export class AgentAPI {

  public static async getAgentById(agentId: string): Promise<IAgentDetail> {

    return {
      id: agentId,
      config: {
        goal: 'goal',
        profile: 'profile',
        saler_desc: 'saler_desc',
        saler_name: 'saler_name',
        constraints: 'constraints',
        personality: 'personality',
        company_info: 'company_info',
        styleExample: 'styleExample',
      },
      product: [],
      workflow: [],
    }
  }

  public static async setFriendShipConfirmed(agentId: string, wx_id: string): Promise<boolean> {
    const apiModel = new APIModel(Config.setting.apiCenter.agentBaseUrl)
    const response = await apiModel.request<IAPICommonInterface<boolean>>({
      url: `/agents/friendship/${wx_id}`,
      method: 'POST',
    })

    if (!response.data) {
      throw new Error('Get agent failed')
    }

    if (response.data.code !== 200) {
      if (response.data.message) {
        throw new Error(response.data.message)
      }

      throw new Error('Get agent failed')
    }

    if (!response.data.result) {
      throw new Error('Get agent failed: result is empty')
    }

    return response.data.result
  }


  public static async getFriendShipConfirmed(agentId: string, wx_id: string): Promise<boolean> {
    const apiModel = new APIModel(Config.setting.apiCenter.agentBaseUrl)
    const response = await apiModel.request<IAPICommonInterface<boolean>>({
      url: `/agents/friendship/${wx_id}`,
      method: 'GET',
    })

    if (!response.data) {
      throw new Error('Get agent failed')
    }

    if (response.data.code !== 200) {
      if (response.data.message) {
        throw new Error(response.data.message)
      }

      throw new Error('Get agent failed')
    }


    return Boolean(response.data.result)
  }

}