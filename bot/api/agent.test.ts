import { AgentAPI } from './agent'


describe('Test', function () {

  it('should pass', async () => {
    console.log(await AgentAPI.setFriendShipConfirmed('123', '1'))

    console.log(await AgentAPI.getFriendShipConfirmed('123', '1'))

    console.log(await AgentAPI.getFriendShipConfirmed('123', '2'))
  })

  it('1', async () => {
    console.log(await AgentAPI.getFriendShipConfirmed('123', '2'))
  }, 30000)
})