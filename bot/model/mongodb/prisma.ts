import { PrismaClient } from '@prisma/client'
export class PrismaMongoClient {
  private static instance: PrismaClient | undefined
  private static configInstance: PrismaClient | undefined

  public static getInstance(): PrismaClient {
    if (!PrismaMongoClient.instance) {
      PrismaMongoClient.instance = new PrismaClient()
    }
    return PrismaMongoClient.instance
  }

  public static getConfigInstance(): PrismaClient {
    if (!PrismaMongoClient.configInstance) {
      PrismaMongoClient.configInstance = new PrismaClient({
        datasources: {
          db: {
            url: 'mongodb://root:free1234$spirit!@dds-bp100ccf7c2e7f741791-pub.mongodb.rds.aliyuncs.com:3717/bot_config?authSource=admin',
          },
        },
      })
    }
    return PrismaMongoClient.configInstance
  }
}