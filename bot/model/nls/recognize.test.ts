
import * as path from 'path'
import { NlsSpeechRecognition } from './recognize'
import { SlsToken } from './token'
import { FileHelper } from '../../lib/file'
import { exec } from 'child_process'


describe('Test', function () {


  it('getToken', async () => {
    console.log(await SlsToken.getToken())
  }, 30000)

  it('', async () => {
    const reg = new NlsSpeechRecognition()

    console.log(await reg.recognize('/Users/<USER>/wechaty_bot/audio/edgvQtnfkngYZ5p4RVfjoF_16.wav'))
  }, 30000)


  it('should pass', async () => {

    const Nls = require('alibabacloud-nls')
    const fs = require('fs')
    const sleep = (waitTimeInMs: number) => new Promise((resolve) => setTimeout(resolve, waitTimeInMs))

    const URL = 'wss://nls-gateway.cn-shanghai.aliyuncs.com/ws/v1'
    const APPKEY = 'Qn8SelkUkHfaFb2t'
    const TOKEN = '71efd283de39456e8deaee1920089467'

    const audioStream = fs.createReadStream(path.join(__dirname, '1.amr'), {
      encoding: 'binary',
      highWaterMark: 1024,
    })
    const b1: Buffer[] = []

    audioStream.on('data', (chunk: string) => {
      const b = Buffer.from(chunk, 'binary')
      b1.push(b)
    })

    audioStream.on('close', async () => {

      const sr = new Nls.SpeechRecognition({
        url: URL,
        appkey: APPKEY,
        token: TOKEN,
      })

      sr.on('completed', (msg: any) => {
        console.log('Client recv completed:', msg)
      })


      sr.on('failed', (msg: any) => {
        console.log('Client recv failed:', msg)
      })

      try {
        await sr.start({
          'format': 'amr',
          'sample_rate': 16000,
          'enable_intermediate_result': false,
          'enable_punctuation_predition': true,
          'enable_inverse_text_normalization': true,
        }, true, 6000)
      } catch (error) {
        console.log('error on start:', error)
      }

      try {
        for (const b of b1) {
          if (!sr.sendAudio(b)) {
            throw new Error('send audio failed')
          }
          await sleep(20)
        }
      } catch (error) {
        console.log('sendAudio failed:', error)
      }

      try {
        console.log('close...')
        await sr.close()
      } catch (error) {
        console.log('error on close:', error)
      }


    })

  }, 10 * 1000)


  it('SilkToPcm', async () => {
    // const buffer = await FileHelper.readFileAsBuffer(path.join(__dirname, '1.sil'))
    //
    // const pcm = slk2pcm(buffer)
    //
    // await FileHelper.writeFile(path.join(__dirname, '1.pcm'), pcm)
    const wavPath = path.join(__dirname, '2.pcm')

    exec(`ffmpeg -loglevel panic -y -f s16le -ar 24000 -ac 1 -i ${path.join(__dirname, '1.pcm')} -ar 16000 ${wavPath}`)


    const reg = new NlsSpeechRecognition()

    console.log(await reg.recognize(wavPath))

  }, 60000)

  it('1', async () => {

  }, 30000)
})
