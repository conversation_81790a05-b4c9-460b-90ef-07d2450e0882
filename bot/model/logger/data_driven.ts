import pino, { DestinationStream, multistream } from 'pino'
import pretty from 'pino-pretty'
import { DateHelper } from '../../lib/date/date'
import path from 'path'
import fs from 'fs'
import { ChatState } from '../../service/baoshu/storage/chat_state'


export enum IEventType {
    ZiLiaoNotFound = '资料未找到',
    FailedToJoinGroup = '加群失败',
}


/**
 * 埋点记录
 */
export class EventTracker {
  public logger: pino.Logger
  public static chatState = new ChatState<EventTracker>()

  constructor(chat_id: string) {
    // const folder = path.join('event', DateHelper.getFormattedDate(new Date(), false))
    // if (!fs.existsSync(folder)) {
    //   // 创建目录
    //   fs.mkdirSync(folder)
    // }
    //
    // const file = path.join(folder, `${chat_id}.log`)

    // // 创建一个写入文件的流
    // const fileStream: DestinationStream = pino.destination(file)

    const streams = [
      // { stream: fileStream },
      {
        stream: pretty({
          colorize: true, // 如果你想要颜色化
          translateTime: 'SYS:mm-dd HH:MM:ss'
        })
      }
    ]

    this.logger = pino({ base: null }, multistream(streams))
  }

  public info(message: string, ...args: unknown[]) {
    this.logger.info(message, ...args)
  }

  public warn(message: string, ...args: unknown[]) {
    this.logger.warn(message, ...args)
  }

  public error(message: string, ...args: unknown[]) {
    this.logger.error(message, ...args)
  }


  public static track(chat_id: string, event: string, meta?: Object) {
    if (!meta) {
      meta = {}
    }

    let logger: EventTracker

    if (EventTracker.chatState.has(chat_id)) {
      logger = EventTracker.chatState.get(chat_id) as EventTracker
    } else {
      logger = new EventTracker(chat_id)
      EventTracker.chatState.set(chat_id, logger)
    }

    // 这里把 message, ...args 处理为跟 console.log 一致
    logger.logger.child({ chat_id, event, ...meta }).info('')
  }

}
