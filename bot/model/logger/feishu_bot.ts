// Create a custom writable stream for Feishu webhook
import stream from 'stream'
import axios from 'axios'
import { DateHelper } from '../../lib/date/date'
import { PrismaMongoClient } from '../mongodb/prisma'



export class MongoDBStream extends stream.Writable {
  async _write(chunk, encoding, callback) {
    const log = JSON.parse(chunk.toString())
    const levelLabels = {
      10: 'trace',
      20: 'debug',
      30: 'info',
      40: 'warn',
      50: 'error',
      60: 'fatal',
    }

    const level = levelLabels[log.level] || 'UNKNOWN'
    const timestamp = new Date(log.time)

    try {
      await PrismaMongoClient.getInstance().log_store.create({
        data: {
          level,
          timestamp,
          chat_id: log.chat_id,
          msg: log.msg
        }
      })

      callback()
    } catch (e) {
      console.error(e)
      callback()
    }
  }
}