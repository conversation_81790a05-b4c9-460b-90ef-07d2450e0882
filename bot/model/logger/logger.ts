import pino, { multistream } from 'pino'
import pretty from 'pino-pretty'
import { DateHelper } from '../../lib/date/date'
import path from 'path'
import fs from 'fs'
import { MongoDBStream } from './feishu_bot'

interface LoggerOptions {
    [key: string]: any
}

/**
 * 日志输出尽量使用这个类，避免直接使用 console.log
 */
class logger {
  private static instance?: pino.Logger

  private constructor(options: LoggerOptions) {
  }

  public static getInstance() {
    if (!this.instance) {
      // const folder = path.join('logs', DateHelper.getFormattedDate(new Date(), false))
      // if (!fs.existsSync(folder)) {
      //   // 创建目录
      //   fs.mkdirSync(folder)
      // }

      // const file = path.join(folder, `${DateHelper.getFormattedDate(new Date(), false)}.log`)

      // 创建一个写入文件的流
      // const fileStream = fs.createWriteStream(file, { flags: 'a' })

      const streams = [
        {
          level: 'trace',
          stream: pretty({
            colorize: true, // 如果你想要颜色化
            translateTime: 'SYS:mm-dd HH:MM:ss',
            minimumLevel: 'trace'
          })
        },
        // {
        //   level: 'trace',
        //   stream: fileStream
        // },
        {
          level: 'trace',
          stream: new MongoDBStream(),
        }
      ]

      this.instance = pino({ level: 'trace' }, multistream(streams))
    }

    return this.instance
  }



  private static _log(level: string, ...args: any[]) {
    const instance = this.getInstance()
    const fn = instance[level].bind(instance)

    if (typeof args [0] === 'string') {
      fn(args.map((arg) => {
        if (typeof arg === 'object') {
          return JSON.stringify(arg)
        }

        return String(arg)
      }).join(' '))
    } else {
      const obj = args [0]
      const msg = args.slice(1).map((arg) => {
        if (typeof arg === 'object') {
          return JSON.stringify(arg)
        }

        return String(arg)
      }).join(' ')
      fn (obj, msg)
    }
  }
  public static trace (obj: unknown, msg?: string, ...args: any []): void
  public static trace (msg: string, ...args: any []): void
  public static trace(...args: any[]) {
    this._log('trace', ...args)
  }

  public static debug (obj: unknown, msg?: string, ...args: any []): void
  public static debug (msg: string, ...args: any []): void
  public static debug(...args: any[]) {
    this._log('debug', ...args)
  }

  // 注意：这些是函数重载的签名
  public static log (obj: unknown, msg?: string, ...args: any []): void
  public static log (msg: string, ...args: any []): void
  public static log(...args: any[]) {
    this._log('info', ...args)
  }

  public static warn (obj: unknown, msg?: string, ...args: any []): void
  public static warn (msg: string, ...args: any []): void
  public static warn(...args: any[]) {
    this._log('warn', ...args)
  }


  public static error (obj: unknown, msg?: string, ...args: any []): void
  public static error (msg: string, ...args: any []): void
  public static error(...args: any[]) {
    this._log('error', args)
  }
}

export default logger