import { EventTracker } from './data_driven'
import logger from './logger'
import pino from 'pino'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    const chatId = '12345'

    try {
      JSON.parse('test')
    } catch (error: any) {
      logger.error(chatId, error.stack)
    }
    logger.log(undefined, 'test message')
  })

  it('data driven', async () => {
    EventTracker.track('id1', 'ZiLiaoNotFound', { url: 'https://www.google.com' })
  }, 30000)


  it('test', async () => {
    logger.log({ chat_id: 'hi' }, '呵呵')
  }, 60000)


  it('logger', async () => {
    logger.debug('fk u', 'hi')
    logger.debug({ fk: 'u' }, 'fk u')
    logger.log({ fk: 'u' }, 'fk u')
    logger.log('fk u')
  }, 30000)

  it('pino', async () => {
    pino().info('hi', 'fk u')
  }, 30000)

  it('1', async () => {
    function removeEscapedCharacters(str) {
      // 匹配转义字符
      const escapedCharRegex = /\\[btnfr"'\\]/g
      return str.replace(escapedCharRegex, '')
    }

    function removeAnsiColors(str) {
      // 匹配 ANSI 颜色控制字符
      const ansiColorRegex = /[\u001b\u009b][[()#;?]*(?:[0-9]{1,4}(?:;[0-9]{0,4})*)?[0-9A-ORZcf-nq-uy=><]/g
      return str.replace(ansiColorRegex, '')
    }

    function cleanLogMessage(str) {
      // 去除转义字符
      str = removeEscapedCharacters(str)
      // 去除 ANSI 颜色控制字符
      str = removeAnsiColors(str)
      return str
    }

    // 示例字符串
    const inputStr = 'enter LLMNode {\n  \"state\": {\n    \"chat_id\": \"7881300846030208_1688854546332791\",\n    \"user_id\": \"7881300846030208\",\n    \"userMessage\": \"我大专，咋搞\"\n  },\n  \"dynamicPrompt\": \"询问客户当前遇到的问题和想达成目标是什么。例如：\\\"有什么诉求？咱们读完书之后有什么规划？\\\"\"\n}'

    // 去除转义字符和 ANSI 颜色控制字符
    const cleanedStr = cleanLogMessage(inputStr)
    console.log(cleanedStr)
  }, 60000)
})