import { ABTest } from './a_b_test'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    for (let i = 1; i <= 25; i++) {
      console.log(`User ${i} is in group: ${ABTest.getStrategyForUser(String(i), 2)}`)
    }

    for (let i = 1; i <= 25; i++) {
      console.log(`User ${i} is in group: ${ABTest.getStrategyForUser(String(i), 2)}`)
    }
  })


  it('global strategy', async () => {
    for (let i = 1; i <= 25; i++) {
      console.log(`User ${i} is in group: ${await ABTest.getGlobalStrategyForUser(String(i), 3)}`)
    }

    for (let i = 1; i <= 25; i++) {
      console.log(`User ${i} is in group: ${await ABTest.getGlobalStrategyForUser(String(i), 3)}`)
    }
  }, 30000)
})