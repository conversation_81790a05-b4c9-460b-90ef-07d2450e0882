import { RedisDB } from '../redis/redis'
import { RedisCacheDB } from '../redis/redis_cache'
import { ObjectUtil } from '../../lib/object'

export class ABTest {
  private static userCount: number = 0
  private static usersInGroups: Map<string, number> = new Map()

  static getStrategyForUser(user_id: string, numGroups: number): number {
    // 如果该 user_id 已被分配到某个策略，直接返回对应策略
    if (this.usersInGroups.has(user_id)) {
      return this.usersInGroups.get(user_id)!
    }

    // 根据当前用户数计算分配的组别
    const group = this.userCount % numGroups
    this.usersInGroups.set(user_id, group)
    this.userCount++

    return group
  }

  static async getGlobalStrategyForUser(user_id: string, numGroups: number): Promise<number> {
    const userCountKey: string = 'baoshuUserCount' // Redis中的用户计数键
    const abTestStrategyKey: string = `baoshuABTest_${user_id}` // Redis中的 AB Test 策略键

    const userStrategy = new RedisCacheDB(abTestStrategyKey)

    // 1. 首先检查该用户是否已经分配了策略
    const existingGroup = await userStrategy.get()
    if (existingGroup !== null && typeof existingGroup === 'number') {
      return existingGroup  // 如果已经有策略，直接返回
    }

    // 2. 如果没有分配策略，计算策略
    const userCountCache = new RedisCacheDB(userCountKey)
    let userCount = await userCountCache.get()
    if (userCount === null) {
      userCount = 0
    }

    // 根据用户计数来分配组别
    const group = userCount % numGroups

    // 3. 将用户策略保存到 Redis，并设置 24 小时有效期
    await userStrategy.set(group, 24 * 60 * 60, 'EX') // 设置有效期为 24 小时（86400 秒）

    // 4. 更新用户计数
    await userCountCache.set(userCount + 1)

    return group
  }
}