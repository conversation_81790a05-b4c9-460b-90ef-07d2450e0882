import { RedisDB } from './redis'
import logger from '../logger/logger'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    console.log(JSON.stringify(await RedisDB.getInstance().set('testKey', 'testValue'), null, 4))

    console.log(JSON.stringify(await RedisDB.getInstance().get('testKey'), null, 4))

    console.log(JSON.stringify(await RedisDB.getInstance().del('testKey')))

    console.log(JSON.stringify(await RedisDB.getInstance().get('testKey'), null, 4))
  }, 60000)
})