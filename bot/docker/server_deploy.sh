#!/bin/bash

set -e

# 变量
REMOTE_REPO="crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/freespirit/baoshu"
TAG_LATEST="latest"

DOCKER_COMPOSE_FILE="docker-compose.yaml"
# Log file in the same directory as the script
LOG_FILE="$(dirname "$0")/deploy_script.log"

# 如果有传入参数，则使用传入的服务名称列表；否则部署所有服务
if [ -n "$1" ]; then
    SERVICES_TO_DEPLOY=("$@")
else
    echo "No services specified. Exiting."
    exit 1
fi

log() {
    # Prepend timestamp to the log message and append to both console and log file
    echo "$(date '+%Y-%m-%d %H:%M:%S') : $1" | tee -a "$LOG_FILE"
}

# 函数：检查命令是否存在
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 确定使用哪个 docker compose 命令
if command_exists docker-compose; then
    DOCKER_COMPOSE_CMD="docker-compose"
elif command_exists docker compose; then
    # 注意空格 - 这是较新的插件语法
    DOCKER_COMPOSE_CMD="docker compose"
else
    log "错误：未找到 'docker-compose' 或 'docker compose' 命令。请安装 Docker Compose。" # 中文日志
    exit 1
fi
log "使用 Docker Compose 命令: $DOCKER_COMPOSE_CMD" # 中文日志

log "===== [Bot Server] 开始部署流程 ====="

# 循环遍历每个需要部署的服务
for CLIENT_SERVICE in "${SERVICES_TO_DEPLOY[@]}"; do
    log "开始部署服务: $CLIENT_SERVICE"

    # 1. 拉取镜像（从远程仓库拉取最新）
    log "拉取镜像: $REMOTE_REPO:$TAG_LATEST for service $CLIENT_SERVICE"
    $DOCKER_COMPOSE_CMD -f "$DOCKER_COMPOSE_FILE" pull "$CLIENT_SERVICE"

    # 2. 重新创建并启动容器
    log "启动服务: $CLIENT_SERVICE"
    # Use --force-recreate to ensure the new image is used
    $DOCKER_COMPOSE_CMD -f "$DOCKER_COMPOSE_FILE" up -d --no-deps --force-recreate "$CLIENT_SERVICE"

    # 3. 等待容器启动
    log "容器已启动，等待 5 秒后进行健康检查..."
    sleep 5

    # 4. 容器健康检查
    log "开始健康检查 for $CLIENT_SERVICE..."
    CONTAINER_ID=$($DOCKER_COMPOSE_CMD -f "$DOCKER_COMPOSE_FILE" ps -q "$CLIENT_SERVICE")

    if [ -z "$CONTAINER_ID" ]; then
        log "错误：无法获取容器 $CLIENT_SERVICE 的 ID，健康检查失败！"
        log "$CLIENT_SERVICE 部署失败，退出部署流程。"
        exit 1
    fi

    STATUS=$(docker inspect -f '{{.State.Status}}' "$CONTAINER_ID" 2>/dev/null)
    log "容器 $CLIENT_SERVICE (ID: $CONTAINER_ID) 当前状态为: $STATUS"

    if [ "$STATUS" != "running" ]; then
        log "错误：容器 $CLIENT_SERVICE 状态为 $STATUS（非 running）"
        log "查看容器日志以获取详细信息:"
        docker logs "$CONTAINER_ID" --tail 50
        log "$CLIENT_SERVICE 部署失败，退出部署流程。"
        exit 1
    else
        EXPOSED_PORT=$(docker port "$CLIENT_SERVICE" | grep -o -m 1 '[0-9]\{4\}' | uniq)

        # 假设容器在 docker-compose.yaml 中将内部端口 3000 映射到宿主机
        HOST_PORT=$($DOCKER_COMPOSE_CMD -f "$DOCKER_COMPOSE_FILE" port "$CLIENT_SERVICE" $EXPOSED_PORT | cut -d: -f2)

        if [ -z "$HOST_PORT" ]; then
            log "警告: 未找到服务 $CLIENT_SERVICE 的端口 的映射。跳过HTTP健康检查。"
        else
            log "对 http://localhost:$HOST_PORT 进行健康检查..."
            retry_count=0
            max_retries=5
            retry_delay=5

            while [[ $retry_count -lt $max_retries ]]; do
                # Use -s for silent, -f for fail fast, -o /dev/null to discard output
                if curl -sf -o /dev/null "http://localhost:$HOST_PORT"; then
                    log "健康检查通过：容器在宿主机的 $HOST_PORT 端口可访问。"
                    break
                else
                    retry_count=$((retry_count + 1))
                    if [[ $retry_count -lt $max_retries ]]; then
                        log "健康检查失败 (尝试 $retry_count/$max_retries)，在 $retry_delay 秒后重试..."
                        sleep $retry_delay
                    else
                        log "错误：健康检查在 $max_retries 次尝试后仍然失败。"
                        log "$CLIENT_SERVICE 部署失败，退出部署流程。"
                        exit 1
                    fi
                fi
            done
        fi
    fi

    log "$CLIENT_SERVICE 部署成功！"
    log "=========================================="
done

# 清理旧的、未使用的镜像
log "清理无用的 Docker 镜像..."
docker image prune -f >> "$LOG_FILE" 2>&1

log "所有指定的服务部署流程完成。"