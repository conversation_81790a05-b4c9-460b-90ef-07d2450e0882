export class DateHelper {
  public static getFormattedDate (date: Date, includeTime: boolean = true): string {
    const year = date.getFullYear ()
    const month = date.getMonth () + 1
    const day = date.getDate ()
    const hour = date.getHours ()
    const minute = date.getMinutes ()
    const second = date.getSeconds ()
    const dayOfWeek = date.getDay ()

    // 如果是一位数字，则在前面补 0
    const monthStr = month < 10 ? `0${month}` : month
    const dayStr = day < 10 ? `0${day}` : day
    const hourStr = hour < 10 ? `0${hour}` : hour
    const minuteStr = minute < 10 ? `0${minute}` : minute
    const secondStr = second < 10 ? `0${second}` : second

    // 定义一个表示周几的字符串数组
    const daysOfWeek = ['日', '一', '二', '三', '四', '五', '六']
    const dayOfWeekStr = `周${daysOfWeek[dayOfWeek]}`

    let timeStr = `${year}-${monthStr}-${dayStr} ${dayOfWeekStr}`
    if (includeTime)
      timeStr += ` ${hourStr}:${minuteStr}:${secondStr}`

    return timeStr
  }
}