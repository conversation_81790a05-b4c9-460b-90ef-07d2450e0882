function dotProduct(vectorA: number[], vectorB: number[]): number {
  return vectorA.map((value, index) => value * vectorB[index])
    .reduce((sum, current) => sum + current, 0)
}

function magnitude(vector: number[]): number {
  return Math.sqrt(vector.map((value) => value ** 2)
    .reduce((sum, current) => sum + current, 0))
}

export function cosineSimilarity(embeddingA: number[], embeddingB: number[]): number {
  if (embeddingA.length !== embeddingB.length) {
    throw new Error('Embeddings must have the same dimension.')
  }

  const dot = dotProduct(embeddingA, embeddingB)
  const magA = magnitude(embeddingA)
  const magB = magnitude(embeddingB)

  if (magA === 0 || magB === 0) {
    throw new Error('One of the vectors is zero, cannot calculate similarity.')
  }

  return dot / (magA * magB)
}


export function calculateJaccardSimilarity(textA: string, textB: string): number {
  // 计算的时候，移除掉空格符号和换行
  // 移除掉结尾的标签， -深度学习/机器学习/人工智能
  const pattern = /-[^\/]+(\/[^\/]+)*$/
  // 移除匹配到的尾部模式
  // 移除掉特殊符号，如：@#￥%……&*（）——+【】《》“”‘’；：、。.
  textA = textA.replace(/\s+/g, '').replace(pattern, '').replace(/[@#￥%……&*（）——+“”‘’；：、。.]/g, '').replace('（使用前请询问）', '')
  textB = textB.replace(/\s+/g, '').replace(pattern, '').replace(/[@#￥%……&*（）——+“”‘’；：、。.]/g, '').replace('（使用前请询问）', '')

  const wordsA = new Set<string>(textA.split(''))
  const wordsB = new Set<string>(textB.split(''))

  const intersection = new Set([...wordsA].filter((word) => wordsB.has(word)))
  const union = new Set([...wordsA, ...wordsB])

  return intersection.size / union.size
}

export function findMostSimilarText(target: string, texts: string[], preprocess?: (text: string) => string): {key: string; similarity: number} {
  let maxSimilarity = 0
  let mostSimilarText = ''

  for (const text of texts) {
    const textA = preprocess ? preprocess(text) : text
    const textB = preprocess ? preprocess(target) : target

    if (!textA || !textB) {
      continue
    }

    const similarity = calculateJaccardSimilarity(textA, textB)

    if (similarity > maxSimilarity) {
      maxSimilarity = similarity
      mostSimilarText = text
    }
  }

  return { key: mostSimilarText, similarity: maxSimilarity }
}

export function findFirstIn(target: string, texts: string[], preprocess?: (text: string) => string) {
  for (const text of texts) {
    const textA = preprocess ? preprocess(text) : text
    const textB = preprocess ? preprocess(target) : target

    if (!textA || !textB) {
      continue
    }

    if (textA.includes(textB)) {
      return text
    }
  }

  return ''
}