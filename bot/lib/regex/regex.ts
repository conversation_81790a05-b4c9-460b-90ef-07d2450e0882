export enum CommonRegexType {
    URL = 'url',
}



const createEmojiRegex = () => {
  const r = String.raw
  const seq = r`(?:\p{Emoji}\uFE0F\u20E3?|\p{Emoji_Modifier_Base}\p{Emoji_Modifier}?|\p{Emoji_Presentation})`
  const sTags = r`\u{E0061}-\u{E007A}`
  return new RegExp(r`[\u{1F1E6}-\u{1F1FF}]{2}|\u{1F3F4}[${sTags}]{2}[\u{E0030}-\u{E0039}${sTags}]{1,3}\u{E007F}|${seq}(?:\u200D${seq})*`, 'gu')
}

export class RegexHelper {
  public static readonly URL_REGEX = /(https?:\/\/)(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()!@:%_\+.~#?&\/\/=]*)/
  public static readonly NUMBER_REGEX = /\d+/
  public static readonly EMOJI_REGEX = /(\u00a9|\u00ae|[\u2000-\u3300]|\ud83c[\ud000-\udfff]|\ud83d[\ud000-\udfff]|\ud83e[\ud000-\udfff])/
  public static NOT_COMMON_CHAR = /[^\u4e00-\u9fa5a-zA-Z0-9，。？！：；、“”‘’（）《》【】{}·~`!@#$%^&*()_\-+=$begin:math:display$$end:math:display$|\\:;"'<>,.?/ ]/

  public static readonly commonRegexMap = {
    [CommonRegexType.URL]: RegexHelper.URL_REGEX,
  }


  public static toGlobalRegex(regex: RegExp): RegExp {
    return new RegExp(regex.source, `${regex.flags.replace('g', '')}g`)
  }

  public static strIncludeRegex(str: string, regex: CommonRegexType): boolean {
    if (regex in this.commonRegexMap) {
      return this.commonRegexMap[regex].test(str)
    }

    return false
  }

  public static extractNumber(str: any) {
    if (typeof str !== 'string') {
      return null
    }

    const match = str.match(RegexHelper.NUMBER_REGEX)
    if (match) {
      return parseInt(match[0], 10)
    }
    return null
  }

  static extractList(llmRes: string): string | null {
    const regex = /\[(.*?)\]/s
    const match = regex.exec(llmRes)

    if (match && match[0]) {
      return match[0].trim()
    }

    return null
  }

  static sayChinese(sentence: string): string {
    const totalLength = sentence.length
    if (totalLength < 50) {
      return sentence
    }

    // 匹配中文字符的正则表达式
    const chineseCharRegex = /[\u4e00-\u9fa5]/
    let chineseCount = 0

    for (const char of sentence) {
      if (chineseCharRegex.test(char)) {
        chineseCount++
      }
    }

    const chineseRatio = chineseCount / totalLength

    if (chineseRatio > 0.4) {
      return sentence
    } else {
      return ''
    }
  }
}