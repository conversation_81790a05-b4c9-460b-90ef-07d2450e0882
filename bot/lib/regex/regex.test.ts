import { RegexHelper, CommonRegexType } from './regex'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    const currentSentence = 'https://github.com/geongeorge/i-hate-regex'
    console.log(RegexHelper.strIncludeRegex(currentSentence, CommonRegexType.URL))
  })

  it('extract list', async () => {
    console.log(RegexHelper.extractList(`fk
    [1, 2, 3]u`))
  }, 60000)

  it('s', async () => {
    let s = 'wastex🦈 Selena🌹'
    s = s.replaceAll(RegexHelper.toGlobalRegex(RegexHelper.EMOJI_REGEX), '')
    console.log(s)
  }, 60000)
})