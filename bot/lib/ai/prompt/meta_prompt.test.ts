import { MetaPrompt } from './meta_prompt'
import { ChatOpenAI } from '@langchain/openai'
import { UserEducationGroup } from '../../../service/baoshu/components/flow/nodes/intentionCalculate'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    console.log(await MetaPrompt.getPrompt(`给你一个留学场景的对话，帮我总结出用户侧的问题，包括用户情况（注意保留全用户的情况），用户需求，用户目标。和留学顾问给出的建议。以下方的格式输出：
用户侧需求：xxx
留学顾问建议：xxx
`))
  }, 1E8)

  it('questions fix', async () => {
    console.log(await MetaPrompt.getPrompt(`给你一个留学咨询场景的对话和一组通过对话抽取出的用户问题，请帮我参考上下文对用户的问题进行修复（补全主语和缺失的上下文信息），使其更加完整和准确，在不提供对话内容上下文时能成为一个完整独立的问题。
比如在某个用户询问韩国留学的对话中，用户提问：费用多少？ 应该修复为：韩国留学的费用是多少？

输出格式为：
<questions>
1. {fixed user question 1}
2. {fixed user question 2}
</questions>
`))
  }, 1E8)

  it('claude', async () => {
    const claude =  new ChatOpenAI({
      temperature: 0,
      openAIApiKey: 'sk-Uut4MPK3AIxiGq1L8d7e9f9005B14e5d971a21A390Cb3aC6',
      configuration: {
        baseURL: 'https://api.kksj.org/v1'
      },
      modelName: 'claude-3-opus-20240229',
    })

    console.log(await claude.predict('你好'))
  }, 1E8)


  it('l to es search', async () => {
    console.log(await MetaPrompt.getPrompt(`你需要把用户的咨询留学的需求转为 ElasticSearch 预查询 JSON，以便于后续在数据库中检索相关信息。参考下面给出的索引字段描述和查询示例：
        留学的索引的字段描述如下：
        {
            properties: {
              project: { type: 'text' }, // "项目"
              description: { type: 'text' }, // "项目介绍"
              requirementType: { type: 'text' }, // "需求", enum 类型, [移民, 低龄规划, 国内本科, 世界名校, 美国, 专业老师咨询, 拿文凭, 专升硕]
              budget: { type: 'float' }, // "预算", 单位为万
              budget_is_per_year: { type: 'boolean' }, // "预算是否为每年", 如无法判断，则默认为总预算，为 false
              gradeRequirement: { type: 'float' }, // "成绩要求"
              applicationStage: { type: 'text' }, // "申请阶段", enum 类型, 目标申请的学历，[高中, 本科, 硕士, 博士]
              languageRequirement: { type: 'text' } // "语言要求"
              minimumEducationRequirement: { type: 'text' }, // "申请要求最低学历", enum 类型, [初中, 高中, 本科, 研究生, 博士]
              country: { type: 'text' }, // "国家", 项目所属国家, 比如：美国，英国，中国，加拿大...
              isDomestic: { type: 'boolean' } // "是否在国内（中国）上课", 布尔型
            }
        }
      
      比如：
      用户：有没有预算低于100万，拿文凭的留学项目？
      转为 ElasticSearch 预查询 JSON 为：
        { "budgetLowerBound": 100, "requirementType": "拿文凭"}
         `))
  }, 1E8)

  it('metaPrompt', async () => {
    console.log(await MetaPrompt.getPrompt('帮我对用户需求进行分类，输出最后的分类项，分类及描述如下：【1】寻求情绪价值：这类用户其实没有有关留学的具体问题，找寻解法。只是出于喜爱暴叔，猎奇。把暴叔作为树洞寻求焦虑宣泄/情感宣泄，并非有想解决问题。；\n' +
        '\n' +
        '【2】来寻求升学/留学规划问题，有的是有比较宽泛需求还没有对应的方向，例如：大专生以后不想当社会的牛马，有什么出路；有的目标明确只是缺乏路径：我想出国提升学历，我后续可以怎么规划达成目标。总体而言来询问暴叔是希望理清思路，做好未来的规划。\n' +
        '\n' +
        '【3】需求和方向都比较明确，但是在具体的路径上有一些单点问题：例如想去英国，ucl比较好还是爱丁堡大学好。德国的双元制具体怎么申请呢？'))
  }, 1E8)


  it('metaPrompt1', async () => {
    console.log(await MetaPrompt.getPrompt('帮我对用户需求进行分类，输出最后的分类项，分类及描述如下：【1】寻求情绪价值：这类用户其实没有有关留学的具体问题，找寻解法。只是出于喜爱暴叔，猎奇。把暴叔作为树洞寻求焦虑宣泄/情感宣泄，并非有想解决问题。；\n' +
        '\n' +
        '【2】来寻求升学/留学规划问题，有的是有比较宽泛需求还没有对应的方向，例如：大专生以后不想当社会的牛马，有什么出路；有的目标明确只是缺乏路径：我想出国提升学历，我后续可以怎么规划达成目标。总体而言来询问暴叔是希望理清思路，做好未来的规划。\n' +
        '\n' +
        '【3】需求和方向都比较明确，但是在具体的路径上有一些单点问题：例如想去英国，ucl比较好还是爱丁堡大学好。德国的双元制具体怎么申请呢？'))
  }, 1E8)

  it('meta3', async () => {
    console.log(await MetaPrompt.getPrompt('你现在是在一个留学售前顾问的场景，售前将用户转交了给了专业方向的顾问，并询问用户是否愿意进群。你需要根据用户的回答，来判断用户是否愿意进群。返回 true 或者 false。'))
  }, 30000)

  it('meata4', async () => {
    console.log(await MetaPrompt.getPrompt(`你是一个专业的留学顾问，帮我根据提供的用户信息和检索出的项目列表，挑出一个你认为匹配度最高的项目。
   用户信息例子：
   {
    "budget": 30,
    "currentLevelOfEducation": "本科",
    "currentEducationBackground": "二本大三在读",
    "applicationStage": "硕士",
    "budget_is_per_year": false
   }
   
   项目列表例子：
   {
                "project": "国内中外合办硕士-非全",
                "applicationStage": "硕士",
                "minimumEducationRequirement": "本科",
                "requirementType": "普娃冲名校",
                "budgetLowerBound": 20,
                "budgetUpperBound": 32,
                "annualBudgetLowerBound": 10,
                "annualBudgetUpperBound": 15,
                "userStory": "因为申请要求比较简单，非常适合推荐考研失利后，没有太多时间背景语言成绩和背景的学生",
                "description": "2年26W，华东理工大学&澳洲堪培拉大学；\\n18个月/32.8W，中国社科院大学&美国杜兰；\\n2年20W，北京城市&华威；\\n2年30W，云南财经大学；\\n2年30W，首都师范大学中外合办；",
                "isDomestic": true,
                "academicYears": "1.5-2",
                "gradeRequirement": "",
                "languageRequirement": "",
                "country": "中国"
  },
  {
                "project": "中外合办硕士",
                "applicationStage": "硕士",
                "minimumEducationRequirement": "本科",
                "requirementType": "差生拿文凭",
                "budgetLowerBound": 20,
                "budgetUpperBound": 30,
                "annualBudgetLowerBound": 10,
                "annualBudgetUpperBound": 15,
                "userStory": "本科想在国内快速拿到硕士学历",
                "description": "1.西交利物浦，1.5年30W\\n2.宁波诺丁汉，1年15W\\n3.北师大浸会，1年20W\\n4.温州肯恩大学，2年20W\\n5.港中文深圳校区，1年20W，2年35W",
                "isDomestic": true,
                "academicYears": "",
                "gradeRequirement": "",
                "languageRequirement": "",
                "country": ""
            },
            {
                "project": "海外升学—常规申请根据国家情况申请",
                "applicationStage": "硕士",
                "minimumEducationRequirement": "本科",
                "requirementType": "差生拿文凭",
                "budgetLowerBound": 30,
                "budgetUpperBound": 300,
                "annualBudgetLowerBound": 10,
                "annualBudgetUpperBound": 50,
                "userStory": "比较适合提前1年以上规划，准备好语言成绩和平时成绩，背景等部分",
                "description": "",
                "isDomestic": false,
                "academicYears": "",
                "gradeRequirement": "",
                "languageRequirement": "",
                "country": ""
            },
            {
                "project": "海外升学—常规申请根据国家情况申请",
                "applicationStage": "硕士",
                "minimumEducationRequirement": "本科",
                "requirementType": "学霸登顶",
                "budgetLowerBound": 30,
                "budgetUpperBound": 300,
                "annualBudgetLowerBound": 10,
                "annualBudgetUpperBound": 50,
                "userStory": "比较适合提前1年以上规划，准备好语言成绩和平时成绩，背景等部分",
                "description": "国内985/211类院校水平学生",
                "isDomestic": false,
                "academicYears": "",
                "gradeRequirement": "",
                "languageRequirement": "",
                "country": ""
            },
            {
                "project": "中外合办非全日制硕士",
                "applicationStage": "硕士",
                "minimumEducationRequirement": "本科",
                "requirementType": "差生拿文凭",
                "budgetLowerBound": 15,
                "budgetUpperBound": 35,
                "annualBudgetLowerBound": 15,
                "annualBudgetUpperBound": 35,
                "userStory": "本科想在国内快速拿到硕士学历，可以接受非全日制",
                "description": "非全日制硕士\\n1.华东理工大学&澳洲堪培拉大学，2年26W\\n2.中国社科院大学&美国杜兰，18个月/32.8W\\n3.北京城市&华威，2年20W\\n4.云南财经大学，2年30W\\n5.首都师范大学中外合办，2年30W",
                "isDomestic": true,
                "academicYears": "",
                "gradeRequirement": "",
                "languageRequirement": "",
                "country": ""
            }
        ]
    }]
   `))
  }, 30000)

  it('category', async () => {
    console.log(await MetaPrompt.getPrompt(`帮我对留学场景的用户进行分类。分类有如下几种:
    【学霸登顶】是指能上中国985和211院校水平学生想冲击更好的学校；
    【普娃冲名校】是指上国内双非和二本院校的普通成绩水平孩子想上一个一流学校的诉求；
    【差生拿文凭】是指考不上普通大学，普通高中的学生想努力那个学历文凭的需求
    【留学移民】是指学生通过留学的方式进行移民
    【低龄规划】是指在小孩子低龄（初中(包含)之前）的节点就提前为孩子规划好未来，例如从小学阶段就开始长线规划后续大学的就读路径等
    
    返回分类后的结果  
    `))
  }, 1E8)

  it('category1', async () => {
    console.log(await MetaPrompt.getPrompt(`我会给你一个用户和留学顾问的聊天记录，帮我根据用户所提出的需求，根据我给你的项目列表，选择出四个最匹配的项目编号，并以优先级顺序返回。
    项目列表示例如下：
    ['1. 美国', '2. 英国', '3. 韩国', '4. 加拿大', '5. 法国']
    
    返回示例
    ['2', '3', '1', '5']
    `))

  }, 1E8)

  it('category2', async () => {
    console.log(await MetaPrompt.getPrompt(`我会给你一个用户和留学顾问的聊天记录，帮我对用户的意图进行分类：
    1. 升学规划
    2. 寻求合作
    3. 售后投诉
    
  一步一步的思考，返回分类后的序号
    `))

  }, 1E8)


  it('schools to countries', async () => {
    console.log(await MetaPrompt.getPrompt(`给你一个学校列表，帮我返回学校列表，对应的国家列表。
For example:
["滑铁卢大学", "斯坦福"]

Result:
["加拿大", "美国"]`))

  }, 1E8)

  it('is query mid', async () => {
    console.log(await MetaPrompt.getPrompt(`给你一个留学顾问和客户的聊天中客户回复的话，帮我分析下当前是否是适合拉用户进群跟专业老师沟通。拉用户进群的判断为：
1. 客户想要询问院校以及专业等规划性细节问题以及具体怎么准备申请
2. 用户主动询问是否有中介服务，或是否有帮忙申请的服务

满足以上任一条件即可，返回 true，否则返回 false`))
  }, 60000)

  it('is fill formed', async () => {
    console.log(await MetaPrompt.getPrompt(`根据用户提供的信息，判断是否填写了下方这个表：
"姓名：
电话：
年级：
高考总分：
英语成绩：
（或）日语成绩：
所在城市：
想【国际本科/中外合办】还是【海外留学】：
本科总预算（学费+生活费）：
意向专业（文商科/理工科/艺术类）："

注意只要填了表就算，不用填满`))
  }, 60000)


  it('is fill formed s', async () => {
    console.log(await MetaPrompt.getPrompt(`你现在正在帮助用户填写一个采集留学信息的表单：
注意下方的填写规则：
注意语言成绩上；高中学习的是英语都填写英语成绩即可，是日语生就填写日语成绩即可。
如果客户对预算问题没有概念，可以普及下出国预算，例如：
"一般主流国家，美国每年60-80w, 英国要准备每年40-50w，新加坡每年25-35w，国际本科平均每年20w左右，总共50-80w，性价比高的韩国/马来西亚，每年10-15w差不多就可以的。 看看大概能卡在哪个档呢，出去预算还是一个很硬的门槛的。”


如果客户不回复表中字段或者询问字段内容，而是说其他问题，可以再引导一次客户填写。
例如：“高考季叔实在非常忙，规整下表格信息我，比较高效。”`))
  }, 60000)


  it('need web search', async () => {
    console.log(JSON.stringify(await MetaPrompt.getPrompt('根据当前用户的提问，判断是否需要网络搜索，注意：一般时效性或者具体信息才需要网络搜索'), null, 4))
  }, 1E8)

  it('isParentSupported', async () => {
    console.log(JSON.stringify(await MetaPrompt.getPrompt('根据提供的聊天记录，判断客户家里（父母）是否支持客户出国的意向，如果无法判断返回 null'), null, 4))
  }, 60000)

  it('isNeedPushProject', async () => {
    console.log(JSON.stringify(await MetaPrompt.getPrompt('根据提供的聊天记录，判断当前是否是适合的时机应该给用户推荐对应的留学项目（国家），比如用户在主动询问项目等， 如果无法判断返回 null'), null, 4))
  }, 60000)

  it('pickProject', async () => {
    console.log(JSON.stringify(await MetaPrompt.getPrompt('根据提供的 用户信息和留学项目列表，选出适合用户的留学项目，如果没有合适的项目可以推荐一个。'), null, 4))
  }, 60000)

  it('groupPick', async () => {
    console.log(JSON.stringify(await MetaPrompt.getPrompt(`根据提供的 用户跟留学顾问的聊天记录，判断当前用户想要主动进入哪个群，群包括：'单词群', '低龄规划群', '本科规划群', '硕士规划群'
只返回群名即可，如果无法判断 返回 null`), null, 4))
  }, 60000)

  it('cityToProvince', async () => {
    console.log(JSON.stringify(await MetaPrompt.getPrompt('帮我写一个 prompt，把提供的城市名转为中国的省份名称， 如果是海外城市，则返回 海外'), null, 4))
  }, 60000)

  it('is budget from counselor', async () => {
    console.log(JSON.stringify(await MetaPrompt.getPrompt('帮我写一个 prompt，给你一个预算和一个聊天记录，帮我判断这个预算是否是从聊天记录中提取的'), null, 4))
  }, 60000)

  it('is budget from counselor', async () => {
    console.log(JSON.stringify(await MetaPrompt.getPrompt('帮我写一个 prompt，给你一个预算和一个聊天记录，帮我判断这个预算是否是从聊天记录中提取的'), null, 4))
  }, 60000)

  it('category', async () => {
    console.log(JSON.stringify(await MetaPrompt.getPrompt('根据顾问和客户的聊天记录，判断客户的需求是 1. 咨询申博 2. 职业规划 3. 纠结 博士 或 职业规划比较 或 其它 中的哪一种'), null, 4))
  }, 60000)


  it('usersad', async () => {
    console.log(JSON.stringify(await MetaPrompt.getPrompt(`帮我写一个专门用于提取客户想要咨询的人的教育阶段（\`current_level_of_education\`）的提示（Prompt）。请注意以下几点：

**特别注意事项：**

1. **确定咨询对象：** 首先思考客户是为谁进行咨询，是客户本人还是他人（例如，他们的孩子）。
2. **推断教育阶段：** 
   - 如果客户为孩子咨询，且孩子年龄较小，\`current_level_of_education\` 应该为 \`'低龄'\`。
   - 如果客户没有明确说明当前的学历，但提到了想申请的学历（如硕士或博士），可以推断客户的当前教育阶段为申请学历的上一级（例如，想申请硕士，则当前学历为 \`'本科'\`）。

**示例：**

1. **用户咨询自己：**

   - **聊天记录：**  
     用户：*"老师，想问下国内双非电子信息研究生前景大概怎么样"*

   - **分析：**  
     用户想了解电子信息专业研究生的前景，未提及当前学历，但想申请研究生，可推断当前学历为本科。

   - **提取结果：**  
     \`{"current_level_of_education": "本科"}\`

2. **用户咨询子女：**

   - **聊天记录：**  
     用户：*"87年，清华美院本硕，儿子10岁，女儿8岁，老公金融民工。  
     女儿钢琴芭蕾，学习，样样轻松样样精通，目前能买到的所有书，包括莎士比亚，四书基本都看完了，在写自己的小说。  
     儿子聪明但不踏实，只学了网球，英文不错，一直抓的比较紧，目前雅思可以做个4.5…  
     家里存款2000，住房1000，北京，我自己有一个营利性幼儿园，经济形势不好，每年也就150利润，目前有人想接手，到手差不多500。对于我们这种家庭，我有两个问题：一是，现在幼儿园要不要卖？二是，两个孩子有没有好的规划方向？咨询愿付费。"*

   - **分析：**  
     用户在咨询两个孩子的规划，孩子年龄分别为10岁和8岁，属于低龄阶段。

   - **提取结果：**  
     \`{"current_level_of_education": "低龄"}\`

**提示（Prompt）：**

---

您的任务是仔细阅读用户与留学顾问的聊天记录，提取用户想要咨询的人的教育阶段（\`current_level_of_education\`）。

**步骤：**

1. **阅读聊天记录：** 仔细阅读用户的聊天内容，确定咨询对象是用户本人还是他人（如子女）。
2. **确定教育阶段：** 根据聊天内容，判断咨询对象的当前教育阶段。
   - 如果咨询对象年龄较小（如未上学或小学阶段），设为 \`'低龄'\`。
   - 如果用户未明确提及当前学历，但提到了想要申请的学历，可以推断其当前学历为申请学历的上一级。
3. **输出结果：** 将提取的信息以 JSON 格式输出，放在 \`<extracted_info>\` 标签内。

**可选的教育阶段枚举值：**

['低龄', '小学', '初中', '职高', '中专', '技校', '高中', '大专', '本科', '硕士', '博士']

**示例输出：**

<extracted_info>
{"current_level_of_education": "本科"}
</extracted_info>

请按照上述提示处理聊天记录，并输出相应的 \`current_level_of_education\``), null, 4))
  }, 60000)
})