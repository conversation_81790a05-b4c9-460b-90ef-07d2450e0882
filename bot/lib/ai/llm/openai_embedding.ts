import { AzureOpenAIEmbeddings, OpenAIEmbeddings } from '@langchain/openai'
import { Config } from '../../../config/config'

export class AzureOpenAIEmbedding {
  public static getInstance(): OpenAIEmbeddings {
    return new AzureOpenAIEmbeddings(
      {
        azureOpenAIApiKey: '********************************',
        azureOpenAIApiVersion: '2023-05-15',
        azureOpenAIApiInstanceName: 'top-sales',
        azureOpenAIApiDeploymentName: 'text-embedding-3-large',
        configuration: {
          baseURL: 'https://top-sales.openai.azure.com/'
        },
        dimensions: 1536, // 最大支持到 3072， 但是 ElasticSearch 上限为 2048
      }
    )
  }
}


export class OpenaiEmbedding {
  public static getInstance(): OpenAIEmbeddings {
    return new OpenAIEmbeddings(
      {
        openAIApiKey: Config.setting.openai.apiKeys[0],
        configuration: {
          baseURL: Config.setting.openai.apiBaseUrl
        },
        modelName: 'text-embedding-3-small'
      }
    )
  }
}