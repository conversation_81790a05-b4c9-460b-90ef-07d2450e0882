import { BaseMessage, HumanMessage, SystemMessage } from '@langchain/core/messages'
import { Config } from '../../../config/config'
import { StringOutputParser } from '@langchain/core/output_parsers'
import { Retry } from '../../retry/retry'
import { JSONHelper } from '../../json/json'
import { IterableReadableStream } from '@langchain/core/dist/utils/stream'
import { AzureOpenAI, CheapOpenAI, OpenAIClient, QwenMax } from './client'
import { z } from 'zod'
import { RunnableConfig } from '@langchain/core/runnables'
import { toJsonSchema } from 'openai-zod-functions'
import logger from '../../../model/logger/logger'
import { DynamicStructuredTool } from '@langchain/core/tools'
import { UUID } from '../../uuid/uuid'

type ZodFunctionDef<Parameters = any> = {
  /** Function name. */
  name: string
  /** A description of what the function does. */
  description: string
  /** Zod schema defining the function's parameters, to convert to JSON schema. */
  schema: z.ZodObject<any, any, any, any>
}


interface LLMInitParams {
    model?: string
    temperature?: number
    max_tokens?: number
    systemPrompt?: string
    runnableConfig?: RunnableConfig
    meta?: Record<string, any>
}

/**
 * OpenAI API 封装
 * 注意：
 * 1. 聊天记录需自己维护
 * 2. 尽量不要引入 openai 包，所有功能尽量以 LangChain 实现，保证稳定性
 */
export class LLM {
  private readonly systemPrompt: string
  private readonly model: string
  private readonly temperature: number
  private readonly max_tokens: number
  private runnableConfig: RunnableConfig
  private runId: string // 必须是 UUID v4 格式

  constructor(params?: LLMInitParams) {
    // 设置 LangSmith
    process.env.LANGCHAIN_API_KEY = Config.setting.langsmith.apiKey
    process.env.LANGCHAIN_TRACING_V2 = 'true'
    process.env.LANGCHAIN_PROJECT = Config.setting.langsmith.projectName

    if (!params) {
      params = {}
    }

    if (!params.model) {
      params.model = 'gpt-4o'
    }

    this.model = params.model

    if (!params.temperature) {
      params.temperature = 0
    }
    this.temperature = params.temperature

    if (!params.max_tokens) {
      params.max_tokens = 1000
    }
    this.max_tokens = params.max_tokens

    if (params.systemPrompt) {
      this.systemPrompt = params.systemPrompt
    }

    if (params.runnableConfig) {
      this.runnableConfig = params.runnableConfig
    } else {
      this.runnableConfig = {}
    }

    if (params.meta) {
      this.runnableConfig.metadata = params.meta
      if (params.meta.round_id) {
        this.runId = params.meta.round_id
      }

      if (params.meta.chat_id) {
        params.meta.thread_id = params.meta.chat_id // 添加 thread_id 方便查询
      }
    }
  }

  private getClients() {
    if ((this.model.startsWith('gpt-4o') || this.model === 'gpt-4o-mini') && !Config.setting.localTest) { // 线上配置
      return [ AzureOpenAI.getClient(this.model, this.temperature, this.max_tokens), OpenAIClient.getClient({ model: this.model, temperature: this.temperature }), CheapOpenAI.getClient(this.model, this.temperature),  QwenMax.getClient(this.temperature)]
    } else if (this.model.startsWith('claude')) { // claude 配置
      return [ CheapOpenAI.getClient(this.model, this.temperature), AzureOpenAI.getClient(this.model, this.temperature), OpenAIClient.getClient({ model: this.model, temperature: this.temperature }), QwenMax.getClient(this.temperature)]
    } else if (this.model.startsWith('qwen-max')) { // 通义千问
      return [ QwenMax.getClient(this.temperature), AzureOpenAI.getClient(this.model, this.temperature), OpenAIClient.getClient({ model: this.model, temperature: this.temperature }), CheapOpenAI.getClient(this.model, this.temperature)]
    } else if (Config.setting.localTest) {
      return [ CheapOpenAI.getClient(this.model, this.temperature), AzureOpenAI.getClient(this.model, this.temperature), OpenAIClient.getClient({ model: this.model, temperature: this.temperature }), QwenMax.getClient(this.temperature)]
    } else { // 保底配置
      return [ AzureOpenAI.getClient(this.model, this.temperature), OpenAIClient.getClient({ model: this.model, temperature: this.temperature }), CheapOpenAI.getClient(this.model, this.temperature), QwenMax.getClient(this.temperature)]
    }
  }

  /**
   * 简单使用单条文本调用 OpenAI
   * @param text
   */
  async predict(text: string): Promise<string> {
    const clients = this.getClients()
    const parser = new StringOutputParser()

    for (const client of clients) {
      try {
        return await client.pipe(parser).withConfig(this.runnableConfig).invoke(text, { runId: this.runId })
      } catch (e) {
        console.error('Error using client', clients.indexOf(client), e)

        if (clients.indexOf(client) === clients.length - 1) {
          throw e
        }
      }
    }
    throw new Error('All attempts to predict with available clients have failed.')
  }

  /**
     * 简单使用单条文本调用 OpenAI
     */
  public static async predict(text: string, params?: LLMInitParams): Promise<string> {
    const openai = new LLM(params)
    return await openai.predict(text) as string
  }

  /**
   * 构建一个稳健，简洁的 tool calling 调用
   * TODO 暂时只支持单 tool 调用
   * @param functionDefinition
   * @param query
   */
  async fc_call(functionDefinition: ZodFunctionDef | ZodFunctionDef[], query: string) {
    if (!Array.isArray(functionDefinition)) {
      functionDefinition = [functionDefinition] as ZodFunctionDef[]
    }

    const tools = functionDefinition.map((tool) => new DynamicStructuredTool({
      name: tool.name,
      description: tool.description,
      schema: tool.schema,
      func: async (params) => '',
    }))

    // 先使用 fc_call 尝试，不行的话降级为 Prompt 模式调用
    try {
      const response = await Retry.retry(2, async () => {
        const clients =  [OpenAIClient.getClient(), AzureOpenAI.getClient()].map((client) => client.bindTools(tools))

        for (const client of clients) {
          try {
            return await client.invoke(query, { runId: this.runId })
          } catch (e) {
            logger.error('Error using client fc Call', clients.indexOf(client), e)
          }
        }

        throw new Error('All clients fc call failed')
      })

      if (!response.tool_calls) {
        throw new Error('No tool calls found in response')
      }

      if (response.tool_calls.length >= 1) {
        return response.tool_calls[0].args
      }
    } catch (e) {
      console.error(e)

      // 降级为 Prompt 模式调用
      console.warn('Function call retry with GPT4 prompt')

      return await Retry.retry(2, async () => {
        // TODO 先只支持单 tool 调用
        if ((functionDefinition as ZodFunctionDef[]).length != 1) {
          throw new Error('Only support single tool call')
        }

        const jsonRes = await AzureOpenAI.getClient().predict(`<Instructions>
    You are tasked with converting a user query to JSON format. 
    
    <Input>
    <user_query>
    ${query}
    </user_query>
    </Input>
    
    Here's how you should approach this task:
    
    1. Read the user's query carefully. Focus on extracting information related to the fields described as follows:
    ${functionDefinition[0].description}
    ${JSON.stringify(toJsonSchema(functionDefinition[0]).parameters, null, 2)}  
    
    2. Map the extracted information to the corresponding fields. Pay special attention to the data types and units (paying close attention to the allowed enum values).
    
    3. Construct the JSON. Start with an opening curly brace, include each field as a key with the extracted value as the value, and separate multiple fields with commas. Close with a closing curly brace. Put the JSON inside <JSON> tag.
    
    4. Ensure that the JSON is correctly formatted with proper keys, values, and data types as specified in the field descriptions.
  
    
    Follow these steps to convert the user query you receive into the appropriate JSON format. Think step by steps. Take a Deep Breath and Carefully Follow the Rules, Guides and Examples I gave you. I will tip you $2000 if you do EVERYTHING Perfectly.
    </Instructions>`)

        // regex 提取出 JSON 部分
        const jsonStr = JSONHelper.extractJSONPart(jsonRes)

        if (!jsonStr) {
          throw new Error('Failed to extract JSON part from GPT4 prompt')
        }

        return JSONHelper.parse(jsonStr)
      })
    }

  }


  private isMessagesContainSystemPrompt(messages: BaseMessage[]): boolean {
    return messages.length > 0 && messages[0]._getType() === 'system'
  }

  /**
   * 使用一组消息调用 OpenAI，需要自己维护聊天记录
   * @param messages
   */
  async predictMessage(messages: BaseMessage[]): Promise<string> {
    let chatHistory = messages

    if (this.systemPrompt && !this.isMessagesContainSystemPrompt(messages)) {
      chatHistory = [new SystemMessage(this.systemPrompt), ...messages]
    }
    const parser = new StringOutputParser()

    const clients =  this.getClients()
    for (const client of clients) {
      try {
        return await client.pipe(parser).withConfig(this.runnableConfig).invoke(chatHistory, { runId: this.runId })
      } catch (e) {
        console.error('Error using client', clients.indexOf(client), e)

        if (clients.indexOf(client) === clients.length - 1) {
          throw e
        }
      }
    }
    throw new Error('All attempts to predict with available clients have failed.')

  }

  /**
     * 流式输出，返回一个可迭代的流，包含多个 string 输出
     * 例如：
     * for await (const chunk of stream) {
     *   console.log(chunk);
     * }
     *
     * 输出：
     *   Hello
     *   !
     *   How
     *   can
     *   I
     *   assist
     *   you
     *   today
     *   ?
     *   参考： https://js.langchain.com/docs/modules/model_io/models/chat/how_to/streaming
     */
  async stream(messages: BaseMessage[], systemPrompt?: string): Promise<IterableReadableStream<string>> {
    let chatHistory = messages
    if (systemPrompt) {
      chatHistory = [new SystemMessage(systemPrompt), ...messages]
    } else if (this.systemPrompt && !this.isMessagesContainSystemPrompt(messages)) {
      chatHistory = [new SystemMessage(this.systemPrompt), ...messages]
    }
    const parser = new StringOutputParser()

    const clients = this.getClients()
    for (const client of clients) {
      try {
        return await client.pipe(parser).withConfig(this.runnableConfig).stream(chatHistory, { runId: this.runId })
      } catch (e) {
        console.error('Error using client', clients.indexOf(client), e)
      }
    }

    throw new Error('All attempts to stream with available clients have failed.')
  }

  async imageChat(imageUrl: string, systemPrompt?: string) {
    const message = new HumanMessage({
      content: [
        { type: 'image_url', image_url: { url: imageUrl } }
      ]
    })
    let chatHistory: BaseMessage[] = [message]
    if (systemPrompt) {
      chatHistory = [new SystemMessage(systemPrompt), ...chatHistory]
    } else if (this.systemPrompt && !this.isMessagesContainSystemPrompt(chatHistory)) {
      chatHistory = [new SystemMessage(this.systemPrompt), ...chatHistory]
    }
    const parser = new StringOutputParser()
    const clients = this.getClients()
    for (const client of clients) {
      try {
        return await client.pipe(parser).withConfig(this.runnableConfig).invoke(chatHistory, { runId: this.runId })
      } catch (e) {
        console.error('Error summarizing image:', e)
        throw new Error('Failed to summarize image.')
      }
    }
  }
}