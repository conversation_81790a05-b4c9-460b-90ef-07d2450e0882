import { AzureChatOpenAI, ChatOpenAI } from '@langchain/openai'
import { Config } from '../../../config/config'
import { ChatAlibabaTongyi } from '@langchain/community/chat_models/alibaba_tongyi'

interface IOpenAIInitParams {
    model?: string
    temperature?: number
    timeout?: number
}

export const CLIENT_DEFAULT_TIMEOUT  = 1.5 * 60 * 1000 // 1.5 minutes

export class OpenAIClient {
  public static getClient(params?: IOpenAIInitParams): ChatOpenAI {
    if (params?.timeout && params.timeout < 1000) {
      throw new Error('Timeout 以秒为单位，请不要填写太短，否则请求会全部超时')
    }

    return new ChatOpenAI({
      openAIApiKey: Config.setting.openai.apiKeys[0],
      configuration: {
        baseURL: Config.setting.openai.apiBaseUrl
      },
      model: params?.model ? params.model : 'gpt-4o-2024-08-06',
      temperature: params?.temperature ? params.temperature : 0,
      timeout: params?.timeout ? params.timeout : CLIENT_DEFAULT_TIMEOUT,
      maxRetries: 1
    })
  }
}

export class AzureOpenAI {
  public static getClient(model = 'gpt-4o', temperature = 0, max_tokens = 1000, timeout = CLIENT_DEFAULT_TIMEOUT): ChatOpenAI {
    if (timeout < 1000) {
      throw new Error('Timeout 以秒为单位，请不要填写太短，否则请求会全部超时')
    }

    if (model === 'gpt-4o-mini') {
      model = 'gpt-4o-mini'
    } else {
      model = 'gpt-4o-2'
    }

    return new AzureChatOpenAI({
      temperature: temperature,
      maxTokens: max_tokens,
      azureOpenAIApiKey: Config.setting.azureOpenAI.azureOpenAIApiKey,
      azureOpenAIApiVersion: Config.setting.azureOpenAI.azureOpenAIApiVersion,
      azureOpenAIApiInstanceName: Config.setting.azureOpenAI.azureOpenAIApiInstanceName,
      azureOpenAIApiDeploymentName: model,
      configuration: {
        baseURL: Config.setting.azureOpenAI.apiBaseUrl
      },
      timeout: timeout,
      maxRetries: 1
    })
  }
}

export class CheapOpenAI {
  public static getClient(model = 'gpt-4o-2024-08-06', temperature = 0): ChatOpenAI {
    return new ChatOpenAI({
      openAIApiKey: Config.setting.cheapOpenAI.apiKey,
      configuration: {
        baseURL: Config.setting.cheapOpenAI.apiBaseUrl
      },
      model: model,
      temperature: temperature,
      timeout: CLIENT_DEFAULT_TIMEOUT,
      maxRetries: 1
    })
  }
}

export class MiTaAI {
  public static getClient(model : 'concise' | 'detail' | 'research', temperature = 0): ChatOpenAI {
    return new ChatOpenAI({
      openAIApiKey: Config.setting.cheapOpenAI.apiKey,
      configuration: {
        baseURL: 'http://112.124.32.162:8000/v1',
        defaultHeaders: {
          Authorization: 'Bearer 61638845b28fa859c374a79f-0abc662597fb4d4ca498a786cbffb761'
        }
      },
      modelName: model,
      temperature: temperature,
      timeout: CLIENT_DEFAULT_TIMEOUT // 1.5 minutes
    })
  }
}

export class QwenMax {
  public static getClient(temperature = 0) {
    return new ChatAlibabaTongyi({
      alibabaApiKey: Config.setting.qwen.apiKey,
      temperature: temperature,
      model: 'qwen-max',
    })
  }
}

export class PerplexityAI {
  public static getClient(temperature = 0) {
    return new ChatOpenAI({
      openAIApiKey: '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',
      configuration: {
        baseURL: 'https://api.perplexity.ai'
      },
      modelName: 'llama-3-sonar-large-32k-online',
      temperature: temperature,
      timeout: CLIENT_DEFAULT_TIMEOUT // 1.5 minutes
    })
  }
}