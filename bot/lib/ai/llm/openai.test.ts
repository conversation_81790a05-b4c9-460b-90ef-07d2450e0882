import { LLM } from './LLM'

import { z } from 'zod'
import { HumanMessage } from '@langchain/core/messages'
import { CheapOpenAI, MiTaAI, OpenAIClient, PerplexityAI } from './client'

describe('Test', function () {
  const openai = new LLM()

  it('predict', async () => {
    console.log(await openai.predict('test'))
  })

  it('投43', async () => {
    console.log(await openai.predict('hi'))
  }, 60000)

  it('predictMessage', async () => {
    console.log(await openai.predictMessage([new HumanMessage('hi')]))
  })

  it('systemMessage', async () => {
    const openai2 = new LLM({ systemPrompt:'使用活泼的语气回答' })

    console.log(await openai2.predictMessage([new HumanMessage('hi')]))
  }, 30000)

  it('stream', async () => {
    const stream = await openai.stream([new HumanMessage(`
目前我在30层等电梯，图片描述了当前电梯的运行状态。假设电梯每 1秒钟可以上升或下降一层，不考虑开关门的时间，其他楼层也不存在乘坐电梯的人。那么我最快要多久才能乘坐该电梯到达地面一层？    
电梯目前运行状态是：正在15层向下    
        `)])

    let str = ''

    for await (const chunk of stream) {
      str += chunk
      console.log(str)
    }
  }, 60 * 1000)

  it('function_call', async () => {
    const fcDefinition =  {
      name: 'analyzeUserPersonaTags',
      description: '对从聊天记录中提取用户画像标签进行处理',
      schema: z.object({
        educational_background: z.string().describe('用户当前的学历，尽量使用缩写，如大四，本科，保研，研0, 研一，研二，在职，博士等。如果没有，则填空字符串'),
        goal: z.string().describe('用户的目标，使用简短的不超过5个字的描述，如找工作，发论文，保研，做项目等。如果没有，则填空字符串'),
        field: z.string().describe('用户的研究领域，尽量使用缩写，如CV,NLP,RL等。如果没有，则填空字符串'),
        major: z.string().describe('用户的专业方向，如机械、计算机、数学等。如果没有，则填空字符串'),
      }),
    }

    const fcRes = await openai.fc_call(fcDefinition, '作为一个大四的学生')
    console.log(JSON.stringify(fcRes, null, 4))
  }, 30000)

  it('imageChat', async () => {
    console.log(await new LLM({ temperature: 0, max_tokens: 200 }).imageChat(
      'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/yuhe/sop/%E6%8A%96%E9%9F%B3%E6%88%AA%E5%9B%BE-%E5%81%A5%E8%BA%AB%E6%88%BF.jpeg',
      `你作为一名抖音流量课老师在与客户的聊天过程中，客户发来一张图片，请对这张图片进行简要解释，只输出一段话。
- 如果客户发的是抖音首页截图，则需要明确描述以下内容：头像，名称，背景，获赞，关注，粉丝，作品描述，作品获赞等信息`))
  }, 60000)

  it('用户画像身份匹配', async () => {
    // const userStory = {
    //   role: '本科',
    //   goal: '找工作',
    //   field: '计算机视觉',
    //   difficulty: '找工作难',
    //   current_progress: '还没开始',
    //   level_of_urgency: '非常紧急'
    // }
    //
    // console.log(JSON.stringify(await new UserStoryVectorDB().similaritySearch(userStory, 1), null, 4))
  }, 30000)

  it('langSmith', async () => {
    const ai = new LLM()

    console.log(await ai.predict('滑铁卢计算机一年学费多少'))
  }, 30000)


  it('a', async () => {
    const input = `$0.019080
$0.036510
$0.035340
$0.030540
$0.041040
$0.054720
$0.054540
$0.054780
$0.159180
$0.215940
$0.135750
$0.154080
$0.108780
$0.168840
$0.121230
$0.130830
$0.032700
$0.043410
$0.043590
$0.050400
$0.034110
$0.073200
$0.055110
$0.050670
$0.021660
$0.036570
$0.038640
$0.040500
$0.020100
$0.030270
$0.037200
$0.033570
$0.021090
$0.035280
$0.036360
$0.024810
$0.044160
$0.063240
$0.043260
$0.049170`
    // 使用 split 方法按换行符分割字符串，得到每个价格
    const priceArray = input.split ('\n')

    // 将每个价格字符串中的美元符号 '$' 移除，并将其转换为浮点数
    const prices = priceArray.map ((price) => parseFloat (price.replace ('$', '')))

    // 使用 reduce 方法计算所有价格的总和
    const totalSum = prices.reduce ((sum, current) => sum + current, 0)

    // 输出结果
    console.log ('Total sum of prices: $', totalSum.toFixed (6))

  }, 30000)


  it('perplexityAI', async () => {
    console.log(await PerplexityAI.getClient().predict('我滑铁卢毕业，75均分去哪里读研好 留学'))
  }, 60000)

  it('MitA', async () => {
    console.log(await  MiTaAI.getClient('concise').predict('美国CS有什么推荐'))
  }, 60000)

  it('MitASelect', async () => {
    console.log(await CheapOpenAI.getClient().predict(`根据用户信息，从下方选出最适合的项目， 如果下方没有合适项目可以推荐一个：
用户信息：
- 学历：本科
- 目标：找个好工作
- 预算：30-40万
- 专业：计算机  
- GPA：73分
- 本科学校：滑铁卢大学  
    
项目：
1. 国际本科3+1/2+2
  - 高考失利和无法上本科的同学的海外升学捷径，对于想体验留学生活的同学，意味着花更少的钱，本科保底，逆袭海外名校
  - 国家本科3+1，总预算50-80w，每年15w以上，分数在本科线下，英语单科在60以上，对接学校一般在QS200-300；2+2的总预算60-80w，成绩一般，英语单科80分上。不想在国内读普通本科的同学。
  
2. 港澳方向
  - 澳门总预算80w+，香港100w+；每年20-35w,“高考+留学”双保险。港澳申请与高考时间不冲突，可提前申请港澳学校，又可参加高考志愿填报，并且港澳离大陆近，对于留学不想去太远的家庭的首选。需要学生成绩过一本线。

3. 新加坡
  - 总预算65-80w，对于高考失利，成绩一般或者较弱的学生，通过新加坡私立大学学习，能够逆袭海外名校（QS200以内）

4. 韩国、马来西亚
  - 适合低预算，想出国留学的同学，4年总预算50W以内。既离家不远，又省钱，还有机会申请进QS前200的海外大学
  - 韩国总预算50-60w，每年10w以上，韩语成绩在韩国大学的录取中占有较大的权重，即便高中成绩一般，但是韩语好，仍然有机会申请进QS前100的韩国大学，所以适合不想上专科，英语一般，想拿个本科文凭的人。

5. 英国、澳洲方向
  - 2个国家总预算都要125w多，每年至少30w，热门留学国家，名校多，升学方式多样，预科、国际大一或者高考成绩，可冲击高排名海外大学

6. 中外合办4+0
  - 总预算25-50；每年10w以上，达到本科线，不甘心只读个普通双非，英语单科100分以上，但又没有充足预算出国，想拿一个海外名校本科文凭的同学首选。 注意不要提及能拿国内外双文凭，一般非高考渠道只能拿海外文凭。
    4+0, 3+1 是指国内4年/3年， 国外0年或1年这个意思

7. 日本
  - 总预算40-60，每年15w左右
    `))
  }, 60000)

})