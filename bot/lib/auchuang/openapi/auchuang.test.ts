import { AoChuang } from './a<PERSON>uang'
import { AoChuangWechatContact } from './contact'
import { AoChuangWechatGroupContact } from './group_contact'
import { Config } from '../../../config/config'
import { CacheDecorator } from '../../cache/cache'
import { IMessageType } from '../../../service/message/message'
import { HumanTransfer, HumanTransferType } from '../../../service/baoshu/components/human_transfer'
import { getChatId } from '../../../config/chat_id'
import { ChatDB } from '../../../service/baoshu/database/chat'

describe('Test', function () {
  beforeAll(() => {

  })

  it('11', async () => {
    console.log(await AoChuangWechatGroupContact.isInvitedToLargeGroup('e165fa2098cbf425368a3e895214cbfb66c63020'))
  }, 30000)

  it('wxId', async () => {
    console.log(JSON.stringify(await AoChuangWechatContact.getContactByWxId('wxid_8003740064812'), null, 4))
  }, 30000)

  it('id', async () => {
    const chatId = getChatId('1d982c8bc0c504db696bd57e905f6d8c874d4bcd')
    await ChatDB.setHumanInvolvement(chatId, false)
  }, 30000)

  it('wechatID', async () => {
    // wxid_dxk6td46jn7f22
    const user = await AoChuangWechatContact.getContactByWechatId('wxid_dxk6td46jn7f22')
    console.log(JSON.stringify(user, null, 4))
  }, 30000)

  it('should pass', async () => {
    await AoChuang.sendMsg('ab4aacefc9c676f6c5611fb3a5d2986968024b28', 'hi')
  })

  it('list wechat accounts', async () => {
    console.log(JSON.stringify(await AoChuang.getWechatAccountList(), null, 4))
  }, 30000)

  it('get friend info', async () => {
    console.log(JSON.stringify(await AoChuang.getFriendInfo('ab4aacefc9c676f6c5611fb3a5d2986968024b28'), null, 4))
  }, 30000)

  it('list all friends', async () => {
    const friends = await AoChuang.getAllFriends()
    console.log(friends.length)
  }, 30000)

  it('get all groups', async () => {
    // console.log(await AoChuang.getGroups())
    console.log(JSON.stringify(await AoChuang.getAllGroups(), null, 4))
  }, 30000)

  it('get group by name', async () => {
    console.log(JSON.stringify(await AoChuangWechatGroupContact.getContactByName('点头学习 1 群'), null, 4))

  }, 30000)

  it('get contact by name', async () => {
    console.log(await AoChuangWechatContact.getContactByName('SYQ'))
    console.log(await AoChuangWechatContact.getContactByName('麦子'))
  }, 30000)

  it('send to group', async () => {
    CacheDecorator.decorateAsync(AoChuangWechatGroupContact.getContactByName)

    const group = await AoChuangWechatGroupContact.getContactByName('点头-AI学习231027群')
    console.log(JSON.stringify(group, null, 4))

  }, 30000)

  it('sss', async () => {
    await HumanTransfer.transfer('123', '42d5aca4ef6d7ec89e7812e54154bed03c8096f4', HumanTransferType.JoinedGroup)
  }, 30000)


  it('拉群', async () => {
    const group = await AoChuangWechatGroupContact.getContactByName('点头学习交流群')
    if (group) {
      console.log('拉群')
      await AoChuang.inviteFriendToGroup(group.id, ['42d5aca4ef6d7ec89e7812e54154bed03c8096f4'])
    }

  }, 30000)


  it('ss', async () => {
    const group = await AoChuangWechatGroupContact.getContactByName('点头学习交流群')
    const user = await AoChuangWechatContact.getContactById('42d5aca4ef6d7ec89e7812e54154bed03c8096f4')

    if (user && group) {
      await AoChuang.sendMsg(group.id, {
        type: IMessageType.GroupAt,
        content: `@${user.nickname} 同学，欢迎加入点头学习交流群，有什么问题都可以在群内交流的~`,
        at_id: user.wechatId
      }, true)
    }
  }, 30000)

  it('发送图片', async () => {
    // await AoChuang.sendMsg('ab4aacefc9c676f6c5611fb3a5d2986968024b28', {
    //     type: IMessageType.Image,
    //     content: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/img_v3_028v_e1f84008-ce0c-40fb-9fbc-7e2ce1ac26fg.png'
    // })

    const emojis = [
      'https://ac-java-user.oss-cn-shanghai.aliyuncs.com/weremote/chat-logs/091A9740B8254CF8832ACD9F5F717071/028e0c6052acf7f91367a4ac7042747f/47/ed7bdb4616363e96ffa5736c832279cb#ed7bdb4616363e96ffa5736c832279cb',
      'https://ac-java-user.oss-cn-shanghai.aliyuncs.com/weremote/chat-logs/091A9740B8254CF8832ACD9F5F717071/028e0c6052acf7f91367a4ac7042747f/47/038a7ba61c1ccf5bfe60dbd178fee654#038a7ba61c1ccf5bfe60dbd178fee654',
      'https://ac-java-user.oss-cn-shanghai.aliyuncs.com/weremote/chat-logs/091A9740B8254CF8832ACD9F5F717071/028e0c6052acf7f91367a4ac7042747f/47/dc493caae467aaa1699182f6d6abdc1c#dc493caae467aaa1699182f6d6abdc1c'
    ]
    for (const emoji of emojis) {
      await AoChuang.sendMsg('ab4aacefc9c676f6c5611fb3a5d2986968024b28', {
        type: IMessageType.CustomEmoticon,
        content: emoji
      })
    }



    // await AoChuang.sendMsg('ab4aacefc9c676f6c5611fb3a5d2986968024b28', {
    //     type: IMessageType.Text,
    //     content: 'hi'
    // })
  }, 30000)


  it('ss1', async () => {
    const user = await AoChuangWechatContact.getContactByName('SYQ')

    console.log(user)
  }, 30000)


  it('送礼物', async () => {
    const chatId = getChatId('578ee927a702471a4197320d294bfc33a3e4527e')
    await HumanTransfer.transfer(chatId, '578ee927a702471a4197320d294bfc33a3e4527e', HumanTransferType.UnknownMessageType)
  }, 30000)
})