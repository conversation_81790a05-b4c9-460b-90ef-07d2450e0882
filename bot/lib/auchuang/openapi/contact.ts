import { AoChuang } from './a<PERSON><PERSON>'
import { WechatContact } from '../event/type'
import { Config } from '../../../config/config'

interface UpdateAliasParams {
    id: string
    alias: string
    leading?: boolean // 是否在前面添加
    replace?: RegExp // 用于替换原有备注
}

export class AoChuangWechatContact {
  private static wechatIdMap: Map<string, WechatContact> = new Map() // 微信ID, 这里的 ID 有可能是用户设置的，也有可能是微信官方的唯一ID
  private static wxIdMap: Map<string, WechatContact> = new Map() // 微信内部唯一ID
  private static wechatNameMap: Map<string, WechatContact> = new Map()
  private static wechatAliasMap: Map<string, WechatContact> = new Map()
  private static idMap: Map<string, WechatContact> = new Map() // 奥创ID


  public static async pullContacts() {
    const friends = await AoChuang.getAllFriends()

    // 构建 Map
    for (const friend of friends) {
      if (friend.wechatAccountId !== Config.setting.wechatConfig?.id) { // TODO 只保留当前微信号的好友
        continue
      }

      if (friend.nickname) {
        this.wechatNameMap.set(friend.nickname, friend)
      }

      if (friend.wechatId) {
        this.wechatIdMap.set(friend.wechatId, friend)
        this.wxIdMap.set(friend.wechatId, friend)
      }

      if (friend.alias) {
        this.wechatIdMap.set(friend.alias, friend)
      }

      if (friend.id) {
        this.idMap.set(friend.id, friend)
      }

      if (friend.conRemark) {
        this.wechatAliasMap.set(friend.conRemark, friend)
      }
    }
  }

  public static async refreshContacts() {
    await this.pullContacts()
  }

  public static async getContactByName(name: string) {
    await this.pullContacts()

    return this.wechatNameMap.get(name)
  }

  public static async getContactById(id: string) {
    return await AoChuang.getContactByFriendId(id)
  }

  static async getContactByWechatId(wechatId: string) {
    await this.pullContacts()

    return this.wechatIdMap.get(wechatId)
  }

  static async getContactByWxId(wxId: string) {
    await this.pullContacts()

    return this.wxIdMap.get(wxId)
  }

  /**
   * 注意增加备注不会覆盖已有备注是在已有备注后面增加
   * @param param
   */
  static async updateAlias(param: UpdateAliasParams) {
    const { id, alias, leading, replace } = param

    if (Config.setting.localTest) { // 本地测试，id 为无效的
      return
    }

    if (!alias) {
      return
    }

    const contact = await this.getContactById(id)
    if (!contact) {
      return
    }

    let newAlias = contact.conRemark

    if (!newAlias) {
      newAlias = contact.nickname.slice(0, 4)
    }

    // if alias match replace regex
    if (replace && newAlias.match(replace)) {
      newAlias = newAlias.replace(replace, alias)
    } else if (leading) {
      newAlias = `${alias  } ${  newAlias}`
    } else {
      newAlias = `${newAlias  } ${  alias}`
    }

    await AoChuang.updateUserAlias(id, newAlias)
  }

  static async getAliasById(contactId: string) {
    const contact = await this.getContactById(contactId)
    if (!contact) {
      return ''
    }

    return contact.conRemark
  }
}