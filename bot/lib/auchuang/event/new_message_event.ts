import { BaseEvent } from './base_event'
import { IFriendNewMessageEventData, MsgOriginEnum, MsgSubTypeEnum, MsgTypeEnum } from './type'

export class NewMessageEvent extends BaseEvent {
  constructor(data: IFriendNewMessageEventData) {
    super(data)
  }

  public get wechatId(): string {
    return this.data.message.wechatAccountId
  }

  public get msgTime(): Date {
    return new Date(this.data.message.msgTime)
  }

  public get msgType(): MsgTypeEnum {
    return this.data.message.msgType
  }

  public get msgSubType(): MsgSubTypeEnum {
    return this.data.message.msgSubType
  }

  public get origin(): MsgOriginEnum {
    return this.data.message.origin
  }

  public get fromId(): string {
    return this.data.message.wechatFriendId
  }

  public get toId(): string {
    return this.data.message.wechatAccountId
  }
}