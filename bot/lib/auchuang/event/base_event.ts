import { IEventData } from './type'

export class BaseEvent {
  constructor(protected data: IEventData) {
  }

  public get id() {
    return this.data.id
  }

  public get eventCreateTime() {
    return new Date(this.data.occurTime)
  }

  public get type() {
    return this.data.type
  }

  public get tenantId() {
    return this.data.tenantId
  }

  public get version() {
    return this.data.eventVersion
  }
}