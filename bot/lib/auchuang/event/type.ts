export enum EventType {
    // 好友通过回调
    AcceptFriendResultEvent = 'AcceptFriendResultEvent',

    // 接受消息
    FriendNewMessageEvent = 'FriendNewMessageEvent',
    ChatroomNewMessageEvent = 'ChatroomNewMessageEvent',

    // 添加好友
    WechatFriendCreatedEvent = 'WechatFriendCreatedEvent',
    AddFriendSendCreateEvent = 'AddFriendSendCreateEvent',
    WechatFriendChangedEvent = 'WechatFriendChangedEvent', // 好友状态变化
    WechatFriendDeletedEvent = 'WechatFriendDeletedEvent', // 好友删除

    // 群管理
    WechatChatroomDeletedEvent = 'WechatChatroomDeletedEvent', // 群聊删除
    WechatChatroomChangedEvent = 'WechatChatroomChangedEvent', // 群聊变化
    WechatChatroomCreatedEvent = 'WechatChatroomCreatedEvent', // 群聊创建

    // 消息成功发送
    ChatroomMessageSentEvent = 'ChatroomMessageSentEvent',
    FriendMessageSentEvent = 'FriendMessageSentEvent',

    // 获取大图
    FriendMessageDownloadOriginImageResultEvent = 'FriendMessageDownloadOriginImageResultEvent',
}

export interface IEventData {
    id: string         // 事件id, 这是一个UUID
    occurTime: number   // 事件发生时间, 是一个long型时间戳
    type: EventType | string      // 事件类型, 一个字符串表明
    tenantId: string    // 事件租户ID
    eventVersion: number // 事件版本号
    [key: string]: any   // 其他数据,根据每个不同的事件类型有不同的参数
}

export interface IAcceptFriendTaskResultEvent extends IEventData {
    taskId: string // 任务ID
    success: boolean // 任务是否成功
}

export interface IFriendMessage {
    id: string              // 消息ID
    tenantId: string        // 租户ID
    wechatAccountId: string // 微信账号ID
    accountId: string       // 当时微信所属客服账号ID
    content: any            // 消息内容消息格式 // TODO 封装类型
    msgType: MsgTypeEnum       // 消息类型
    msgId: string           // 微信本地消息ID
    msgSvrId: string        // 微信服务器消息ID
    isSend: boolean         // 是否是主动发送的消息
    isDeleted: boolean      // 是否已删
    deleteDate: number      // 删除时间
    sendStatus: number      // 发送状态
    wechatTime: number      // 消息时间
    origin: MsgOriginEnum        // 消息来源
    isRecalled: boolean     // 是否已撤回
    msgSubType: MsgSubTypeEnum      // 消息子类型
    createDate: number      // 消息保存到数据库的时间
    updateDate: number      // 消息更新时间
    wechatFriendId: string  // 好友ID
}

export interface IChatroomMessage {
    accountId: string // 账户ID
    blockedMsg: boolean // 是否阻止消息
    chatroomId: string // 聊天室ID
    content: string // 消息内容
    createBy: string // 创建者ID
    createDate: number // 创建日期（时间戳）
    deletedMsg: boolean // 是否已删除消息
    deviceId: string // 设备ID
    deviceImei: string // 设备IMEI
    deviceOwnerId: string // 设备所有者ID
    id: string // 消息ID
    isDeleted: boolean // 是否已删除
    isRecalled: boolean // 是否已撤回
    isSend: boolean // 是否为发送的消息
    msgId: number // 消息标识
    msgSubType: number // 消息子类型
    msgSvrId: string // 消息服务器ID
    msgType: number // 消息类型
    origin: number // 消息来源
    sendStatus: number // 发送状态
    senderWechatId: string // 发送者微信ID
    talker: string // 对话者（通常是聊天室ID或对方的微信ID）
    tenantId: string // 租户ID
    updateBy: string // 更新者ID
    updateDate: number // 更新日期（时间戳）
    wechatAccountId: string // 微信账户ID
    wechatChatroomId: string // 微信聊天室ID
    wechatId: string // 微信ID
    wechatTime: number // 微信消息时间（时间戳）
}

export interface IFriendNewMessageEventData extends IEventData {
    message: IFriendMessage
    wechatId?: string
}

export interface IChatroomNewMessageEventData extends IEventData {
    message: IChatroomMessage
    wechatId?: string
}


export interface IFriendMessageDownloadOriginImageResultEvent extends IEventData {
    messageId: string
    content: string
    resultMsg: string
    wechatTime: number
    taskId: string
}


/**
 * 消息来源
 **/
export enum  MsgOriginEnum {
    /**
     * 手机端
     */
    PHONE = 0,

    /**
     * 客服聊天端, 这个地方发出去的群聊消息,没有wechatId:
     */
    CLIENT = 1,

    /**
     * 聊天机器人
     */
    ROBOT = 2,

    /**
     * 第三方接口
     */
    OPEN_API = 3
}

/**
 * 消息类型
 */
export enum MsgTypeEnum {
    SystemNotification = 10000, // 微信的系统消息
    Text = 1,                   // 文字
    Image = 3,                  // 图片
    Voice = 34,                 // 语音
    ContactCard = 42,           // 名片
    Video = 43,                 // 视频(mp4等)
    CustomEmoticon = 47,        // 自定义表情
    Location = 48,              // 位置
    LinkOrFile = 49,            // 链接/文件
    VoiceOrVideoCall = 50,      // 语音、视频通话
    VoiceRelatedSystemMessage = 64, // 语音相关的系统消息
    Transfer = 419430449,       // 转账
    RedPacket = 436207665,      // 红包
    RealTimeLocation = -1879048186, // 实时位置
    GroupMemberChange = 570425393, // 群成员变动
    JuxiaSystemMessage = 90000, // 聚侠系统消息
    TextWithAt = 90001,         // 微信文字带@
    RedPacketOpened = -10001,   // 红包打开
    MessageRecalled = -10002,   // 撤回消息成功（自己或者对方）
    ReferenceMessage = 822083633, // 引用类型消息
    ConfirmMessage = 10002,     // 去确认消息
}

/**
 * 消息子类型
 */
export enum MsgSubTypeEnum {
    /*普通的聊天*/
    COMMON = 0,
    /*分配记录消息*/
    ALLOT_MSG = 1,
    /*批量发送*/
    BATCH_SEND = 2,
    /*通过好友恢复*/
    ACCEPT_REPLY = 3,
    /*自动回复*/
    AUTO_REPLY = 4,
    // 入群欢迎语
    CHATROOM_WELCOME = 5
}


export interface NewFriendshipEventData extends IEventData {
    wechatId: string
    addFriendSend: AddFriendRecord
}

export interface WeChatFriend {
    id: string // 好友ID
    wechatAccountId: string // 所属微信ID
    wechatId: string // 好友微信ID
    alias: string // 好友微信号
    conRemark: string // 备注
    nickname: string // 昵称
    pyInitial: string // 昵称拼音缩写
    quanPin: string // 昵称拼音全拼
    avatar: string // 头像
    gender: number // 性别
    region: string // 地区
    addFrom: number // 添加途径（参考枚举值说明）
    phone: string // 电话号码
    labelList: any[] // 标签列表
    signature?: string // 签名
    isDeleted: boolean // 是否已删
    deleteDate: number // 删除时间
    accountId: string // 所属客服
    extendFields?: { [key: string]: any } // 扩展字段数据
    country: string // 国家
    province: string // 省份
    city: string // 城市
    groupId?: string // 所属分组
    isPassed: boolean // 是否通过
    passTime: number // 通过时间
    createDate?: number // 入库时间
    updateDate: number // 最后更新时间
    desc?: string // 好友描述
    additionalPicture?: string // 附加图片
    config?: { // 配置
        chat?: boolean // 是否在最近会话显示
    }
    taskId?: string // 如果是接口添加的将会有这个字段

    // 实例数据中存在但未在接口定义明确指出的字段
    entryptUsername?: string
    status?: number
    tenantId?: string
    updateBy?: string
    wechatLabelIds?: any[]
}

export interface WechatFriendCreatedEvent extends IEventData {
    wechatId: string
    wechatFriend: WeChatFriend
}

export interface AddFriendRecord {
    id: string // 唯一标识符
    tenantId: string // 租户id
    imei: string // 设备imei
    wechatAccountId: string // 工作微信的db_id
    keywords: string // 加好友时关键字(手机号/微信号)
    md5Keywords: string // 关键字md5
    friendRemark: string // 好友备注
    addTime: number // 添加时间（时间戳）
    name: string // 好友名字
    avatar: string // 好友头像
    greetingWords: string // 招呼语
    extra: string // 其他信息
    createBy: string // 创建人
    createDate: number // 创建时间
}

export interface WechatContact {
    accountId: string
    addFrom: number
    additionalPicture: string
    alias: string // 微信 ID
    avatar: string
    city: string
    conRemark: string // 微信备注
    country: string
    createBy: string
    createDate: number
    desc: string
    entryptUsername: string
    gender: number
    id: string
    isDeleted: boolean
    isPassed: boolean
    labels: string[]
    nickname: string
    passTime: number
    phone: string
    province: string
    pyInitial: string
    quanPin: string
    region: string
    signature: string
    tenantId: string
    updateBy: string
    wechatAccountId: string
    wechatId: string
}

interface ChatroomMember {
    avatar: string // 成员头像
    createDate: string // 成员加入日期
    deletedDate: string // 成员删除日期
    id: string // 成员标识
    isAdmin: boolean // 是否管理员
    isDeleted: boolean // 是否被删除
    nickname: string // 昵称
    updateDate: string // 最后更新日期
    wechatChatroomId: string // 微信聊天室ID
    wechatId: string // 微信ID
}

interface GroupOwner {
    avatar: string // 群主头像
    id: string // 群主标识
    nickname: string // 群主昵称
}

export interface WechatGroup {
    accountId: string // 帐户ID
    avatar: string // 聊天室头像
    chatroomId: string // 聊天室ID
    conRemark: string // 备注信息
    createBy: string // 创建者
    createDate: string // 创建日期
    deleteDate: string // 删除日期
    groupId: string // 分组ID
    id: string // 聊天室标识
    isDeleted: boolean // 是否已删除
    members: ChatroomMember[] // 聊天室成员列表
    nickname: string // 聊天室昵称
    notice: string // 聊天室公告
    owner: GroupOwner // 群主信息
    ownerWechatId: string // 群主微信ID
    pyInitial: string // 昵称拼音缩写
    quanPin: string // 昵称拼音全拼
    selfDisplayName: string // 自己在聊天室的显示名称
    tenantId: string // 租户ID
    updateBy: string // 更新者
    updateDate: string // 更新日期
    wechatAccountId: string // 微信账户ID
}