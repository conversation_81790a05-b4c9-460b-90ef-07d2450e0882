import axios, { AxiosInstance, AxiosResponse, AxiosRequestConfig } from 'axios'
import CryptoJS from 'crypto-js'

/**
 * 平台类型
 */
export type Platform = 'dingtalk' | 'app';

/**
 * SDK配置选项
 */
export interface SDKConfig {
    /** API Token */
    token: string
    /** 公司ID */
    corpId: string
    /** 用户ID */
    userId?: string
    /** 平台类型，默认为 'app' */
    platform?: Platform
    /** API版本，默认为 'v2' */
    version?: string
    /** 请求超时时间，默认为 10000ms */
    timeout?: number
}

/**
 * API响应结构
 */
export interface APIResponse<T = any> {
    code: number
    msg: string
    result?: T
    success: boolean
}

/**
 * 列表查询条件
 */
export interface ListCondition {
    attr: string
    symbol: string
    value: any[]
    subAttr?: string
}

/**
 * 列表查询参数
 */
export interface ListParams {
    formId?: number
    conditions?: ListCondition[]
    page?: number
    pageSize?: number
    sortMap?: {
        field: string
        sort: 'asc' | 'desc'
    }
}

/**
 * 销帮帮API SDK
 */
export class XiaoBangBangSDK {
  private axios: AxiosInstance
  private token: string
  private corpId: string
  private userId?: string

  constructor(config: SDKConfig) {
    const {
      token,
      corpId,
      userId,
      platform = 'app',
      version = 'v2',
      timeout = 10000
    } = config

    this.token = token
    this.corpId = corpId
    this.userId = userId

    // 根据平台选择基础URL
    const baseURL = platform === 'dingtalk'
      ? `https://proapi.xbongbong.com/pro/${version}/api/`
      : `https://appapi.xbongbong.com/pro/${version}/api/`

    // 创建axios实例
    this.axios = axios.create({
      baseURL,
      method: 'POST',
      timeout,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    // 请求拦截器
    this.axios.interceptors.request.use(
      (config) => {
        // 确保data存在
        config.data = config.data || {}

        // 添加基础参数
        config.data.corpid = this.corpId
        if (this.userId) {
          config.data.userId = this.userId
        }

        // 生成签名
        const dataString = JSON.stringify(config.data)
        const sign = CryptoJS.SHA256(dataString + this.token).toString(CryptoJS.enc.Hex)

        // 添加签名到header
        config.headers = config.headers || {}
        config.headers.sign = sign

        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    this.axios.interceptors.response.use(
      (response: AxiosResponse) => {
        return response.data
      },
      (error) => {
        return Promise.reject(error)
      }
    )
  }

  /**
     * 通用请求方法
     */
  async request<T = any>(endpoint: string, data?: Record<string, any>): Promise<APIResponse<T>> {
    try {
      const response = await this.axios.request({
        url: endpoint,
        data: data || {},
      })
      // 由于响应拦截器已经返回了response.data，这里直接返回即可
      return response as unknown as APIResponse<T>
    } catch (error) {
      throw error
    }
  }
}

export default XiaoBangBangSDK