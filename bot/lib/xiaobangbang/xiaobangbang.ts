import XiaoBangBangSDK from './xbb'


export interface XbbTag {
  group: string
  name: string
}

export interface IXbbCustomer {
  text_36: string // 群名
  text_16: string // 创建人， 谁创建的这个数据
  text_54: '已拉咨询群' | '已拉运营群' | '不同意进群'// 是否拉群
  ownerId?: string[] // 顾问（接收客户的顾问）
  text_53: '低龄' | '本科' | '硕士生' | '博士' | '其他' // 升学阶段
  text_50: '学生' | '家长' // 身份 学生/家长
  text_52: '10-20万' | '20-30万' | '30-50万' | '50万以上' | '其他' // 预算（每年）
  text_1: string // 微信昵称
}

export enum XbbFormId {
  Clue = 7892940,
  Customer = 7713636,
}

export class XiaoBangBangAPI {
  private static client: XiaoBangBangSDK | undefined

  private static getClient() {
    if (!this.client) {
      this.client = new XiaoBangBangSDK({
        token: '7106b6711bea64cd955e7c338d2865e8',
        corpId: 'wpOGQmBgAAe8yIUz6wrFRnOpaw46Xyvw',
        // userId: 'woOGQmBqAA-liUrOYUiKRuaLH6H3f6_g',
        timeout: 10 * 1000,
        platform: 'app'
      })
    }
    return this.client
  }

  public static async getAllMembers() {
    const members = await this.getClient().request('user/list', {
      page: 1,
      pageSize: 1000,
    })

    return (members as any).result.userList
  }

  public static async getMemberIdByName(name: string) {
    const members = await this.getClient().request('user/list', {
      page: 1,
      pageSize: 1000,
    })

    const users = (members as any).result.userList

    if (users.length === 0) {
      return null
    }

    const user = users.find((user: any) => user.name === name)
    if (!user) {
      return null
    }
    return user.userId
  }

  public static async getLastCustomerByName(name: string) {
    const clients = await this.getClient().request('customer/list', {
      formId: 7713636,
      conditions: [
        {
          attr: 'text_1',
          symbol: 'equal',
          value: [name]
        }
      ],
    })

    const users = (clients as any).result.list

    if (users.length === 0) {
      return null
    }

    return users[0]
  }

  /**
   * 获取 离创建时间最接近的客户
   * @param name
   */
  public static async getCluesByName(name: string) {
    const clients = await this.getClient().request('clue/list', {
      formId: 7892940,
      conditions: [
        {
          attr: 'text_1',
          symbol: 'equal',
          value: [name]
        }
      ],
    })

    const users = (clients as any).result

    if (!users || users.list.length === 0) {
      return null
    }

    return users.list
  }

  public static async addLabel(formId: number, dataId: number, labelIds: number[]) {
    const params = {
      formId: formId,
      dataIdList: [dataId],
      labelIds: labelIds,
      businessType: formId === XbbFormId.Clue ? 8000 : 100,
      attr: formId === XbbFormId.Clue ? 'array_1' : 'array_31',
    }

    return this.getClient().request('label/batch/add', params)
  }

  public static async getLabelsByGroupId(formId: number, groupId: string) {
    return this.getClient().request('label/list', {
      formId: formId,
      groupId: groupId,
      enable: 0,
      businessType: formId === XbbFormId.Clue ? 8000 : 100,
    })
  }

  public static async getLabelGroupList(formId: number) {
    return this.getClient().request('label/group/list', {
      formId: formId,
      businessType: formId === XbbFormId.Clue ? 8000 : 100,
    })
  }

  static async getClueDetail(dataId: number) {
    return this.getClient().request('clue/detail', {
      dataId: dataId,
    })
  }

  static async getCustomerDetail(dataId: number) {
    return this.getClient().request('customer/detail', {
      dataId: dataId,
    })
  }

  static async createCustomer(param: IXbbCustomer) {
    return this.getClient().request('customer/add', {
      formId: 7713636,
      dataList: {
        ...param,
        text_35: 'C端客户',
        date_2: 0, // 对接时间，默认为 0
        date_4: Math.floor(Date.now() / 1000), // 创建时间戳, Unix 秒为单位
        coUserId: [param.text_16], // 初筛人员
        num_3: 1, // 是否公海，默认为 1
        num_4: 2, // 是否归档，默认为 2（未归档）
        array_31: [], // 标签
      },
    })
  }

  static async editClue(dataId: number, dataList: Record<string, any>) {
    return this.getClient().request('clue/edit', {
      dataId: dataId,
      dataList: dataList,
    })
  }

  static async explainForm(formId: XbbFormId) {
    return this.getClient().request('form/get', {
      formId: formId,
    })
  }
}