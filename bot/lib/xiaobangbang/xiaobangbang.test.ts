import XbbClient from 'xbb-api'
import { IXbb<PERSON><PERSON><PERSON>, XbbFormId, XbbTag, XiaoBangBangAPI } from './xiaobangbang'

import { ClueStatus, XbbHelper } from '../../service/baoshu/components/flow/nodes/helper/xbbHelper'
import { ChatStatStoreManager } from '../../service/baoshu/storage/chat_state_store'
import { Config } from '../../config/config'
import { loadConfigByWxId } from '../../../test/helper'

describe('Test', function () {
  beforeAll(() => {
    Config.setting.wechatConfig = {
      orgToken: 'e0d70927040a4efa92b79b7279ecb1c1',
      id: '1688855135509802',
      botUserId: 'momoLiaoLiuXue',
      name: '支持组2(暴叔)',
      notifyGroupId: 'R:10840704064745792',
      counselorIds: [],
      xbbId: 'woOGQmBgAAmu1IiKTDug0L26h6RnLAMQ'
    }
  })

  it('fk', async () => {
    console.log(await Xiao<PERSON>angBangAPI.getMemberIdByName('AFM4'))
  }, 60000)

  it('all members', async () => {
    console.log(JSON.stringify(await XiaoBangBangAPI.getAllMembers(), null, 4))
  }, 60000)

  it('should pass', async () => {
    const client = new XbbClient({
      token: '7106b6711bea64cd955e7c338d2865e8',
      corpId: 'wpOGQmBgAAe8yIUz6wrFRnOpaw46Xyvw',
      userId: 'woOGQmBqAA-liUrOYUiKRuaLH6H3f6_g',
      timeout: 10 * 1000,
      platform: 'app',
      version: 'v2'
    })

    console.log(JSON.stringify(await client.request('department/list', {
      'corpid': 'wpOGQmBgAAe8yIUz6wrFRnOpaw46Xyvw'
    }), null, 4))

    console.log(JSON.stringify(await client.request('user/list', {
      page: 1,
      pageSize: 1000,
    }), null, 4))

    console.log(JSON.stringify(await client.request('form/list', {
      'saasMark': 1,
      corpid: 'wpOGQmBgAAe8ylUz6wrFRnOpaw46Xyvw'
    }), null, 4))

    console.log(JSON.stringify(await client.request('form/get', {
      formId: XbbFormId.Clue,
      corpid: 'wpOGQmBgAAe8ylUz6wrFRnOpaw46Xyvw'
    }), null, 4))

    console.log(JSON.stringify(await client.request('form/get', {
      formId: 7713636,
      corpid: 'wpOGQmBgAAe8ylUz6wrFRnOpaw46Xyvw'
    }), null, 4))

    console.log(JSON.stringify(await client.request('label/group/list', {
      formId: XbbFormId.Clue,
      'businessType': 8000,
      corpid: 'wpOGQmBgAAe8ylUz6wrFRnOpaw46Xyvw'
    }), null, 4))

    console.log(JSON.stringify(await client.request('user/list', {
      corpid: 'wpOGQmBgAAe8ylUz6wrFRnOpaw46Xyvw',
      pageSize: 100
    }), null, 4))

    console.log(JSON.stringify(await client.request('clue/list', {
      formId: '7892940',
      corpid: 'wpOGQmBgAAe8ylUz6wrFRnOpaw46Xyvw',
      'conditions': [
        {
          'attr': 'text_1',
          'symbol': 'equal',
          'value': [
            '。'
          ]
        }
      ],
    }), null, 4))

    console.log(JSON.stringify(await client.request('label/allList', {
      formId: '7892940',
      corpid: 'wpOGQmBgAAe8ylUz6wrFRnOpaw46Xyvw',
      'businessType': 8000,
      userId: 'woOGQmBgAAReZMwNi6RCMryGA27_AOpQ',
      enable: 0
    }), null, 4))


    console.log(JSON.stringify(await client.request('label/group/list', {
      formId: '7892940',
      corpid: 'wpOGQmBgAAe8ylUz6wrFRnOpaw46Xyvw',
      businessType: 8000,
      userId: 'woOGQmBgAAReZMwNi6RCMryGA27_AOpQ',
    }), null, 4))

    console.log(JSON.stringify(await client.request('form/get', {
      formId: 7892940,
      corpid: 'wpOGQmBgAAe8ylUz6wrFRnOpaw46Xyvw'
    }), null, 4))

    // console.log(JSON.stringify(await XiaoBangBangAPI.getLabelsByGroupId('81541'), null, 4))
  }, 60000)

  it('构建标签 dict', async () => {
    // { groupName: {labelName: id}}
    const res = await XiaoBangBangAPI.getLabelGroupList(XbbFormId.Clue)
    const groups = (res as any).result.labelGroupList
    const labelsDict = {}

    for (const group of groups) {
      const labels = (await XiaoBangBangAPI.getLabelsByGroupId(XbbFormId.Customer, group.groupId) as any).result.labelList
      labelsDict[group.name] = {}
      for (const label of labels) {
        labelsDict[group.name][label.name] = label.dataId
      }
    }

    console.log(JSON.stringify(labelsDict, null, 4))

  }, 60000)

  it('labels', async () => {
    console.log(JSON.stringify(await XiaoBangBangAPI.addLabel(XbbFormId.Clue, 6116646, [
      118895
    ]), null, 4))
  }, 60000)

  it('转换用户', async () => {
    console.log(JSON.stringify(await XiaoBangBangAPI.getCluesByName('Zora'), null, 4))
  }, 60000)

  it('线索详情', async () => {
    console.log(JSON.stringify(await XiaoBangBangAPI.getClueDetail(6116646), null, 4))
  }, 60000)

  it('客户详情', async () => {
    // console.log(JSON.stringify(await XiaoBangBangAPI.getLastCustomerByName('jungkook'), null, 4))

    console.log(JSON.stringify(await XiaoBangBangAPI.getCustomerDetail(65769653), null, 4))
  }, 60000)

  it('全流程', async () => {
    // 拆分为两个步骤，1. 线索打标签 2. 创建客户并打标签

    // 打标签
    const userName = 'SYQ'
    const tags = [{ group:'初筛销售', name: 'AFM组' }]

    const xbbUser = await XiaoBangBangAPI.getCluesByName(userName)
    if (!xbbUser) {
      throw new Error(`xbb 找不到用户：${ userName}`)
    }

    const xbbUserId = xbbUser.dataId
    const tagIds = XbbHelper.getTagIdsByTagNames(tags)

    await XiaoBangBangAPI.addLabel(XbbFormId.Clue, xbbUserId, tagIds)

    // 转换客户
    // TODO 填入客户字段
    const customerInfo: IXbbCustomer = {
      'text_36': '0730 AI 建群',
      'text_16': 'woOGQmBgAA21FvlVvohZ1RPJWdsZkFgw',
      'text_54': '已拉咨询群',
      'ownerId': ['woOGQmBgAAi3qA-7gxhr3zPGLjQw_pEw'],
      'text_53': '其他',
      'text_52': '其他',
      'text_1': 'AI 测试账号',
      'text_50': '学生'
    }

    // 创建客户
    const customer = await XiaoBangBangAPI.createCustomer(customerInfo)

    if (!(customer as any).dataId) {
      throw new Error('转换客户失败')
    }
    const customerTagIds = XbbHelper.getTagIdsByTagNames(tags)

    // 更新标签
    await XiaoBangBangAPI.addLabel(XbbFormId.Customer, (customer as any).dataId, customerTagIds)
    // 标记为无效客户

  }, 60000)

  it('创建客户', async () => {
    // console.log(JSON.stringify(await XiaoBangBangAPI.createCustomer({
    //   'text_36': '0730 AI 建群',
    //   'text_16': 'woOGQmBgAA21FvlVvohZ1RPJWdsZkFgw',
    //   'text_54': '已拉咨询群',
    //   'ownerId': ['woOGQmBgAAi3qA-7gxhr3zPGLjQw_pEw'],
    //   'text_53': '其他',
    //   'text_52': '其他',
    //   'text_1': 'AI 测试账号',
    //   'text_50': '学生'
    // }), null, 4))

    // 更新标签
    console.log(JSON.stringify(await XiaoBangBangAPI.addLabel(XbbFormId.Customer, 65769653, [
      118969,
      119027,
      119040,
      119042
    ]), null, 4))

    console.log(JSON.stringify(await XiaoBangBangAPI.getLastCustomerByName('AI 测试账号'), null, 4))
  }, 60000)

  it('1231231', async () => {
    const xbbUser = await XiaoBangBangAPI.getCluesByName('SYQ')
    console.log(JSON.stringify(xbbUser, null, 4))
  }, 60000)


  it('修改状态', async () => {
    let xbbUser = await XiaoBangBangAPI.getCluesByName('SYQ')
    console.log(JSON.stringify(xbbUser.data.text_5, null, 4))

    if (!xbbUser) {
      throw new Error('xbb 找不到用户')
    }

    const xbbUserId = xbbUser.dataId

    const res = await XiaoBangBangAPI.editClue(xbbUserId,   {
      text_5: ClueStatus.ConvertedToCustomer
    })
    if ((res as any).code !== 1) {
      throw new Error(`修改线索状态失败:${JSON.stringify(res)}`)
    }

    xbbUser = await XiaoBangBangAPI.getCluesByName('SYQ')
    console.log(JSON.stringify(xbbUser.data.text_5, null, 4))
  }, 60000)

  it('ccc', async () => {
    Config.setting.wechatConfig = await loadConfigByWxId('1688855043510357')


    await ChatStatStoreManager.initState('7881300201052267_1688855043510357')

    await XbbHelper.labelClue('7881300201052267_1688855043510357', '7881300201052267') // 异步提高性能

    // console.log(JSON.stringify(, null, 4))

  }, 60000)
})

