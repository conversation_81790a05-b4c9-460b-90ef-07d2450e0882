import { LRUCache } from 'lru-cache'

type SyncFunction = (...args: any[]) => any
type AsyncFunction = (...args: any[]) => Promise<any>


export class CacheDecorator {
  public static cachePool: Map<string,  LRUCache<string, any>> | undefined // 用于管理函数缓存

  public static clear(func: AsyncFunction | SyncFunction) {
    if (!CacheDecorator.cachePool) {
      return
    }

    if (CacheDecorator.cachePool.has(func.name)) {
      CacheDecorator.cachePool.delete(func.name)
    }
  }

  public static clearAll () {
    this.cachePool = undefined
  }


  public static printCachePoolSize() {
    if (!CacheDecorator.cachePool) {
      return
    }

    let size = 0
    CacheDecorator.cachePool.forEach((cache) => {
      size += cache.size
    })

    console.log('cache size:', size)
  }

  public static hash(args: any[]) {
    // if (!args.length) {
    //     throw Error('must has arguments')
    // }

    return JSON.stringify(args)
  }

  public static cacheSet(funcName: string, key: string, value: any) {
    if (!CacheDecorator.cachePool) {
      CacheDecorator.cachePool = new Map()
    }

    if (!CacheDecorator.cachePool.has(funcName)) {
      CacheDecorator.cachePool.set(funcName,  new LRUCache<string, any>({ max: 3000 }))
    }

    const cache = CacheDecorator.cachePool.get(funcName)
    if (cache) {
      cache.set(key, value)
    }
  }

  public static cacheGet(funcName: string, key: string) {
    if (!CacheDecorator.cachePool) {
      return
    }

    if (!CacheDecorator.cachePool.has(funcName)) {
      return
    }

    const cache = CacheDecorator.cachePool.get(funcName)
    if (cache) {
      return cache.get(key)
    }
  }

  public static cacheHas(funcName: string, key: string) {
    if (!CacheDecorator.cachePool) {
      return false
    }

    if (!CacheDecorator.cachePool.has(funcName)) {
      return false
    }

    const cache = CacheDecorator.cachePool.get(funcName)
    if (cache) {
      return cache.has(key)
    }
    return false
  }

  // TODO decorate share memory, 一个查询数组，一个查询单个元素的函数, 启用共享缓存。


  public static decorateAsync(func: AsyncFunction) {
    const returnFunc =  async function (...args: any[]) { // 匿名函数 this 指向最终调用者
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      const self = this

      const key = CacheDecorator.hash(args) // 伪数组

      if (CacheDecorator.cacheHas(func.name, key)) {
        console.log('use cache', func.name, key)
        return CacheDecorator.cacheGet(func.name, key)
      }


      const result = await func.apply(self, args) // 现在 "this" 指向调用的对象
      if (result) { // 不 cache null, undefined
        CacheDecorator.cacheSet(func.name, key, result)
      }

      return result
    }

    Object.defineProperty(returnFunc, 'name', { value: func.name }) // 保留函数名

    return returnFunc
  }


  public static decorateSync(func: SyncFunction) {

    const cache = new Map()
    return function (...args: any[]) { // 匿名函数 this 指向最终调用者
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      const self = this

      const key = CacheDecorator.hash(args) // 伪数组

      if (cache.has(key)) {
        // console.log('get from cache:', key, func.name)
        return cache.get(key)
      }


      const result = func.apply(self, args) // 现在 "this" 指向调用的对象
      cache.set(key, result)
      return result
    }
  }
}