import axios from 'axios'
import { Config } from '../../config/config'
import { CreateGroupParams, IWecomMessage } from './type'
import { UUID } from '../uuid/uuid'
import { JuZiWecomClient } from './client'

import { getChatId } from '../../config/chat_id'
import logger from '../../model/logger/logger'

interface ICommonResponse {
  errcode: number
  errmsg: string
  data: any
}

interface IWxIdToExternalIdResponse {
  errcode: number
  errmsg: string
  externalUserId: string
}

interface GetGroupDetailParams {
  imBotId: string
  imRoomId: string
}

interface IExternalIdToWxIdResponse {
  errcode: number
  errmsg: string
  wxid: string
}

interface ICreateRoomResponse {
    errcode: number
    errmsg: string
    data: {
        chatId: string
        roomWxid: string
    }
}

interface IGetCustomerInfoResponse {
  errcode: number
  errmsg: string
  data: {
    imContactId: string
    name: string
    avatar: string
    gender: string
    remark: string
    createTimestamp: string
    remarkMobiles: number[]
    imInfo: any
    botInfo: any
    systemTags?: any
  }
}

interface AddToGroupParams {
  botUserId: string
  contactWxid: string // 客户的 imContactId
  roomWxid: string // 群聊的 wxId
}

interface IJuziSendMsgParam {
  imBotId: string
  imRoomId?: string
  imContactId?: string
  msg: IWecomMessage
}

interface IChangeRoomNameParam {
  imRoomId: string     // The ID of the room to be renamed
  imBotId: string      // The ID of the bot responsible for the room
  name: string         // The new name for the room
}

interface IGroupInfoData {
  errcode: number // 错误码
  errmsg: string // 错误原因
  data: {
    wecomChatId: string // 群聊系统id
    name: string // 群名称
    owner: string // 群主系统id
    createTime: number // 群创建时间
    memberList: Array<{
      type?: number // 群成员类型 (微信、企微)
      imContactId?: string // 群成员系统id
      joinTime?: number // 加入群聊时间
      joinScene?: number // 入群方式
      externalUserId?: string // 群成员企微联系人id
      identity?: 1 | 2 // 群成员身份 (枚举值)
      nickName?: string // 昵称
      avatarUrl?: string // 用户头像
    }>
    chatAvatar: string // 群头像
    imRoomId: string // 系统群聊ID
    systemTag?: Array<{
      // 群自定义标签
      [key: string]: any
    }>
    notice?: string // 群公告
  }
}

export class JuziAPI {
  public static token = Config.setting.juziWecom.token

  public static async getOriginalImage(chatId: string, messageId: string) {
    const url = 'https://aa-api.ddregion.com/message/getArtworkImage'

    try {
      const response = await axios.post(url, {
        token: Config.setting.wechatConfig?.orgToken as string,
        chatId,
        messageId
      })

      return response.data
    } catch (error) {
      console.error('请求失败:', error)
      throw error
    }
  }

  public static async pullChatHistory(date: string, pageSize: string, seq?: string) {
    let url = `https://aa-api.ddregion.com/message/history?token=${this.token}&snapshotDay=${date}&pageSize=${pageSize}`

    if (seq) {
      url += `&seq=${seq}`
    }

    try {
      const response = await axios.get(url)
      return response.data
    } catch (error) {
      console.error('请求失败:', error)
      throw error
    }
  }

  /**
   * 查询群详情
   */
  public static async groupDetail(params: GetGroupDetailParams) {
    let url = 'https://aa-hub.ddregion.com/api/v2/groupChat/detail'
    url += `?token=${this.token}`

    try {
      const response = await axios.get<IGroupInfoData>(url, {
        params: params
      })
      return response.data
    } catch (error) {
      console.error('请求失败:', error)
      throw error
    }
  }

  public static async createRoom(createGroupParams: CreateGroupParams) {
    let url = 'https://aa-hub.ddregion.com/api/v1/instantReply/createRoom'
    url += `?token=${this.token}`

    try {
      const response = await axios.post<ICreateRoomResponse>(url, createGroupParams)
      if (response.data && response.data.errcode === 0) {
        return response.data
      } else {
        throw new Error(response.data.errmsg)
      }
    } catch (error) {
      console.error('请求失败:', error)
      throw error
    }
  }

  public static async addToGroup(params: AddToGroupParams) {
    let url = 'https://aa-hub.ddregion.com/api/v1/instantReply/addFromRoom'
    url += `?token=${this.token}`

    try {
      const response = await axios.post(url, params)
      return response.data
    } catch (error) {
      console.error('请求失败:', error)
      throw error
    }
  }


  public static async listCustomers() {
    const url = 'v2/customer/list'
    const client = new JuZiWecomClient()
    return await client.get(url, {
      current: 1,
      pageSize: 1000
    })
  }

  public static async sendMsg(param: IJuziSendMsgParam) {
    if (!param.imContactId && !param.imRoomId) {
      throw new Error('imContactId 和 imRoomId 不能同时为空')
    }

    const imBotId = param.imBotId
    const externalRequestId = UUID.short()
    const url = 'v2/message/send'
    const messageType = param.msg.type

    return await new JuZiWecomClient().post(url, {
      imBotId,
      externalRequestId,
      imContactId: param.imContactId,
      imRoomId: param.imRoomId,
      messageType: messageType,
      payload: param.msg
    })
  }


  public static async changeRoomName(param: IChangeRoomNameParam) {
    if (!param.imRoomId || !param.imBotId || !param.name) {
      throw new Error('token, imRoomId, imBotId, and name are required')
    }

    const url = 'v1/instantReply/changeRoomName'  // The API URL for changing room name

    try {
      return await new JuZiWecomClient().post(url,  {
        imRoomId: param.imRoomId,
        imBotId: param.imBotId,
        name: param.name
      })
    } catch (error) {
      console.error('Error changing room name:', error)
      throw new Error('Failed to change room name')
    }
  }


  public static async sendGroupMsg(from: string, groupId: string, msg: IWecomMessage) {
    const imBotId = from
    const externalRequestId = UUID.short()
    const url = 'v2/message/send'
    const messageType = msg.type

    return await new JuZiWecomClient().post(url, {
      imBotId,
      externalRequestId,
      imRoomId: groupId,
      messageType: messageType,
      payload: msg
    })
  }

  static async externalIdToWxId(externalUserId: string) {
    const url = 'v1/customer/externalUserId_to_wxid'

    const client = new JuZiWecomClient()
    const response = await client.get<IExternalIdToWxIdResponse>(url, {
      externalUserId: externalUserId
    })

    if (response.data && response.data.errcode === 0) {
      return response.data.wxid
    }

    logger.error('externalIdToWxId', getChatId(externalUserId), JSON.stringify(response))

    return null
  }

  static async getMembersList(current: number, pageSize: number) {
    const url = 'v1/user/list'
    const client = new JuZiWecomClient()
    const response = await client.get<ICommonResponse >(url, {
      current,
      pageSize
    })

    if (response.data && response.data.errcode === 0) {
      return response.data.data
    }

    return null
  }

  static async getCustomerInfo(botId: string, customerId: string) {
    const url = 'v2/customer/detail'
    const client = new JuZiWecomClient()
    const response = await client.post<IGetCustomerInfoResponse>(url, {
      systemData: {
        imBotId: botId,
        imContactId: customerId
      }
    })

    if (response.data && response.data.errcode === 0) {
      return response.data.data
    }

    logger.error('getCustomerInfo', getChatId(customerId), JSON.stringify(response))

    return null
  }

  static async getCustomerInfoByExternalId(botUserId: string, customerId: string) {
    const url = 'v2/customer/detail'
    const client = new JuZiWecomClient()
    const response = await client.post<IGetCustomerInfoResponse>(url, {
      wecomData: {
        wecomUserId: botUserId,
        externalUserId: customerId
      }
    })

    if (response.data && response.data.errcode === 0) {
      return response.data.data
    }

    logger.error('getCustomerInfo', getChatId(customerId), JSON.stringify(response))

    return null
  }

  static async getMemberInfo(customerId: string) {
    const url = 'v2/user/detail'
    const client = new JuZiWecomClient()
    const response = await client.get<IGetCustomerInfoResponse>(url,
      {
        uid: customerId
      }
    )

    if (response.data && response.data.errcode === 0) {
      return response.data.data
    }

    logger.error('getMemberInfo', getChatId(customerId), JSON.stringify(response))

    return null
  }

  static async wxIdToExternalUserId(user_id: string) {
    const url = 'v1/customer/wxid_to_externalUserId'

    const client = new JuZiWecomClient()
    const response = await client.get<IWxIdToExternalIdResponse>(url, {
      wxid: user_id
    })

    if (response.data && response.data.errcode === 0) {
      return response.data.externalUserId
    }

    logger.error('wxIdToExternalId', getChatId(user_id), JSON.stringify(response))

    return null
  }

  static async updateUserAlias(botId: string, userId: string, newAlias: string) {
    const url = 'v2/customer/updateContactCustomerName'

    const client = new JuZiWecomClient()
    const response = await client.post<ICommonResponse>(url, {
      imBotId: botId,
      imContactId: userId,
      customerName: newAlias
    })

    if (response.data && response.data.errcode === 0) {
      return response.data
    }

    logger.error('updateUserAlias', getChatId(userId), JSON.stringify(response))

    return null
  }

  static async getTags() {
    const url = 'v2/tag/list'
    const client = new JuZiWecomClient()
    const response = await client.get<ICommonResponse>(url, {})

    if (response.data && response.data.errcode === 0) {
      return response.data.data
    }

    return null
  }

  /**
   * https://apifox.com/apidoc/shared-71b1c1e2-7473-4473-9e25-d2091b22199e/api-147790387
   * @param externalUserId
   * @param botUserId
   * @param addTags 标签 ID
   * @param removeTags 标签 ID
   */
  static async updateUserTags(externalUserId: string, botUserId: string, addTags: string[], removeTags: string[]) {
    const url = 'v2/tag/mark'

    const client = new JuZiWecomClient()
    const response = await client.post<ICommonResponse>(url, {
      externalUserId,
      botUserId: botUserId,
      addTagId: addTags,
      removeTagId: removeTags
    })

    if (response.data && response.data.errcode === 0) {
      return response.data
    }

    logger.error(getChatId(externalUserId), JSON.stringify(response))

    return null
  }

  static async listRoom(imBotId: string) {
    const url = 'v2/groupChat/list'
    const client = new JuZiWecomClient()
    const response = await client.get<ICommonResponse>(url, {
      current: 1,
      pageSize: 100,
      imBotId
    })

    if (response.data && response.data.errcode === 0) {
      return response.data.data
    }

    return null
  }

  static async listGroup(imBotId: string) {
    const url = 'v2/groupChat/list'
    const client = new JuZiWecomClient()
    const response = await client.get<ICommonResponse>(url, {
      current: 1,
      pageSize: 100,
      imBotId
    })

    if (response.data && response.data.errcode === 0) {
      return response.data.data
    }

    return null
  }
}