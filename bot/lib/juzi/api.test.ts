import { JuziAP<PERSON> } from './api'
import { IWecomMsgType } from './type'
import { Config } from '../../config/config'
import { WechatMessageSender } from '../../service/baoshu/components/message_send'
import { GroupNotification } from '../../service/baoshu/notification/group'
import { loadConfigByAccountName } from '../../../test/helper'

describe('Test', function () {
  beforeAll(() => {
    Config.setting.wechatConfig =   {
      orgToken: 'e0d70927040a4efa92b79b7279ecb1c1',
      id: '****************',
      botUserId: 'BaoShuXiaoZhuShou_3',
      name: '暴叔',
      counselorIds: ['Ming'],
      notifyGroupId: 'R:*****************'
    }
  })

  it('获取群列表', async () => {
    // console.log(JSON.stringify(await <PERSON><PERSON><PERSON><PERSON>., null, 4))
  }, 60000)

  it('获取聊天记录', async () => {
    console.log(JSON.stringify(await JuziAPI.pullChatHistory('2024-04-15', '10', '1'), null, 4))
  })

  it('遍历三个账号获取对应的需要的群成员', async () => {
    const ids = ['****************', '****************', '****************']
    const counselorIds = []

    for (const id of ids) {
      const rooms =  await JuziAPI.listGroup(id)

      for (const room of rooms) {
        console.log(room.name, room.imRoomId, room.owner === id, room.owner, JSON.stringify(room.memberList, null, 4))
      }
    }


  }, 60000)

  it('拉群', async () => {
    // const { data } = await JuziAPI.createRoom({
    //   botUserId: 'ShengYueQing',
    //   userIds: ['MaiZi'],
    //   name: '@测试',
    //   greeting: '@测试',
    //   externalUserIds: ['wm41XZJQAAraXYGQlIcWplXS58GTqrew']
    // })

    // 1. 获取群内 imContactId
    // 2. 发送 @ 消息

    const ids = [Config.setting.wechatConfig?.id as string]

    // 企微用户，去重
    // const enterPrisePersonIds = new Set()

    // for (const id of ids) {
    //   const rooms =  await JuziAPI.listGroup(id)
    //
    //   for (const room of rooms) {
    //     console.log(room.name, room.imRoomId, room.owner === id, room.owner, JSON.stringify(room.memberList, null, 4))
    //   }
    // }

    // @ 测试
    await JuziAPI.sendMsg({
      imBotId: '****************',
      imRoomId:  'R:10925260921704120',
      msg: {
        type: IWecomMsgType.Text,
        text: 'hi',
        mention: ['MaiZi']
      }
    })

  }, 30000)

  it('获取客户列表', async () => {
    console.log(JSON.stringify(await JuziAPI.listCustomers(), null, 4))
  }, 30000)

  // it('发送消息', async () => {
  //   console.log(JSON.stringify(await JuziAPI.sendMsg('****************', 'Ming',  {
  //     type: IWecomMsgType.Emoticon,
  //     imageUrl: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/0516bf9c808dce3297b345f1d93150d5.jpg'
  //   }), null, 4))
  // }, 30000)
  //
  // it('发送消息2', async () => {
  //   console.log(JSON.stringify(await JuziAPI.sendMsg('****************', 'Ming',  {
  //     type: IWecomMsgType.Text,
  //     text: '测试消息'
  //   }), null, 4))
  // }, 30000)

  it('发送群消息', async () => {
    await GroupNotification.notify('测试群消息')
  }, 60000)

  it('测试企业账号 externalId to wxId', async () => {
    Config.setting.wechatConfig = await loadConfigByAccountName('欢乐斗地主AFM-ada')

    // 群 @ 测试

    // {
    //   "type": 3,
    //     "imContactId": "****************",
    //     "joinTime": **********,
    //     "joinScene": 1,
    //     "externalUserId": "HuRong_1",
    //     "identity": 1,
    //     "nickName": "胡蓉",
    //     "avatarUrl": "https://wework.qpic.cn/wwpic3az/271458_xjY7CKqbSmuOECA_1705761360/0",
    //     "corpName": "AFYEDU"
    // }

    console.log(JSON.stringify(await JuziAPI.getCustomerInfoByExternalId(Config.setting.wechatConfig.id, '****************'), null, 4))

    // console.log(JSON.stringify(await JuziAPI.externalIdToWxId('HuRong_1'), null, 4))
  }, 60000)

  it('externalId To UserId', async () => {
    console.log(JSON.stringify(await JuziAPI.externalIdToWxId('LaoYuLei'), null, 4))
  }, 30000)

  it('queryCustomerInfo', async () => {
    console.log(JSON.stringify(await JuziAPI.getCustomerInfo('****************', 'ShengYueQing'), null, 4))
  }, 30000)

  it('queryMemberInfo', async () => {
    console.log(JSON.stringify(await JuziAPI.getCustomerInfoByExternalId('horus', ''), null, 4))
  }, 60000)

  it('wxId to externalId', async () => {
    console.log(JSON.stringify(await JuziAPI.wxIdToExternalUserId('7881300846030208'), null, 4))
  }, 30000)

  it('测试', async () => {
    await WechatMessageSender.sendById({
      chat_id: '',
      user_id: '7881300846030208',
      ai_msg: '',
      send_msg: {
        type: IWecomMsgType.Emoticon,
        imageUrl: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/0516bf9c808dce3297b345f1d93150d5.jpg'
      },
    })
  }, 30000)

  it('111', async () => {
    console.log(JSON.stringify(await JuziAPI.getCustomerInfo('1688857006556549', '7881300846030208'), null, 4))
  }, 60000)

  it('修改备注', async () => {
    // await JuziAPI.updateUserAlias(Config.setting.wechatConfig?.id as string, '7881301680965389', '测试')
    // 7881301175955392_****************

    console.log(JSON.stringify(await JuziAPI.getCustomerInfo('****************1', '7881301175955392'), null, 4))
  }, 60000)

  it('获取成员列表', async () => {
    console.log(JSON.stringify(await JuziAPI.getMembersList(1, 1000), null, 4))
  }, 30000)

  it('标签获取', async () => {
    // console.log(JSON.stringify(await JuziAPI.getTags()))
    console.log(JSON.stringify(await JuziAPI.listRoom('****************')))
  }, 60000)

  it('获取群详情', async () => {
    console.log(JSON.stringify(await JuziAPI.groupDetail({
      imBotId: '****************',
      imRoomId: 'R:20887857035114'
    }), null, 4))
  }, 60000)

})