import { StringHelper } from './index'

describe('字符串类型判断测试', function () {
  it('IsNumber', function () {
    expect(StringHelper.isNumber('123')).toBe(true)
    expect(StringHelper.isNumber('123.123')).toBe(true)
    expect(StringHelper.isNumber('123.123.123')).toBe(false)
    expect(StringHelper.isNumber('0')).toBe(true)
    expect(StringHelper.isNumber('-123')).toBe(true)
    expect(StringHelper.isNumber('-123.123')).toBe(true)

    expect(StringHelper.isNumber('01')).toBe(false)
    expect(StringHelper.isNumber('')).toBe(false)
    expect(StringHelper.isNumber('abc')).toBe(false)
    expect(StringHelper.isNumber(' ')).toBe(false)
  })

  it('IsBoolean', function () {
    expect(StringHelper.isBoolean('true')).toBe(true)
    expect(StringHelper.isBoolean('false')).toBe(true)
    expect(StringHelper.isBoolean('yes')).toBe(true)
    expect(StringHelper.isBoolean('no')).toBe(true)
    expect(StringHelper.isBoolean('True')).toBe(true)
    expect(StringHelper.isBoolean('False')).toBe(true)
    expect(StringHelper.isBoolean('Yes')).toBe(true)
    expect(StringHelper.isBoolean('No')).toBe(true)

    expect(StringHelper.isBoolean('')).toBe(false)
    expect(StringHelper.isBoolean('1')).toBe(false)

    expect(StringHelper.isBoolean('True ')).toBe(false)
    expect(StringHelper.isBoolean('False ')).toBe(false)
  })

  it('IsEmail', function () {
    expect(StringHelper.isEmail('<EMAIL>')).toBe(true)
    expect(StringHelper.isEmail('<EMAIL>')).toBe(true)
    expect(StringHelper.isEmail('<EMAIL>')).toBe(true)
    expect(StringHelper.isEmail('<EMAIL>')).toBe(true)
    expect(StringHelper.isEmail('<EMAIL>')).toBe(true)
    expect(StringHelper.isEmail('<EMAIL>'))
    expect(StringHelper.isEmail('<EMAIL>'))

    expect(StringHelper.isEmail('12312')).toBe(false)
    expect(StringHelper.isEmail('123@')).toBe(false)
    expect(StringHelper.isEmail('@')).toBe(false)
    expect(StringHelper.isEmail('@123')).toBe(false)
    expect(StringHelper.isEmail('@123.123')).toBe(false)
    expect(StringHelper.isEmail('.com')).toBe(false)
    expect(StringHelper.isEmail('.')).toBe(false)
    expect(StringHelper.isEmail('')).toBe(false)
  })

  it('isPhone', async () => {
    expect(StringHelper.isPhone('13853505679')).toBe(true)
    expect(StringHelper.isPhone('2265056867')).toBe(true)
    expect(StringHelper.isPhone('+12265056867')).toBe(true)
    expect(StringHelper.isPhone('+14346022109')).toBe(true)

    expect(StringHelper.isPhone('12345.1')).toBe(false)
    expect(StringHelper.isPhone('')).toBe(false)
  })

  it('isUrl', async () => {
    expect(StringHelper.isUrl('https://www.baidu.com')).toBe(true)
    expect(StringHelper.isUrl('http://www.baidu.com')).toBe(true)
    expect(StringHelper.isUrl('https://www.baidu.com/')).toBe(true)
    expect(StringHelper.isUrl('http://www.baidu.com/')).toBe(true)
    expect(StringHelper.isUrl('https://www.baidu.com/index.html')).toBe(true)
    expect(StringHelper.isUrl('http://www.baidu.com/index.html')).toBe(true)
    expect(StringHelper.isUrl('https://www.baidu.com/index.html?a=1&b=2')).toBe(true)
    expect(StringHelper.isUrl('http://www.baidu.com/index.html?a=1&b=2')).toBe(true)
    expect(StringHelper.isUrl('https://www.baidu.com/index.html?a=1&b=2#a')).toBe(true)
    expect(StringHelper.isUrl('http://www.baidu.com/index.html?a=1&b=2#a')).toBe(true)
    expect(StringHelper.isUrl('https://www.notion.so/46e1512e0b904f78b2a24e8f1d3fa769')).toBe(true)

    expect(StringHelper.isUrl('')).toBe(false)
    expect(StringHelper.isUrl('123')).toBe(false)
    expect(StringHelper.isUrl('123.123')).toBe(false)
    expect(StringHelper.isUrl('123.123.123')).toBe(false)
  })

  it('isDate', async () => {
    expect(StringHelper.isDate('2019-01-01')).toBe(true)
    expect(StringHelper.isDate('2019-01-01 00:00:00')).toBe(true)
    expect(StringHelper.isDate('2019-01-01 00:00:00.000')).toBe(true)
    expect(StringHelper.isDate('2019-01-01 00:00:00.000Z')).toBe(true)
    expect(StringHelper.isDate('2019-01-01 00:00:00.000+0800')).toBe(true)
    expect(StringHelper.isDate('2019-01-01 00:00:00.000+08:00')).toBe(true)
    expect(StringHelper.isDate('2019-01-01 00:00:00.000+0800')).toBe(true)
    expect(StringHelper.isDate('2019-01-01 00:00:00.000+08:00')).toBe(true)
    expect(StringHelper.isDate('2019-01-01 00:00:00.000+08')).toBe(true)
    expect(StringHelper.isDate('01 Jan 1970 00:00:00 GMT')).toBe(true)

    expect(StringHelper.isDate('')).toBe(false)
    expect(StringHelper.isDate('12321')).toBe(false)
    expect(StringHelper.isDate('01')).toBe(false)
  })

  it('chineseDate', async () => {
    expect(StringHelper.isChineseDate('1904年1月1日')).toBe(true)
    expect(StringHelper.isChineseDate('1904 年 1 月 1 日')).toBe(true)
    expect(StringHelper.isChineseDate('1904年 1 月 1 日')).toBe(true)
    console.log(StringHelper.convertChineseDateToDate('1904年1月1日'))
    console.log(StringHelper.convertChineseDateToDate('1904 年 1 月 1 日'))
  }, 30000)

  it('千分位', async () => {
    expect(StringHelper.isThousandth('1,123,123')).toBe(true)
    expect(StringHelper.isThousandth('123')).toBe(true)
    expect(StringHelper.isThousandth('1,123')).toBe(true)
    expect(StringHelper.isThousandth('9999,999')).toBe(true)
    expect(StringHelper.isThousandth('9999,9999')).toBe(false)
  }, 30000)

  it('百分号', async () => {
    expect(StringHelper.isPercent('1%')).toBe(true)
    expect(StringHelper.isPercent('1%1')).toBe(false)
    expect(StringHelper.isPercent('10.1%')).toBe(true)
    expect(StringHelper.isPercent('123123213%')).toBe(true)
  }, 30000)

  it('英文', async () => {
    const isFullEnglishAndChinese = (text: string) => {
      return /^([\p{sc=Han}\p{P}A-Za-z0-9\s])*$/u.test(text)
    }

    console.log(isFullEnglishAndChinese('08.ÃæÏò¶ÔÏó±à³Ì»ù´¡.md'))
  }, 30000)
})

describe('测试随机字符串 getRandomString', () => {
  it('应该具有默认参数', () => {
    let error = false
    try {
      StringHelper.getRandomString()
    } catch {
      error = true
    }
    expect(error).toBe(false)
  })

  it('默认参数为 6', () => {
    const str = StringHelper.getRandomString()
    expect(str?.length).toBe(6)
  })

  it('应该返回指定长度的字符串', () => {
    const str = StringHelper.getRandomString(10)
    expect(str.length).toBe(10)
  })

  it('每次返回的字符串应不相同', () => {
    const strA = StringHelper.getRandomString(10)
    const strB = StringHelper.getRandomString(10)
    expect(strA).not.toBe(strB)
  })

  it('替换多个空行为1个', async () => {
    const text = `这是第一行。

  
  
    
这是第二行。

这是第三行。`

    const result = StringHelper.replaceMultipleBlankLines(text)
    console.log(result)
  }, 60000)
})
