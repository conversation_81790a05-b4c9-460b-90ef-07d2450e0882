interface DelayOptions {
    s?: number // 秒
    m?: number // 分钟
    h?: number // 小时
}

export class DelayedTask {
  private delay: number
  private task: () => Promise<any>
  private condition: () => Promise<boolean>

  private timeoutId: NodeJS.Timeout | null

  constructor(delay: number | DelayOptions, task: () => Promise<any>, condition: () => Promise<boolean>) {
    if (typeof delay === 'number') {
      this.delay = delay * 1000 // Convert to milliseconds
    } else {
      const { s, m, h, } = delay
      if (s !== undefined) this.delay += s * 1000
      if (m !== undefined) this.delay += m * 60 * 1000
      if (h !== undefined) this.delay += h * 60 * 60 * 1000
    }

    this.task = task
    this.condition = condition

    this.timeoutId = null
  }

  start() {
    this.scheduleTask()
  }

  private scheduleTask() {
    this.timeoutId = setTimeout(async () => {
      try {
        if (await this.condition()) {
          await this.task()
        } else {
          console.log('Task cancelled due to unmet condition.')
        }
      } catch (error) {
        console.error('Task execution failed:', error)
      }
    }, this.delay)
  }

  public cancel() {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId)
    }
  }
}