interface DelayOptions {
  s?: number // 秒
  m?: number // 分钟
  h?: number // 小时
  fc: () => Promise<void> // 返回要执行的异步任务的函数
}

export function delayFc(options: DelayOptions): void {
  const { s, m, h, fc } = options

  let totalDelay = 0
  if (s !== undefined) totalDelay += s * 1000
  if (m !== undefined) totalDelay += m * 60 * 1000
  if (h !== undefined) totalDelay += h * 60 * 60 * 1000

  if (totalDelay === 0) {
    throw new Error('至少需要提供一个时间单位（秒、分钟或小时）')
  }

  setTimeout(() => {
    fc()
  }, totalDelay)
}


/**
 * 延时函数
 * @param {*} ms 毫秒
 */
export async function sleep(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

export async function randomSleep(min: number, max: number): Promise<void> {
  const rand = Math.floor(Math.random() * (max - min + 1)) + min
  return new Promise((resolve) => setTimeout(resolve, rand))
}

