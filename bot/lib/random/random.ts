export class RandomHelper {

  /**
   * 根据不同 index 的权重，选取一个 index。 参考：https://leetcode.cn/problems/cuyjEf/description/
   * @param weights  w[i] 代表下标 i 的权重， 随机地获取下标 i，选取下标 i 的概率与 w[i] 成正比。
   */
  public static pickIndexByWeights(weights: number[]): number {
    const weightPrefixSums: number[] = []
    let totalWeight: number = 0

    for (const weight of weights) {
      totalWeight += weight
      weightPrefixSums.push(totalWeight)
    }

    function binarySearch(target: number): number {
      let left = 0
      let right = weightPrefixSums.length - 1

      while (left < right) {
        const mid = Math.floor((left + right) / 2)
        if (weightPrefixSums[mid] < target) {
          left = mid + 1
        } else {
          right = mid
        }
      }

      return left
    }

    const randomWeight = Math.random() * totalWeight
    return binarySearch(randomWeight)
  }
}