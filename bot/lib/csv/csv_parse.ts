import csv_parser from 'csv-parse'
import { FileHelper } from '../file'


export class CsvParser {
  public static async read(csvPath: string): Promise<string[][]> {
    const stream = await FileHelper.readFileAsStream(csvPath)

    const table: string[][] = []
    const parser = stream.pipe(csv_parser({
      bom: true,
      relax: true, // 允许单引号单独出现 allow quoted fields
      relax_column_count: true, // 允许每行列数不一致
    }))

    let maxColumns = 0
    for await (const row of parser) {
      if (row.length > maxColumns) {
        maxColumns = row.length
      }

      if (row.join('').trim() === '') {
        continue
      }

      table.push(row)
    }

    for (const row of table) {
      if (row.length < maxColumns) {
        const padding = maxColumns - row.length
        for (let i = 0; i < padding; i++) {
          row.push('')
        }
      }
    }
    return table
  }
}
