import { calculateJaccardSimilarity, cosineSimilarity } from './text_similarity'
import { OpenaiEmbedding } from './ai/llm/openai_embedding'

describe('Test', function () {
  beforeAll(() => {

  })

  it('regex', async () => {
    console.log('你必须知道的Pytorch中文教程.'.replace(/[@#￥%……&*（）——+“”‘’；：、。.]/g, ''))
  }, 30000)

  it('', async () => {
    const embedding1 =  await OpenaiEmbedding.getInstance().embedQuery('好的，那祝你学习愉快，有问题随时找我哦~')
    const embedding2 =  await OpenaiEmbedding.getInstance().embedQuery('好的，有问题随时找我哦~')

    const similarity = cosineSimilarity(embedding1, embedding2)
    console.log(similarity) // 输出余弦相似度
  }, 30000)


  it('test', async () => {
    const embedding1 =  await OpenaiEmbedding.getInstance().embedQuery('好的，有需要随时联系我哦~')
    const embedding2 =  await OpenaiEmbedding.getInstance().embedQuery('不客气，有需要再联系我哦，祝你开学顺利!')

    const similarity = cosineSimilarity(embedding1, embedding2)
    console.log(similarity) // 输出余弦相似度
  }, 30000)
})