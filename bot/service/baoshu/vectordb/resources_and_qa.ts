import path from 'path'
import { Document } from 'langchain/document'
import { OpenaiEmbedding } from '../../../lib/ai/llm/openai_embedding'
import { IMessage, IMessageType } from '../../message/message'
import { PathHelper } from '../../../lib/path'
import { FaissStore } from '@langchain/community/vectorstores/faiss'


export interface KnowledgeBlock {
    type: KnowledgeBlockType
    index_text: string // 索引文本，用于检索资料
    text_content?: string // 文本内容
    file_urls?: string[] // 文件地址
    supplement?: string // 补充说明
}

export enum KnowledgeBlockType {
    Text = 'text',
    File = 'file',
}



/**
 * 既可以作为素材召回，又可以作为问答检索
 */
export class ResourceAndQABase {
  private static _blocks: KnowledgeBlock[] = []

  public async similaritySearch(query: string, k = 1) {
    const blocks = ResourceAndQABase.blocks

    const docs = blocks.map((block) => {
      return new Document({
        pageContent: block.index_text,
        metadata: {
          meta: {
            type: block.type,
            index_text: block.index_text,
            text_content: block.text_content,
            file_urls: block.file_urls,
            supplement: block.supplement
          }
        }
      })
    })

    const vectorStore = await FaissStore.fromDocuments(docs, OpenaiEmbedding.getInstance())


    return await vectorStore.similaritySearchWithScore(query, k)
  }

  public static get blocks(): KnowledgeBlock[] {
    const resourceFolder = path.join(process.cwd(), 'resource', 'knowledge_base')
    const absPath = (relativePath: string) => path.join(resourceFolder, relativePath)

    if (this._blocks.length === 0) {
      // 构建知识库
      // 课程信息
      this._blocks.push({
        type: KnowledgeBlockType.File,
        index_text: '课程信息',
        file_urls: [absPath(path.join('course_introduction', '系统班.jpeg')), absPath(path.join('course_introduction', '训练营.jpeg'))],
        supplement: `主要包含了以下2个部分【人工智能通识系统课】＋【阶段专业能力实战进阶课】，经过组合之后可以适用于任何一个阶段和目标的同学去学习。

蓝色部分是从0到1得一个能够独立建模实战得系统课程，也就是为你发表ai+文章的相关ai基础+算法模型论文复现等内容。我们的课程是从系统环境配置，基础数理基础和编程知识，到最后实战cv/nlp方向算法模型，手把手上手。
黄色部分是，根据大家学习ai的终点站设计得三个板块得内容，现在报名系统课后可以免费选一门课，论文辅导课/kaggle指导/就业指导。

整套课程的权限是直播可以看两年＋录播（两年内更新的录播）一直可看＋答疑也是一直有的,而且这样也就可以保证你每个知识点都可以以最佳的学习方式（直播）学到，且提供录播一直给你复习学习，相应的课件和源码也都会给到你，同时还有答疑。

整的一期【全程直播】讲完人工智能蓝色实战部分的课程需要6-8个月的时间，如果你时间比较紧，可以在两个月左右看完上一期的录播，不影响学习进度。而且咱们报名之后，都会约老师给咱们做一个1v1规划，规划目标学习相关的学习路线，这样效率可能还可以再提升一些。

这样安排形式，也一定程度上解决了很多同学没办法跟直播课的问题，所以学习时间很自由，完全可以跟着自己的节奏走。`
      })

      // 促销活动
      this._blocks.push({
        type: KnowledgeBlockType.Text,
        index_text: '促销活动',
        text_content: `买原价14800元的系统课程，现在2人拼团价8480元。
且额外加赠原价6980元的kaggle比赛/AI实战就业指导/论文指导，三选一。活动有效期是3天，下定金100金额可以锁定当前的优惠权益`
      })

      // 课程价格
      this._blocks.push({
        type: KnowledgeBlockType.Text,
        index_text: '课程价格',
        text_content: '原价是8980, 现在正逢元旦，享9折优惠，今晚前下单还能再减200元。如果现在下单的话只需要支付8082元'
      })

      // 优惠
      this._blocks.push({
        type: KnowledgeBlockType.Text,
        index_text: '优惠',
        text_content: '如果这样您还是觉得太贵了，我可以去帮您申请助学金，助学金包含了1000元的优惠，您在学习过程中，只要能在半年内，完成80%以上的课程，但是名额有限，您如果现在下单的话，我帮您把这个名额定下来。'
      })

      // 试听链接
      this._blocks.push({
        type: KnowledgeBlockType.Text,
        index_text: '试听直播链接',
        text_content: 'www.zhibo.com/s/1dFgk3j2',
        supplement: '可以体验下我们的上课风格和授课质量， 这样也对我们的VIP课程更放心些。 看完之后跟我说哈 我再给你做详细介绍'
      })

      // 录播链接
      this._blocks.push({
        type: KnowledgeBlockType.Text,
        index_text: '录播链接',
        text_content: 'www.lubo.com/s/1dFgk3j2',
        supplement: '可以体验下我们的上课风格和授课质量， 这样也对我们的VIP课程更放心些。 看完之后跟我说哈 我再给你做详细介绍'
      })

      // 付款链接
      this._blocks.push({
        type: KnowledgeBlockType.Text,
        index_text: '付款链接',
        text_content: 'www.taobao.com',
        supplement: '付完款后记得发一下截图给我哦~'
      })

      // 产品优势及服务
      this._blocks.push({
        type: KnowledgeBlockType.Text,
        index_text: '产品优势及服务',
        text_content: `1.课程内容：课程内容是由斯坦福，加州大学，曼切斯特大学的师资力量，课程内容包含了人工智能的基础知识，算法模型，实战项目，论文复现等内容，是一个从0到1的系统课程。
2.1v1根据学习目标，定制学习路线图，学习效率进一步提升
3.1v1课程答疑：所有在课堂上没听明白的，都有专门的答疑群，甚至1v1视频会议回答疑惑。且后续学员永久答疑
4.配套班主任和新手村服务，一站搞定环境配置，班主任根据任务监督学习打卡，杜绝学一半因为没有监督而放下的情况。`
      })

      // 支付方式
      this._blocks.push({
        type: KnowledgeBlockType.Text,
        index_text: '支付方式',
        text_content: `1.支付保障：
淘宝官方下单，支持7天无理由退款。
2.分期付款
- 支持花呗12期免息
- 助学金名额：先付3000元，后续按6个月分期完成。`
      })

      // 分期
      this._blocks.push({
        type: KnowledgeBlockType.Text,
        index_text: '分期',
        text_content: '支持花呗12期免息。淘宝官方下单，支持7天无理由退款。'
      })

      // 退款
      this._blocks.push({
        type: KnowledgeBlockType.Text,
        index_text: '退款',
        text_content: '淘宝官方下单，支持7天无理由退款。'
      })

      // 师资力量
      this._blocks.push({
        type: KnowledgeBlockType.File,
        index_text: '师资力量 老师',
        file_urls: [absPath(path.join('teacher', 'teacher.jpeg'))],
        supplement: '目前我们的【教学团队】分为:授课老师➕答疑老师，我们的课程主讲老师有6位。这个配置应该是目前业内最强的了，基本我们的学员的问题都能被及时解答'
      })

      // 学员反馈
      this._blocks.push({
        type: KnowledgeBlockType.File,
        index_text: '学员反馈 课程反馈',
        file_urls: [
          absPath(path.join('feedback', 'feedback1.jpg')),
          absPath(path.join('feedback', 'feedback2.jpg')),
          absPath(path.join('feedback', 'feedback3.jpg'))
        ],
      })

      // 论文指导
      this._blocks.push({
        type: KnowledgeBlockType.File,
        index_text: '论文指导',
        file_urls: [absPath(path.join('course_introduction', '训练营.jpeg'))],
        supplement: `我们的论文辅导是从你选题选刊开始，到算法选择，数据，实验写作到最后投稿全流程指导和答疑，上课形式是，论文相关的直播课+答疑的方式。
但是如果同学在AI上没有什么基础，那我们就不单独售卖了。可以陪套我们的基础课程包，0-1的ai基础课程学习，一般同学报名后，我们老师会根据你的论文方向和目标，定制学习路线，这样的学习效率就比较高了。
具体可以看下我们的课程大纲：
蓝色部分是我们的基础课程，从数理和代码基础开始打基础到最后模型的应用。
黄色是指导咱们的论文服务内容，从研读论文开始，可视化，到最后发表的直播课，在写作过程全程支持代码答疑，问题解答。且我们的答疑是终身答疑的哦！
平时有任何问题可以在服务群里问，我们有个强大的助教团队在实时回复的。如果有什么特别难的问题，我们班主任老师也是可以帮助约老师1v1的腾讯会议咨询的。这个答疑我们是终生制的，就算你后续毕业工作有什么问题也都可以来问我们的哈！

这是我们普及AI知识，做好AI教育的初心。`
      })
      //上课方式和时间
      this._blocks.push({
        type: KnowledgeBlockType.Text,
        index_text: '上课时间',
        text_content: `我们上课模式是【直播+录播】的形式，大纲上的所有内容均有直播和录播提供。
 
【上课时间】
每周人工智能基础系统课程是周六、日下午4：00~5：30
机器学习的数学基础课是在周五晚上8：00~9：00
国际前沿是不定时周六下午13：30~15：00
进阶课程是每周日上午10：00~10：45
还有不定时的优秀学员针对自己擅长领域的分享，比如那个被邀请成为SCI2区审稿人的小宝也会分享相关内容。
（每周助教老师都会把提前安排好的课程发在班级群里）`
      })

      // 太贵了
      this._blocks.push({
        type: KnowledgeBlockType.Text,
        index_text: '太贵了',
        text_content: '如果这样您还是觉得太贵了，我可以去帮您申请助学金，助学金包含了1000元的优惠，您在学习过程中，只要能在半年内，完成80%以上的课程，但是名额有限，您如果现在下单的话，我帮您把这个名额定下来。'
      })

      this._blocks.push({
        type: KnowledgeBlockType.Text,
        index_text: '有些贵呀，可以再优惠些吗',
        text_content: '这个已经是最优惠的啦，我们的学员也都是学生为主，本科生和研究生，所以我们的定价及服务都是很优惠的了，性价比很高。'
      })

      this._blocks.push({
        type: KnowledgeBlockType.Text,
        index_text: '这些课程多久？都学吗？',
        text_content: '课程大纲上所有罗列的内容都会全部上，并且课程在不断更新升级。下个月会新增强化学习的专题，后续课程更新也都免费提供给学员。课程时长为6个月，适合全面学习AI的同学。'
      })

      this._blocks.push({
        type: KnowledgeBlockType.Text,
        index_text: '学了的话论文多久能发？',
        text_content: '发表论文的时间取决于研究方向、基础和进度。0基础起步可能需要数月准备加审稿周期。投稿至SCI可能5个月后见刊。'
      })

      this._blocks.push({
        type: KnowledgeBlockType.Text,
        index_text: '老师是一对一的吗? 有代码上面的疑惑都会解答？',
        text_content: '初期学习规划是一对一的，后续班级里有助教和指导老师，可解答编码问题，也可一对一会议解答。'
      })

      this._blocks.push({
        type: KnowledgeBlockType.Text,
        index_text: '答疑不及时吗？',
        text_content: '答疑非常及时，群里提问即可得到快速反馈，遇到复杂问题也可约老师一对一指导。'
      })

      this._blocks.push({
        type: KnowledgeBlockType.Text,
        index_text: '这个需要排队吗？',
        text_content: '预约过程十分灵活，当天有问题可直接联系班主任即日预约老师。'
      })

      this._blocks.push({
        type: KnowledgeBlockType.Text,
        index_text: '代码保密性好吗？',
        text_content: '完全可以放心，老师仅进行指导，不直接调试或修改代码。'
      })

      this._blocks.push({
        type: KnowledgeBlockType.Text,
        index_text: '报名之后后续怎么安排计划呢？',
        text_content: '报名后签合同，添加班主任并约老师制定学习规划，后续问题可通过班级群或一对一指导解决。'
      })

      this._blocks.push({
        type: KnowledgeBlockType.Text,
        index_text: '我是有问题直接找老师是吧?',
        text_content: '是的，有问题直接联系班主任预约一对一指导，或在群内提问。'
      })

      this._blocks.push({
        type: KnowledgeBlockType.Text,
        index_text: '课程是录播吗？',
        text_content: '我们提供直播及录播形式的课程，每周六、日上午十点进行直播。'
      })

      this._blocks.push({
        type: KnowledgeBlockType.Text,
        index_text: '那如果是比方有别的（不是人工智能）的代码方面问题可以问老师吗？',
        text_content: '老师可以针对您的问题提供指导和帮助。'
      })

      this._blocks.push({
        type: KnowledgeBlockType.Text,
        index_text: '那发文章需要带机构吗？',
        text_content: '不需要，我们的学员都是以个人名义发表的。'

        // TODO: 迁移到向量数据库
      })

    }
    return this._blocks
  }


  public static getKnowledgeBlockOutput(block: KnowledgeBlock): KnowledgeBlockOutput[]  {
    const output: KnowledgeBlockOutput[] = []

    switch (block.type) {
      case KnowledgeBlockType.Text:
        if (block.text_content) {
          output.push({
            send_msg: block.text_content,
            output_msg: block.text_content
          })
        }

        if (block.supplement) {
          output.push({
            send_msg: block.supplement,
            output_msg: block.supplement
          })
        }
        break
      case KnowledgeBlockType.File:
        if (block.file_urls) {
          block.file_urls.forEach((url) => {
            const fileExtension = PathHelper.getFileExt(url, true)

            output.push({
              send_msg: {
                type: IMessageType.Image,
                file_path: url,
              },
              bot_msg: `[[${block.index_text.trim() + fileExtension}]]`
            })
          })
        }

        if (block.supplement) {
          output.push({
            send_msg: block.supplement,
            output_msg: block.supplement
          })
        }
        break

      default:
        break
    }

    return output
  }
}

export interface KnowledgeBlockOutput {
    send_msg: string | IMessage
    bot_msg?: string // 机器人回复，用于放到机器回复聊天记录中作为占位符，文件特有，一般以 [[]] 格式，如 [[file_1]]
    output_msg?: string // 用于信息参考，除文件类型为空以外，一般与 send_msg 一致。
}