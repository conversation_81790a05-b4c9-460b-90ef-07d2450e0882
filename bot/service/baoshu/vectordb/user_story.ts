// import { FaissStore } from '@langchain/community/vectorstores/faiss'
// import { FileHelper } from '../../../lib/file'
// import path from 'path'
// import { Document } from 'langchain/document'
// import { OpenaiEmbedding } from '../../../lib/ai/llm/openai_embedding'
//
// export interface IUserStory {
//     role: string
//     goal?: string
//     field?: string
//     difficulty?: string
//     current_progress?: string
//     level_of_urgency?: string
// }
//
// export class UserStoryVectorDB {
//   private static _vectorDB: FaissStore
//
//   public async similaritySearch(query: IUserStory, k = 1) {
//     if (!UserStoryVectorDB._vectorDB) {
//       const rawData = await FileHelper.readFile(path.join(process.cwd(), 'dev', 'story.json'))
//       const data = JSON.parse(rawData)
//
//       const docs = data.map((doc) => {
//         return new Document({
//           pageContent: doc['story'],
//           metadata: {
//             meta: JSON.stringify(doc, null, 4)
//           }
//         })
//       })
//
//       UserStoryVectorDB._vectorDB = await FaissStore.fromDocuments(docs, OpenaiEmbedding.getInstance())
//
//     }
//
//     return await UserStoryVectorDB._vectorDB.similaritySearchWithScore(query.role +  query.goal ?? `${  query.field}` ?? `${  query.current_progress}` ?? `${  query.difficulty}` ?? `${  query.level_of_urgency}` ?? '', k)
//   }
// }