import { Document } from 'langchain/document'
import { OpenaiEmbedding } from '../../../lib/ai/llm/openai_embedding'
import { IVectorDB } from './base'
import { FileHelper } from '../../../lib/file'
import path from 'path'
import { VectorStore } from '@langchain/core/vectorstores'
import { Milvus } from '@langchain/community/dist/vectorstores/milvus'

export class QAVectorDB implements IVectorDB {
  private static _vectorDB: VectorStore

  public async similaritySearch(query: string, k = 3) {
    if (!QAVectorDB._vectorDB) {
      QAVectorDB._vectorDB = await Milvus.fromExistingCollection(
        OpenaiEmbedding.getInstance(),
        {
          collectionName: 'qa',
          clientConfig: {
            address: 'https://in01-d1c034bfa918872.tc-ap-beijing.vectordb.zilliz.com.cn:443',
            token: '09f45b589a5043055d36508a190de2bca084d88fcabc6384e6e0df7e6c89f9d207c8eec6701f81664203b2f597dae70ad090d5de',
            logLevel: 'info'
          }
        }
      )
    }

    return await QAVectorDB._vectorDB.similaritySearchWithScore(query, k)
  }
}