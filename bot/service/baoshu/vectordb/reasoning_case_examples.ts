import { Document } from 'langchain/document'

import { OpenaiEmbedding } from '../../../lib/ai/llm/openai_embedding'
import { IVectorDB } from './base'
import { FaissStore } from '@langchain/community/vectorstores/faiss'

export class ReasoningExampleVectorDB implements IVectorDB {
  private static _vectorDB: FaissStore

  public async similaritySearch(query: string, k = 2) {
    if (!ReasoningExampleVectorDB._vectorDB) {
      const dbExamples =
                [
                  {
                    'content': '课程介绍下',
                    'meta': {
                      'intention': '建议：在不知道客户的具体需求的情况下，先了解对课程的具体需求：希望课程后达成什么目标。了解到客户具体需求后，再开始发送我们的课程大纲和课程核心卖点以及用户证明。这个环节注意不要报价。'
                    }
                  },
                  {
                    'content': '论文服务',
                    'meta': {
                      'intention': '建议：先简单介绍当前论文服务的内容，询问客户的论文方向（CV/NLP）（我们更擅长辅导应用A技术的论文需求）了解客户什么时候希望完成发表，对论文服务有没有明确的需求。了解客户明确痛点后，再展开针对性介绍。'
                    }
                  },
                  {
                    'content': '客户不了解产品下，询问产品价格',
                    'meta': {
                      'intention': '建议：应当让销售员先给出产品介绍说明，针对客户的痛点突出产品价值。再询问意见，等用户觉得产品满意，再输出价格'
                    }
                  },
                  {
                    'content': '客户已经了解产品了，再次询问产品多少钱',
                    'meta': {
                      'intention': '建议：给出第一轮的价格体系和优惠体系，原价是6980, 现在正缝双十一期间，享9折优惠，今晚前下单还能再减200元。如果现在下单的话只需要支付6082元，早下单早享受！'
                    }
                  },
                  {
                    'content': '客户有需求，觉得产品好，但是第一次提到价格贵',
                    'meta': {
                      'intention': '建议：这是用户的初次试探，不要降价，告诉客户课程价值'
                    }
                  },
                  {
                    'content': '用户有需求，觉得产品好，但是第二次或者多次提到价格贵',
                    'meta': {
                      'intention': '建议：有可能当前的价格已经超出用户的目标价格了，此时给出第二轮价格体系进行逼单，承诺可以帮助用户申请助学金名额，但是需要现场下单。示例：如果这样您还是觉得太贵了，我可以去帮您申请助学金，助学金包含了1000元的优惠，您在学习过程中，只要能在半年内，完成80%以上的课程，但是名额有限，您如果现在下单的话，我帮您把这个名额定下来。'
                    }
                  },
                  {
                    'content': '用户不知道如何学习，没有基础',
                    'meta': {
                      'intention': '建议：根据用户具体情况给用户做出未来规划，告诉用户课程的学习路径，从而告诉客户我们在这其中发挥什么作用。'
                    }
                  },
                  {
                    'content': '客户考虑自学',
                    'meta': {
                      'intention': '建议：强调自学可能存在问题：找资料慢，且资料并不适配当前的能力，导致资料学习效率低下；有问题没有人答疑，学习进度受限，影响学习信心，很容易导致中途放弃。最后回到目标达成的角度，突出报课的重要性'
                    }
                  },
                  {
                    'content': '太贵了',
                    'meta': {
                      'intention': '用户意图：有2个种情况会觉得贵：一种是还没充分认识到产品的价值，所以觉得贵。第二种预算问题，所以觉得很难支撑。建议：强调价值里包含服务的内容，以及原价是多少。其次说明市面上的价格最低都是1.2w以上。如果是因为预算问题，可以用助学金名额&花呗分期来支持先报名。'
                    }
                  },
                  {
                    'content': '我再考虑下',
                    'meta': {
                      'intention': '建议：客户的反馈是这个时候，注意追问客户具体考虑的点是什么，可以以营销活动快要结束为由头，推进客户的决策进程，再定向解决客户这些顾虑。'
                    }
                  },
                  {
                    'content': '我和家人商量下',
                    'meta': {
                      'intention': '建议：对客户的需求表示理解，并要具体的沟通时间方便后续跟进的，最好先让定金100，锁定当前赠送福利。'
                    }
                  },
                  {
                    'content': '产品效果',
                    'meta': {
                      'intention': '建议：发送客户案例，用户故事来证明相关性。'
                    }
                  },
                  {
                    'content': '不着急，我过段时间再来考虑这件事情。',
                    'meta': {
                      'intention': '建议：首先强调目标的时间线：我们课程学习最好预留6个月比较充分，从客户目标达成角度倒推应该什么时候开始学习其次：用当前我们8980的限时活动价来推进客户尽快下单。'
                    }
                  }
                ]


      const docs = dbExamples.map((doc) => {
        return new Document({
          pageContent: doc['content'],
          metadata: {
            intention: doc['meta']['intention']
          }
        })
      })

      ReasoningExampleVectorDB._vectorDB = await FaissStore.fromDocuments(docs, OpenaiEmbedding.getInstance())

    }

    const results =  await ReasoningExampleVectorDB._vectorDB.similaritySearch(query, k)

    return results.map((result) => {
      return `${result.pageContent  }\n${  result.metadata['intention']}`
    }).join('\n\n')
  }
}