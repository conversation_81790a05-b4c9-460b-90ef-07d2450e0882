import { Document } from 'langchain/document'

import { OpenaiEmbedding } from '../../../lib/ai/llm/openai_embedding'
import { IVectorDB } from './base'
import { FaissStore } from '@langchain/community/vectorstores/faiss'

export class ReasoningExampleVectorDB implements  IVectorDB {
  private static _vectorDB: FaissStore

  public async similaritySearch(query: string, k = 3) {
    if (!ReasoningExampleVectorDB._vectorDB) {
      // Reasoning 20 个例子
      const dbExamples =
                [{ 'content': 'User          麻烦告知这个网站资料 谢谢\nBot          已经发送，注意查收。\nBot          你这边现在是在校还是已经在职了呀？\nUser          还是学生\nBot          嗯嗯，你这边是人工智能相关专业嘛？\nUser          医学 想做相关课题\nBot          嗯呢\nBot          那主要就是结合cv这块会比较多啦。\nUser          想做深度学习\nBot          嗯嗯\nBot          你现在人工智能的基础怎么样呢\nUser          您这个链接里面的用的读文献网站是哪一个呀？您发的材料太多了\nUser          不太会[破涕为笑]。',
                  'meta': { 'intention': '意见：我们刚刚发送了资料，提供了价值，是有机会和客户要点信息的。为了判断客户有无可能成为我们的目标客户，所以先询问下身份的粗颗粒信息。这个问题客户回复门槛不高。如果客户这个问题都不回复，大概率不是目标客户。',
                    'tags': ['学生', '阶段一', '收集客户画像'] } },
                { 'content': 'User          CV是啥\nBot          计算机视觉\nBot          你是医学相关基本上后续想结合人工智能会涉及到这块的呢\nUser          做影像学或脑电辅助诊断\nBot          嗯呢\nBot          那你做这块基本上是需要用到cv 的\nBot          你可以进群了解一下呢\nBot          你这块是必须要学习深度学习，然后再学cv的\nUser          嗯嗯 好的呀 我就只有点matlab基础 能学会吗\nBot          嗯呢 你还是有编程基础的 我们这边正式学习的话就是从零开始的呢\nBot          你可以先进群，然后今天晚上会有零基础的直播课\nBot          然后你现在也可以多了解一下人工智能这块\nBot          感觉你的了解还是比较少的\nUser          嗯嗯',
                  'meta': { 'intention': '意见：了解客户工作方向后，主动抛出客户在AI方面的需求点，并邀请客户进群。等客户反馈，如果客户同意，可以认为是意向客户。记得我们问的所有问题和回复都是为了匹配客户需求和我们产品需求的可能性。',
                    'tags': ['学生', '阶段一', '需求明确'] } },
                { 'content': 'Bot          这个是我们整个课程的路线，你可以看看\nBot          【蓝色部分】是我们的主体内容，从0基础开始到最基础的语言工具（python）再到算法的基础知识（数学），然后搭建算法模型的框架（pytorch+opencv），再然后我们才能深入基础理论并结合经典论文中的模型实战做详细讲解。除了同时基础外，我们还细分了CV和NLP两个大方向，可以优先选择1个方向深入，学有余力再去拓展另一个。\nBot          【黄色部分】是根据大家学习ai的终点站设计得三个板块得内容（三选一）其中包括了论文写作，比赛指导，项目实战，帮助同学将所学内容应用到实际业务需求中去\nBot          【橙色部分】是增值内容（且以后会一直更新得内容），这个是目前作为福利赠送给到大家开拓视野的，包括但不限于国际前沿论文及讲座，最新最热门领域应用等\nBot          现在黄色部分和橙色部分都是我们现在活动期间给到的福利，黄色部分三个服务可以选择一个，橙色部分是考虑到大家不仅学习人工智能可能还会需要其他的一些技能点，现在也是刚开第三期的班，作为赠送呢\nBot          然后入学之后，咱们老师会根据你这边的需求以及基础情况帮你规划学习路线\nBot          这样会清晰一些嘛\nUser          嗯嗯 上课中遇到实际问题咋沟通呀？群里问吗\nBot          我们会有班级群，可以直接在群里面问老师，或者有一些比较特殊的情况或者问题，可以联系老师进行一对一的指导\nUser          一对一指导需要额外付费吗\nBot          不需要的\nBot          我们课程报名之后都是不会再收取任何费用，在学习权限之内\nUser          直播录播是在每周啥时间呀？\nBot          现在还有SVIP的名额，学习权限是两年，然后录播是可以一直看的，答疑也是永久的\nBot          直播课是周六，周日上午10点开始呢  (To Dr.L: 直播录播是在每周啥时间呀？)\nBot          现在也是第三期，刚开班，直播课之后都是有录播课的，可以随时学习\nBot          然后像你如果现在时间比较多，然后时间也比较紧张的话，那就可以看前面第二期的录播课来补进度',
                  'meta': { 'intention': '意见：对客户的基础情况有了解，且抛很多AI学习相关的邀请，客户的反馈都比较积极，说明客户对AI学习的意向很不错，也基于他也听过公开课对课程但有模糊理解，现在可以给他介绍具体课程内容了。但是注意结合他的需求有重点的推荐，切忌平铺直叙：对客户的基础情况有了解，且抛很多AI学习相关的邀请，客户的反馈都比较积极，说明客户对AI学习的意向很不错，也基于他也听过公开课对课程但有模糊理解，现在可以给他介绍具体课程内容了。但是注意结合他的需求有重点的推荐，切忌平铺直叙。',
                    'tags': ['学生', '阶段二', '针对性产品介绍'] } },
                { 'content': 'Bot          你学习的话学习费用会有压力嘛\nUser          费用有压力[破涕为笑]\nBot          正常的 毕竟现在还是在校学生嘛\nBot          但是我们的学习费用就是保价了，包括现在也是赠送的福利比较多\nBot          刚才老师在课堂讲的花呗 信用卡分期免息你有听到嘛\nBot          [嘿哈]\nUser          嗯嗯\nBot          分期的话你的压力大嘛[皱眉]\nBot          在我们这边学习的学生，基本上就是两种情况，一种就是和父母沟通，父母也支持理解就全款报名了，还有一种就是不想麻烦父母，就自己投资自己学习，就采用分期的模式\nBot          不过自己就会比较省一些\nUser          得和家人商量一下[破涕为笑]\nBot          你是做影像学或脑电辅助诊断这块也是需要用到我们课程里面CV这块的，肯定也是符合你的需求哒，包括后面你想打比赛呀或者是写论文我们老师这边是会带队打比赛然后辅导写论文的\nBot          嗯嗯 和家长商量肯定是最好的，基本上家长也都是会支持的 毕竟我们课程是真的能够帮助到你的\nBot          今天能商量好嘛\nUser          还能有优惠吗\nBot          [捂脸]这个已经是保价啦\nBot          现在也是优惠力度最大的时候呢\nBot          保价就是之后不会有比这更低的价格啦\nBot          像橙色增值部分之后都是需要额外的费用来报名了呢\nBot          但是今天是不需要再额外收费了呢',
                  'meta': { 'intention': '意见：我们一顿产品介绍后，客户有几分钟不回复，客户这时候面对着决策还是面对很大的压力。这个压力来源一定不是产品相关的，因为从上文回复来看，有产品问题，客户会回复得很快。现在不回复大概率在纠结价格问题，但是作为客户，是很不愿意直接说出自己没钱的，我们可以站在客户视角，直接指出这个问题。价格问题，我们先接纳和认同客户的状况先，拉近和客户的距离。同时强调我们价格是低价，打消我们可能还可以再便宜的顾虑。客户这个时候要做个采用什么方式付费的决定。我在这里告诉他们一般人都是怎么决定的，提高他们做决策的动力。过程中追问每个环节的时间点很重要，要客户一个行动承诺，如果能确定下来这是最好，否则的话，客户很可能就放下这个事情而不继续往下推进了。',
                    'tags': ['学生', '阶段三', '沟通费用问题'] } },
                { 'content': 'Bot          咱们工作中需要用到AI嘛\nBot          【目标检测理论】完整课程录播回放+课件PPT 链接：https://pan.baidu.com/s/1S6nUV0Qmf2F0_kYPmHBh1A?pwd=8888 提取码：8888 --来自百度网盘超级会员V5的分享\nBot          课后资料在这里哈\nUser          我自己想从事这方面的事 ，但目前的事业不涉及这些\nBot          嗯嗯 那可以的呀\nUser          是比较传统的生意\nUser          多学习 希望走在前头吧\nBot          可以先把基础打好 然后再找机会换工作\nUser          嗯嗯',
                  'meta': { 'intention': '意见：确认客户目标和需求，只有了解这个我们才能判断我们的产品能给客户提供什么价值，继而提供更多针对性信息给到对方。',
                    'tags': ['工作', '阶段一', '收集客户画像'] } },
                { 'content': 'Bot          今天老师介绍了一个系统学习路线图\nUser          感谢\nBot          同学需要了解下嘛\nUser          需要\nBot         可以看下这个\nBot          看完可以给你详细介绍下\nUser          我目标是理解算法看懂代码 我学习力估计扛不住这些\nUser          会多学学基础知识 但不打算成为技术 的情况\nBot          课程里面重点就是先搞懂算法\nBot          把基础打好的\nBot          你是自己不想做算法开发这个方向吗\nUser          对 没那脑子 但是朋友技术可以\nUser          在大厂从事云计算云安全的开发\nUser          所以我属于学习了解 便于我理解人工智能和应用人工智能\nBot          了解 那你先不急着做决定 可以先复习下',
                  'meta': { 'intention': '意见：初步已经学习目标有了了解，且进群学习，上课都很积极，说明客户还是很有意向的，可以和客户介绍一些我们服务相关的内容了。但是直接上来介绍产品，会太生硬，会让客户感觉很突然，还是以提供价值的角度，介绍帮助其目标达成学习路径，让我们的产品在这个学习计划中露出，让客户自然接收到产品信息。',
                    'tags': ['工作', '阶段二', '上完直播课后介绍课程'] } },
                { 'content': 'User          多少钱\nBot          现在是想学习这个系统课程，然后有论文辅导嘛\nUser          对\nUser          可以分期吗\nBot          可以的\nBot          你现在研究呢\nBot          论文方向确定了嘛\nUser          大致定了\nUser          但是可以微调\nUser          如果有更好的也可以改一下\nUser          可以大概说个预算吗\nBot          方便说一下大致的方向和目前掌握的技能点嘛，我也好给你一些建议和规划\nUser          环境科学这边的，目前的话会一些spss数据分析的，python浅入门吧，其实和机器学习不是很沾边，目前在做物质流分析，找到一个源代码模型，想尝试看看能不能结合我的物质做。之前有和您这边老师沟通过，但是原来那个模型以及被我弃掉了所以没有买课。另一个方向是学习另一个化学传输模型，网上公开有教程和代码，难点在于需要根据自己的物质进行一些调试，我想老师辅导一下，方便我更快熟悉代码。这是我目前两个小的方向\nBot          我们人工智能系统班结合论文辅导这块的话目前优惠之后是在八千多，稍等，我可以给你详细介绍我们的课程以及辅导模式内容',
                  'meta': { 'intention': '意见：经我们1个月的信息触达，客户主动开始发问价格，最近一次被唤醒的是论文相关的需求，所以客户的痛点应该还是比较明确，为了后续客户购买决策可控性，先问清楚客户具体的需求是什么样的，我们对其的可能价值是什么。明确好客户围绕论文这个大背景下的具体痛点是什么，好后续给针对性方案。在销售原则中，没有针对用户把产品的价值讲透彻之前，报价只回把用户吓走，此时不要直接给用户回复报价，建议进一步了解用户当前的情况，如继续询问论文方向，当前掌握的技能等信息，以便为用户做好定制化的建议和规划。此时可以告诉用户真实的价格，并且充分结合客户的痛点介绍产品价值。',
                    'tags': ['学生', '阶段一', '了解客户需求背景', '论文'] } },
                { 'content': 'User        想学习kaggle\nUser        kaggle资料\nBot        现在是学生还是在职呢\nUser        研0\nUser        双非硕士\nBot       是想打比赛吗？"\nUser        对\nBot        选好赛题了吗\nUser        还没\nBot        我们这边有导师带队打比赛\nBot        有没有兴趣一起呀\nUser        有',
                  'meta': { 'intention': '意见：客户比较明确表达了打比赛的需求，且当前还处于刚开始，比较迷茫的阶段，可以直白告知我们有相关服务，进一步看客户需求。',
                    'tags': ['学生', 'kaggle', '阶段一：了解客户需求背景'] } },
                { 'content': 'User        kaggle怎么收费啊？\nBot        稍等 我把介绍内容发你看下哈\nBot        你看下上面这个两个Kaggle课题 你对哪个比较感兴趣呀\nUser        我是零基础，今年9月读研[捂脸]\nBot        嗯嗯 我知道呀\nBot        你刚跟我说过的\nUser        llm\nUser        第一个\nBot        这个是详细介绍\nUser        参赛要求的我都不会，可以吗[捂脸]\nBot        我们是有赛前集训营的"\nBot        可以提前打基础\nUser        可以，打基础怎么收费\nUser        ok\nUser        详细介绍下哈\nBot        "链接：https://pan.baidu.com/s/1J5Z"\nBot        现在有时间看的吧\nBot        [图片]\nBot        这个是我们的讲师介绍 可以看看哈\nBot        [图片]\nBot        这是我们学员的一些反馈\nBot        上面这个同学自学然后想打比赛\nBot        难度还是非常大\nBot        [图片]\nUser        大三就学了，我大学在干啥啊[捂脸]\nBot        [图片]\nBot        哈哈啊\nBot        现在开始也不晚的\nBot        💡我们上课模式是直播+录播的形式，大纲上的所有内容【均有直播】和录播提供。\nBot        每周人工智能基础系统课程是周六、日下午4：00~5：30上课（每周助教老师都会把提前安排好的课程发在班级群里）\nBot        你先看完课程，稍后再给你做详细介绍吧\nUser        好的\n意见：客户一般上来先关心费用，先考量是否有预算做这件事情，但是这时候，我们一定要忍住。因为没有了解我们课程价值，也不知道怎么选择这类课程的时候，一般都会无标准的觉得贵，而打消继续了解课程价值的想法。其实Kaggle比赛是我们的一个延伸课程方向，我们课程主题是基础课程，但是因为这个时候客户的视角里只关心kaggle比赛相关的内容，所以我们先直接介绍kaggle比赛相关的课程问题。清楚介绍完kaggle比赛之后，比赛的服务被客户接收之后，会自然遇到基础的问题。这个时候再和客户介绍我们的主题课程的部份。针对客户需求针对性输出，再加上的我们的服务案例。User: 课程多少钱呢\nSale：先发您课程路线看一下呢，这边还有试听课，要不要听一下呢',
                  'meta': { 'intention': '意见：不要直接回答价格，要先给用户提供课程试听，或者课程内容介绍，让用户认为有价值，再给出价格',
                    'tags': ['学生', '阶段二', '介绍课程', 'kaggle'] } },
                { 'content': 'User: 这个价位说实话已经不是我自己能负担的了，能再优惠点吗\nUser: 还有就是你们这边有什么保证吗\nSale: 嗯嗯，理解的，毕竟现在也还是学生，所以我了解你的情况之后也还是推荐你可以先学习我们课程，因为单独的毕设辅导价格会更高一些，我们课程学费的话已经是保价了,不会有比这更低的价格。\nSale: 我们进班后是可以签订合同的\nSale: 进班学习不仅可以学到AI基础和实战部分，还可以有论文指导，项目指导的。',
                  'meta': { 'intention': '意见：对于学生来说 8000多的价位是很大的负担，但是用户有需求， 这个时候要通过介绍相关服务，推荐试听课，横向对比其他机构的价格，展示合同，展示相关同学案例，增加信任' } },
                { 'content': 'User：零基础学懂每周要花多长时间呀\nSale: 像你现在是零基础每周学6-8个小时就可以了，我们课程也是完全从零开始进行学习的，然后像我们的一些实战部分，都是在你学习了理论知识之后再给你安排实战的任务来进行落地\nUser: 全部学完还是很大工程量呀\nSale: 全部学完基本上就是6~8个月的时间 毕竟从零开始学习嘛 我们也要保证你掌握的扎实，真正能够掌握的比较好的 肯定是需要一定时间的呀。\nSale：但是具体的还是看你的学习时间情况呢 时间多一些每天就能多学一些 但是也不能太多呀 还是得消化好的[偷笑]',
                  'meta': { 'intention': '意见：用户担心学习时间过长，这个时候应该通过每周或每月拆分学习时长。但是实际上，课程的规划和学习是不可能速成的，要慢慢来，也要说明这一情况。' } },
                { 'content': 'User: 您好 我暂时不报名了\nSale: 怎么了呢，和家里商量是有哪里没有沟通好还是\nUser: 太贵了\nSale: 你是觉得课程不值这个学费嘛\nUser :值不值得我也没有这个钱[破涕为笑]\nSale: 你不是说和家里商量的哇\nUser: 不同意所以我没钱啊',
                  'meta': { 'intention': '意见：用户选择不付款，这个时候要去找到原因。是因为课程本身，还是因为价格问题。判断出价格问题后，可以通过优惠券，分期付款，首期付一部分减少付费压力，或者是推荐试听课，让用户先体验一下课程，再决定是否付款。' } },
                { 'content': 'User: 话说关于你们的论文班有什么资料吗？\nSale: 你怎么知道我们有论文班\nUser: 这不是看你的朋友圈吗[破涕为笑]\nUser: 论文写作班',
                  'meta': { 'intention': '意见：通过及时发送客户需要的材料，客户和我们已经建立好基础信任，我们可以开口问客户更个人的信息。为了完成销售目标，首先进行对客户的的基本了解，因为一般比较关注论文的都是研究生，所以开口问是研究生几年级是更快的获取信息的方式' } },
                { 'content': 'User: 大概是这个研究内容\nUser: 我们实验室有具体的课题，但和实验室的学长学姐聊过之后，可能不一定要解决这个具体的问题了，实验室的强化学习要一次训练一个多月。\nUser: 所以可能往理论或者别的课题上变，但这下子就变得迷茫了起来[破涕为笑]\nUser: 我们之前是用智能优化算法解决大规模多目标优化问题，我的想法是用GNN和强化学习上开发新方法。\nUser: 用ai的新方法的基础知识还不太够，加上课题方向上比较迷茫，现在需要这些帮忙吧',
                  'meta': { 'intention': '意见：用户已经明确需求，需要先确定课程能够解决他的问题才能够报班，这个时候转到人工，让人工来解决，跟老师预约沟通时间' } },
                { 'content': 'Sale: 是这样的哦，我们这边今天晚上8点有一节直播的AI体验课程，特别邀请曼彻斯特大学、英国南安普顿大学人工智能、生物信息双硕士来上课的，你这边有时间参与吗\nUser: 什么课\nSale: 目标检测这块的\nUser: 嗯可以看看\nSale: 好的，晚上我给你发直播地址哈\nUser: 嗯嗯',
                  'meta': { 'intention': '意见：很关键重要的问题，本质还是在筛选用户，原因是这里的获客渠道是通过免费“领取ai学习资料加微信”来的客户，而不是通过类似抖音的表单（用户主动填写姓名和手机号）这样更精准的客户，同时用户在添加微信的时候并不知道我们是卖AI课程的，所以来的用户量会非常多，比较杂，早期的对话目的更多的是筛选客户。' } },
                { 'content': 'Sale: 现在是读研还是在职啦\nUser: 读研的\nSale: 现在研几了，具体是做的那一块呀\nUser: 刚研一，建筑方面的[囧]',
                  'meta': { 'intention': '意见：可以表达这个行业和ai之间的价值，同时继续确定用户画像，这样方便后续围绕用户的场景，给出更针对的建议' } },
                { 'content': 'Sale: 同学这边听课感觉怎么样呀~\nUser: 还行，第一次接触听懂个大概\nSale: 嗯呢，刚接触是这样子的',
                  'meta': { 'intention': '意见：关键问题，问的时机非常合适，用户听完了直播课程，并且表示“听懂了大概”也是代表了初步认可了，因为在直播课程中，线上的老师第一次表明了“我们是一家教大家学习AI的公司，也简单露出了付费课程，也给到了价格”' } },
                { 'content': 'Sale: 这是具体的学习路线和实战项目同学先仔细看看\nSale: 理论+实战结合教学的\nUser: 好的！\nSale: 同学这边听课感觉怎么样呀~\nUser: 还行，第一次接触听懂个大概\nSale: 嗯呢，刚接触是这样子的\nSale: 有想法系统学习这块的知识嘛\nUser: 有想法\nSale: 那可以趁着我们这次的活动跟着阿文老师学习起来呀\nUser: 感觉学费还是有点贵\nSale: 其实学费这块的话我们在行业内是算低的勒，主要是看同学学习这门课程可以给你带来什么价值是吧! 学费和价值是对等的\nSale: 第一的话我们课程你现在报名是有两年的学习权限的，我们课程来说6-8个月更新一期，每一期都是会加新内容的，等于两年的学习权限你是最少可以学习到3-4期的课程的\nSale: 第二，两年内容产生的录播课程你是可以永久学习的，包括我们的答疑服务你也是可以永久享受的，就算等你就业了，有这块的问我都是可以约老师给你指导的\nSale: 第三，现在报名的话是包含了论文辅导的，像你硕士论文和毕业论文我们都是可以给到你帮助的\nSale: 况且你现在建筑行业的话后续结合人工智能发论文这块的创新点也是多一些的',
                  'meta': { 'intention': '意见：用户主动表达了觉得价格贵，这里有隐含意义，只有思考过是否要购买的用户才会思考价格的高低，所以这是一个极其积极的信号，是销售输出产品价值的时候了。这里销售要有明确的引导性，引导用户从价格去看到课程的价值。引导用户从价格去看到课程的价值。讲述课程价值，不止是给到学习相关的资料，而是学习服务。v' } },
                { 'content': 'Sale: 课程的性价比还是非常高的\nUser: 那这个课程学习完会达到什么程度呢\nSale: 课程对标的是硕士人工智能专业的水平\nUser: 就是会有硕士的程度吗\nSale: 是的，因为本身课程就是理论+实战穿插教学的\nSale: 我们课程一共是有46个实战项目的\nUser: 哦哦，一万五还是太贵了[捂脸]\nSale: 不是的呀，现在我们课程第三期是先开班，有活动优惠的，课程原价是14880，现在活动优惠价格是8980\nUser: 那个8980不是需要抢优惠券才有嘛\nSale: 我可以给你直接去内部申请优惠的\nUser: 那优惠期限到什么时候\nSale: 你现在是经济这块有压力要和家里商量吗',
                  'meta': { 'intention': '意见：从客户目前是学生以及不停的讨论价格，结合销售过往的经验，8000多对一个中国学生来说很少有学生可以完全没有压力一次性拿出，10个学生里9个学生基本都属于需要家里的帮助或者分期，这里的共情来自于销售的历史经验。' } },
                { 'content': 'Sale: 你现在是经济这块有压力要和家里商量吗\nUser: 是的\nSale: 你现在方便和家里沟通不\nUser: 今天时间来不及了应该\nSale: 那这样，我给你先把优惠留下来，你先支付100的预定费用，我先给你录入学院系统，然后你明天再和家里商量',
                  'meta': { 'intention': '意见： 极其重要的动作，看似只是100的预付，但是这是一个非常重要的流程，100块看似只是8000多课程总价的80/1，并且可以退，但是乘热打铁让用户做一个互相commit的动作，极大的降低了用户后续反悔的概率' } },
                { 'content': 'User: 嗯嗯，上课是怎么上，直播嘛\nSale: 上课时直播+录播的一个形式，直播课是每个星期的周六和周日上午\nSale: 然后你明天报名的话我可以给你去申请赠送第二期的录播\nSale: 然后我们创始人的话也是中南大学的硕士\nUser: 可以，我先商量商量\nSale: 可以的，要和家里说清楚，学习的价值和意义在哪里，我们这边现在百分之99的学员都是家里支持的, 大部分都是和你一样刚研一的学员，后续都是需要结合人工智能去学习的',
                  'meta': { 'intention': '意见：因为在这个客户这儿，最后的“消费者”是学生家长，销售就要尝试给到学生一些说服家长的思路，同时说明跟他相同情况的热门是如何选择的，人具有从众性。' } },
                { 'content': 'Sale: 你好,下午分享的内容看完了吗\nUser: 嗯嗯\nSale: 感觉如何\nUser: 感觉还行\nSale: 嗯嗯  那你这边怎么考虑呢\nSale: 要不要加入我们系统学习  一边打基础  一边准备论文\nUser: 我没有这么多钱',
                  'meta': { 'intention': '意见：针对钱的卡点问题，直接抛出我们的解决方案的，看客户的反馈是什么' } }]

      const docs = dbExamples.map((doc) => {
        return new Document({
          pageContent: doc['content'],
          metadata: {
            intention: doc['meta']['intention']
          }
        })
      })

      ReasoningExampleVectorDB._vectorDB = await FaissStore.fromDocuments(docs, OpenaiEmbedding.getInstance())

    }

    const results =  await ReasoningExampleVectorDB._vectorDB.similaritySearch(query, k)

    return results.map((result) => {
      return `${result.pageContent  }\n${  result.metadata['intention']}`
    }).join('\n\n')
  }
}