// 在通知群内通知对应的客服处理消息
import { JuziAPI } from '../../../lib/juzi/api'
import { Config } from '../../../config/config'
import { IWecomMsgType } from '../../../lib/juzi/type'

export class GroupNotification {
  public static async notify(message: string, groupId?: string) {

    await JuziAPI.sendGroupMsg(Config.setting.wechatConfig?.id as string, groupId ? groupId : Config.setting.wechatConfig?.notifyGroupId as string, {
      type: IWecomMsgType.Text,
      text: message
    })
  }
}