import { PromptTemplate } from '@langchain/core/prompts'

interface TonePromptVars {
    origin_message: string
    chat_history: string
}

export class ResourceSendPrompt {
  public static async format(params: TonePromptVars) {
    return PromptTemplate.fromTemplate(
      `你是一个微信聊天场景的语言改写助手，参考下方的聊天记录，我会给你一个要发送给用户的资料内容，你需要将这个内容改写成更贴近用户意图的答案，并且更符合当前聊天记录内的语气。不要发送emoji。

聊天记录：            
###
{chat_history}
###
    
修改前：{origin_message}
修改后：`
    ).format({
      chat_history: params.chat_history,
      origin_message: params.origin_message
    })
  }
}