import { PromptTemplate } from '@langchain/core/prompts'

export class isUsingWebSearchPrompt {
  public static async format(query: string) {
    return PromptTemplate.fromTemplate(`<Instructions>
Your task is to determine whether a network search is needed based on a user's question. The key criterion for deciding to use a network search is whether the information required is time-sensitive or highly specific.

Follow these steps:

1. Analyze the question to understand what kind of information the user needs. Ensure that you focus on keywords or phrases that indicate whether the information is dynamic (changes over time) or specific (requires detailed and precise information).
2. Use the following criteria to decide if a network search is necessary:
   - If the question pertains to current events, latest data, or any information that is likely to change frequently, a network search is required.
   - If the question seeks precise information that cannot be answered with general knowledge or static data, a network search is needed.
3. Justify your reasoning before providing your final decision. Explicitly state the keywords or phrases that led to your decision.
4. Conclude with your decision on whether a network search is needed.

Here's the format for your response:

<scratchpad>
[Explain your reasoning, mentioning specific keywords or phrases from the question]
</scratchpad>

<answer>
[YES/NO: Whether a network search is needed, based on your reasoning, only write YES or NO]
</answer>

Given the user's question: 
<user_question>
{user_question}
</user_question>
</Instructions>`).format({
      user_question: query
    })
  }
}