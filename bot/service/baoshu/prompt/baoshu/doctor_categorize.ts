import { PromptTemplate } from '@langchain/core/prompts'

export class DoctorCategorizePrompt {
  public static async format(chat_history: string) {
    return PromptTemplate.fromTemplate(`You will be analyzing a conversation between a consultant and a client to determine the client's primary need. Here is the conversation transcript:
<conversation>
{chat_history}
</conversation>

Your task is to categorize the client's need into one of the following categories:
1. Consulting about applying for a PhD
2. Career planning
3. Undecided between pursuing a PhD or career planning
4. Other

Carefully read through the conversation and pay attention to the client's statements, questions, and concerns. Consider the following guidelines when determining the category:

- If the client primarily asks about PhD application processes, requirements, or specific PhD programs, categorize as "Consulting about applying for a PhD"
- If the client mainly discusses job prospects, career paths, or professional development outside of academia, categorize as "Career planning"
- If the client expresses uncertainty between pursuing a PhD or exploring other career options, or if their needs don't clearly fit into the first two categories, categorize as "Undecided between pursuing a PhD or career planning, or other"

After analyzing the conversation, provide a brief justification for your categorization, highlighting key points from the conversation that support your decision. Then, state your final category selection.

Write your response in the following format:

<analysis>
[Your justification for the categorization, referencing specific parts of the conversation]
</analysis>

<category>
[Your selected category: 1, 2, 3 or 4]
</category>`).format({
      chat_history
    })
  }
}