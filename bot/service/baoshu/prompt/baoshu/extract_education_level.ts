import { PromptTemplate } from '@langchain/core/prompts'

export class ExtractEducationLevelPrompt {
  public static async format(query: string) {
    return PromptTemplate.fromTemplate(`You are an AI assistant tasked with extracting the education level of the person the customer wants to consult about in a chat between a user and a study abroad consultant. Here is the chat history:
    
<chat_history>
{{chat_history}}
</chat_history>

Your task is to carefully read the chat history and determine the current education level (current_level_of_education) of the person being discussed. Follow these steps:

1. Identify who is being consulted in the conversation - the user themselves or someone else (e.g., their child).
2. Look for explicit mentions or implicit clues about the current education level.
3. If the current education level is not explicitly stated but there's mention of applying for a higher degree, infer the current level as one step below the target degree (e.g., if applying for a master's degree, infer the current level as '本科').
4. If you cannot determine the education level with certainty, set it as 'null'.

The possible education levels are:
['低龄', '小学', '初中', '职高', '中专', '技校', '高中', '大专', '本科', '硕士', '博士']

After analyzing the chat history, output your result in JSON format within <extracted_info> tags. The format should be:

<analyze>
Follow above steps to analyze the given chat history to determine the current education level of the person being discussed.
</analyze>
<extracted_info>
{"current_level_of_education": "教育阶段"}
</extracted_info>

Here are two examples to guide you:

Example 1:
User: "老师，想问下国内双非电子信息研究生前景大概怎么样"
Output:
<analyzeExample 2:
User: "87年，清华美院本硕，儿子10岁，女儿8岁，老公金融民工。我有个问题：两个孩子有没有好的规划方向？"
Output:
<analyze>
用户在咨询两个孩子的规划，孩子年龄分别为10岁和8岁，属于低龄阶段。
</analyze>
<extracted_info>
{"current_level_of_education": "低龄"}
</extracted_info>>
用户想了解电子信息专业研究生的前景，未提及当前学历，但想申请研究生，可推断当前学历为本科。
</analyze>
<extracted_info>
{"current_level_of_education": "本科"}
</extracted_info>

Remember to focus solely on extracting the current education level from the given chat history. Do not add any explanations or additional information in your output.`, { templateFormat: 'mustache' }).format({
      chat_history: query,
    })
  }
}