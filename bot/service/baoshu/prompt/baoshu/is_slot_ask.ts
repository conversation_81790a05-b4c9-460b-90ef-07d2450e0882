import { PromptTemplate } from '@langchain/core/prompts'

/**
 * 意向客户判断 Prompt
 */
export class IsAICompleteSlotAskTaskPrompt {
  public static async format(task: string, query: string) {
    return PromptTemplate.fromTemplate(`You are given a task description and a response from a consultant. Your job is to determine whether the consultant's response completes the task described.   
Here is the task description:
<task_description>
{task}
</task_description>

Here is the consultant's response:
<consultant_response>
{query}
</consultant_response>

Decide if the consultant's response completes the task. Return 'true' if it does, and 'false' if it does not. Write your decision inside <answer> tags.
注意顾问问的问题有可能是基于用户上下文进行提问的，所以只要顾问回复中有问句则认为是完成了任务，返回 true

<answer>
[Your decision here based on the justification, only response true or false.]
</answer>

example:
<consultant_response>
你是想去哪个国家呢
</consultant_response>

顾问的回复中包括问句，返回 true
<answer>
true
</answer>
`).format({
      task,
      query
    })
  }
}