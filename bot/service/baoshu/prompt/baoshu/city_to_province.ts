import { PromptTemplate } from '@langchain/core/prompts'

export class CityToProvincePrompt {
  public static async format(query: string) {
    return PromptTemplate.fromTemplate(`You are tasked with converting a given city name to its corresponding Chinese province name, or identifying it as a foreign city. Follow these steps:
    
1. You will be provided with a city name in the following format:
<city_name>xx</city_name>

2. Analyze the given city name and determine if it is a Chinese city or a foreign city.

3. If it is a Chinese city:
   - Identify the province in which the city is located.
   - Output the province name in Chinese characters.

4. If it is a foreign city or you are unsure:
   - Output "海外" (which means "overseas" in Chinese).

5. Use the following guidelines to help you determine if it's a Chinese city:
   - Chinese city names are typically written in Chinese characters.
   - If the city is a non-Chinese city, output "海外".
   - If you can't determine the province, output "null".

6. Provide your answer within <answer> tags.

Here's an example of how your response should be formatted:

<answer>江苏</answer>
or
<answer>海外</answer>
or
<answer>null</answer>

Remember, if you're not sure about the city's location or if it's definitely not in China, always output "海外".

The city name you need to convert is:
<city_name>{city}</city_name>
`).format({
      city: query
    })
  }
}