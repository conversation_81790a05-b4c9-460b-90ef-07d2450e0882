import { PromptTemplate } from '@langchain/core/prompts'

export class ProjectSelectPrompt {
  public static async format(userSlots: string, projects: string) {
    return PromptTemplate.fromTemplate(`根据用户信息，从下方项目列表中选出最适合的项目，如果下方没有合适项目可以推荐一个。注意专升本，或专科留学，一般总预算就是一年预算，不需要额外计算总预算。

<user_info>
{userSlots}
</user_info>

<program_list>
{projects}
</program_list>

Provide your recommendation in the following format:

<recommendation>
Explanation:
[Provide a detailed explanation of why the recommended program are suitable for the user.]
[Recommended program: The most recommended program here, or recommend a new program]
</recommendation>

首先分析客户预算与推荐项目的预算是否匹配，如果不匹配明确告知用户预算不够，建议考虑其他国家或项目。
Answer in Chinese`).format({
      userSlots: userSlots,
      projects: projects
    })
  }
}