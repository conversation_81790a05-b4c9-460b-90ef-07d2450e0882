import { PromptTemplate } from '@langchain/core/prompts'

export class ShouldPushProjectPrompt {
  public static async format(chat_history: string) {
    return PromptTemplate.fromTemplate(`You will be analyzing a chat history to determine if it's an appropriate time to recommend a study abroad program to the user. Here is the chat history:

<chat_history>
{chat_history}
</chat_history>

Your task is to determine if the current moment in the conversation is suitable for recommending a study abroad program (or country) to the user. This would be appropriate if:

1. The user is actively inquiring about study abroad programs or countries.
2. The conversation naturally leads to a point where suggesting a study abroad program would be relevant and helpful.

Analyze the chat history carefully, paying attention to the user's questions, statements, and the overall context of the conversation. 

If you determine that it is an appropriate time to recommend a study abroad program, output "true". If you cannot confidently determine that it's an appropriate time, or if the conversation is not related to study abroad programs, output "null".

Provide your reasoning before giving your final determination. Use the following format for your response:

<reasoning>
[One sentence to explain your analysis of the chat history and why you believe it is or is not an appropriate time to recommend a study abroad program]
</reasoning>

<answer>
[Output either "true" or "null"]
</answer>

Remember, only output "true" if there is a clear indication that the user is interested in or asking about study abroad programs. If you're unsure or the conversation is unrelated, output "null". `).format(
      // @ts-ignore mustache 使用
      {
        chat_history: chat_history
      })
  }
}