import { PromptTemplate } from '@langchain/core/prompts'

export class BooleanUserInfoExtractPrompt {
  public static async format(chat_history: string) {
    return PromptTemplate.fromTemplate(`Your task is to carefully read the user's chat history with the study abroad consultant and extract key information related to the user's study abroad requirements. This will help in retrieving relevant information from the database based on the user's needs.
<Input>
<chat_history>
{{chat_history}}
</chat_history>
</Input>

Here's how you should approach this task:

1. Read the user's query carefully to identify key requirements and constraints.
2. Focus on extracting information related to the following fields:
   - "is_study_abroad": Determine if the user has an intention to study abroad soon, not at all, or prefers to delay the time abroad, or is following the traditional domestic education route. Use "true" for immediate intention, "false" for no intention, "later" for delayed intention, and "domestic" for traditional domestic education.
   - "is_parent": Determine if the user is the student's parent or the student themselves.
   - "is_japanese_student": Determine if the user is a Japanese language student. Use "true" if the user explicitly mentions they are studying Japanese or are a Japanese student, and "false" otherwise.
   - "is_gaokao_student": Determine if the user is a student who has taken the "高考" this year. Use "true" if the user mentions they are a 高三 student or taken "高考" this year, or explicitly states they will take or have taken the Gaokao, and "false" otherwise.
   - "is_working": Determine if the user mentions that they are already working or have graduated. Use "true" if the user explicitly states they are working or have graduated, and "false" otherwise.
   - "has_offer": Determine if the user mentions having received an offer for admission to a school/university. Use "true" if they explicitly state they have an admission offer, and "false" otherwise.
   - "has_agent": Determine if the user already has an agent. Use "true" if they explicitly mention they have an agent, "false" if they mention they do not have an agent, and "null" if there's no mention. 

1. "is_study_abroad":
   - Look for explicit mentions of studying abroad, including phrases like "想出国", "希望出去", "不想出国", "想润" etc.
   - Consider implied intentions, such as discussions about foreign universities, study plans, or related topics.
   - If the user mentions studying abroad soon, set "is_study_abroad" to "true".
   - If the user explicitly states they do not want to study abroad, set "is_study_abroad" to "false".
   - If the user mentions delaying the time abroad, set "is_study_abroad" to "later".
   - If the user mentions prefer traditional domestic education routes, set “is_study_abroad” to “domestic”. 注意用户明确说偏好国内读书，不想出国，才设为 "domestic", 用户说自己所在城市的时候，不代表不出国，set "is_study_abroad" to null.
   - If the user mentions 统招/计划内/双证。都是计划内，计划申请国内的大学和本科。
   - If the information is insufficient, set "is_study_abroad" to null.
   - 注意"中外合办","国际本科", "国际高中", "4+0", "3+1", "2+2"都算是在国内读几年，然后再出国, set "is_study_abroad" to "later".

2. "is_parent":
   - Look for mentions of the user being a parent, such as "我是家长", "我孩子", "我女儿/儿子".
   - Consider responses indicating the user is a student, such as "我是学生", "我自己".
   - If the user indicates they are a parent, set "is_parent" to true.
   - If the user indicates they are the student, set "is_parent" to false.
   - If the information is insufficient, set "is_parent" to null.
   
3. "is_japanese_student":
   - Look for explicit mentions of the user studying Japanese or being a Japanese language student, such as "学日语", "我是日语生".
   - If the user mentions they study Japanese or identifies as a Japanese student, set "is_japanese_student" to true.
   - If the user does not mention studying Japanese, set "is_japanese_student" to false.
   - If the information is insufficient, set "is_japanese_student" to null.

4. "is_gaokao_student":
   - Look for explicit mentions of the user being a current senior 3 student or stating they have taken the Gaokao this year, such as "我是高三学生", "高考出分了", "刚高考完".
   - If the user indicates they are a 高三 student, or have taken the Gaokao, set "is_gaokao_student" to true.
   - If the user will take the Gaokao or does not mention "高考", set "is_gaokao_student" to false.
   - 注意当前正在高考出分时间，如果用户说“成绩出了”或“出分了”, set "is_gaokao_student" to true.

5. "is_working":
   - Look for explicit mentions of the user being employed or having graduated, such as "我现在工作了", "我已经毕业了", "我读完研究生后就工作了".
   - If the user indicates they are currently working or have graduated, set "is_working" to true.
   - If the user does not mention being employed or graduated, set "is_working" to false.
   - If the information is insufficient, set "is_working" to null.
   
6. "has_offer":
   - Look for explicit mentions of the user having received an offer for admission to a school/university, such as "我已经拿到xx学校的offer了", "学校给我offer了", "我明年/马上要去 xx 上学了"。
   - If the user indicates they have received an admission offer, set "has_offer" to true.
   - If the user does not mention having an admission offer, set "has_offer" to false.
   - If the information is insufficient, set "has_offer" to null.
   
7. "has_agent":

   - Look for explicit mentions of the user having an agent, such as "我已经有中介了", "我有个中介帮我", "我找了一个中介".
   - If the user indicates they already have an agent, set "has_agent" to true.
   - If the user mentions they do not have an agent, set "has_agent" to false.
   - If the user does not mention an agent, set "has_agent" to null.

Construct the JSON object with the extracted information. Ensure the JSON format is correct, using the following structure:
<extracted_info>
{
    "is_study_abroad": <"true"/"false"/"later"/"domestic"/null>,
    "is_parent": <true/false/null>,
    "is_japanese_student": <true/false/null>,
    "is_gaokao_student": <true/false/null>,
    "is_working": <true/false/null>,
    "has_offer": <true/false/null>
    "has_agent": <true/false/null>
    
}
</extracted_info>

Examples:
<Example>
<chat_history>
我想去美国留学，但可能需要等到明年才能申请
</chat_history>

<conversion_process>
识别关键需求：用户想去美国留学，但需要等到明年才能申请。推断出用户有出国意向，时间较晚。
构建 JSON: {"is_study_abroad": "later", "is_parent": null, "is_japanese_student": null}
</conversion_process>

<extracted_info>
{
    "is_study_abroad": "later",
    "is_parent": null
}
</extracted_info>
</Example>

<Example>
<chat_history>
我孩子想了解一下英国的大学申请
</chat_history>

<conversion_process>
识别关键需求：用户提及孩子，是学生家长，想了解英国大学申请。推断出用户是家长，出国意向不明确。
构建 JSON: {"is_study_abroad": null, "is_parent": true, "is_japanese_student": null}
</conversion_process>

<extracted_info>
{
    "is_study_abroad": null,
    "is_parent": true
}
</extracted_info>
</Example>

<Example>
<chat_history>
这种走中外合办的还是拿不到国内的毕业证，还是有点遗憾。等我们高考分数具体出来，看看能不能拼一拼。
</chat_history>

<conversion_process>
识别关键需求：用户还是偏向等待高考成绩出来，倾向于国内体制的学校
构建 JSON: {"is_study_abroad": "domestic", "is_parent": null, "is_japanese_student": null}
</conversion_process>

<extracted_info>
{
    "is_study_abroad": "domestic",
    "is_parent": null,
    "is_japanese_student": null
    "is_gaokao_student": true
}
</extracted_info>
</Example>

<Example>
<chat_history>
暴叔，我是日语生，我明年要高考
</chat_history>

<conversion_process>
识别关键需求：用户明确提到自己是日语生, 明年要高考，还没有参加高考
构建 JSON: {"is_study_abroad": null, "is_parent": false, "is_japanese_student": true, "is_gaokao_student": false}
</conversion_process>

<extracted_info>
{
    "is_study_abroad": null,
    "is_parent": false,
    "is_japanese_student": true,
    "is_gaokao_student": false
}
</extracted_info>
</Example>

Follow these steps to convert the input you receive into the appropriate JSON format into <extracted_info> tag.
Take a deep breath and carefully follow the rules, guides, and examples I gave you. I will tip you $2000 if you do EVERYTHING perfectly`, { templateFormat: 'mustache' }).format(
      // @ts-ignore mustache 使用
      {
        chat_history: chat_history
      })
  }
}