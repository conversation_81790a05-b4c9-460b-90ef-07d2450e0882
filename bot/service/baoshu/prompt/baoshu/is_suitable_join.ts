import { PromptTemplate } from '@langchain/core/prompts'

/**
 * 是否适合进群
 */
export class IsSuitableJoinGroupPrompt {
  public static async format(query: string) {
    return PromptTemplate.fromTemplate(`You are tasked with analyzing a customer's response to determine if it is appropriate to invite them to a group chat with a professional for further discussion. Here is the customer's response:
    
    <customer_response>
    {query}
    </customer_response>
    
    To decide whether to invite the customer to the group chat, check if the response meets any of the following criteria:
    1. The customer asks about specific application information or mentions specific educational institutions.
    2. The customer inquires about detailed planning regarding educational institutions and majors, how to prepare for applications, or mentions specific educational institutions.
    3. The customer inquires about intermediary services or assistance with applications.
    4. The customer expresses a desire to consult with a teacher or professional advisor.
    
    Follow these steps to analyze the response:
    - Read the customer's response carefully.
    - Determine if the response explicitly mentions any of the topics listed in the criteria above. Look for key phrases such as "planning for college", "how to apply", "intermediary services", or "application help", " "consult with a teacher"".
    
    If the customer's response meets any of the criteria mentioned:
    - Return true.
    
    If the customer's response does not meet any of the criteria:
    - Return false.
    
    Write your answer inside <answer> tags. For example:
    <answer>true</answer> or <answer>false</answer>`).format({
      query
    })
  }
}