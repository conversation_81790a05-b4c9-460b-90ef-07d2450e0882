import { PromptTemplate } from '@langchain/core/prompts'

/**
 * 意向客户判断 Prompt
 */
export class IntendedCustomerPrompt {
  public static async format(chat_history: string) {
    return PromptTemplate.fromTemplate(`You are tasked with analyzing a chat transcript to determine if the user is a potential client for a study abroad consultancy. A potential client is identified based on specific criteria related to their educational needs, preferences, budget, and willingness to engage further.

Here is the chat transcript you will analyze:

<chat_transcript>
{chat_history}
</chat_transcript>

To determine if the user is a potential client, consider the following criteria:
1. Clarity of Needs and Goals: The user should have clear educational demands and goals, such as aiming for top universities, needing a degree for career purposes, immigration, or financial goals.
2. Preference for Programs: The user should express a preference for programs outside the traditional educational system, such as study abroad or joint international programs.
3. Budget: The user should have a stable and sufficient budget, either annually or in total, to support their educational plans.
4. Positive Response to Further Communication: The user should show a positive response to an invitation for further discussion or consultation.

Analyze the chat transcript and identify statements or exchanges that indicate the user meets the above criteria. Use the following format to structure your analysis and conclusion:

<analysis>
- Clarity of Needs and Goals: [Provide specific quotes from the chat that demonstrate the user's clear needs and goals.]
- Preference for Programs: [Provide specific quotes from the chat that show the user's preference for specific educational programs.]
- Budget: [Provide specific quotes from the chat that discuss the user's budget and its stability.]
- Positive Response to Further Communication: [Provide specific quotes from the chat where the user responds positively to further communication.]
</analysis>

Based on your analysis, conclude whether the user is a potential client:

<conclusion>
Based on the criteria and the evidence provided in the chat transcript, the user is [a potential client / not a potential client]. Only response true or false.
</conclusion>

Here is an example of how your completed task might look:

<example>
<chat_transcript>
User: I am looking to apply to top universities in the US for my master's degree. I have a budget of around $100,000 for the entire course.
Consultant: That sounds great! Would you be interested in discussing this further in a detailed consultation?
User: Yes, I would love to. Please tell me more about the process.
</chat_transcript>

<analysis>
- Clarity of Needs and Goals: "I am looking to apply to top universities in the US for my master's degree."
- Preference for Programs: The user's interest in US universities implies a preference for international programs.
- Budget: "I have a budget of around $100,000 for the entire course."
- Positive Response to Further Communication: "Yes, I would love to. Please tell me more about the process."
</analysis>

<conclusion>
true
</conclusion>
</example>

Follow this structure to ensure a clear and comprehensive analysis.`).format({
      chat_history: chat_history
    })
  }
}