import { PromptTemplate } from '@langchain/core/prompts'

export class OperationGroupRecommendationPrompt {
  public static async format(chat_history: string) {
    return PromptTemplate.fromTemplate(`You will be analyzing a chat history between a user and a study abroad consultant to determine which group the user wants to join. Here is the chat history:
    
    <chat_history>
    {chat_history}
    </chat_history>
    
    Your task is to determine which group the user wants to join based on the conversation. The possible groups are:
    - 单词群
    - 低龄规划群
    - 本科规划群
    - 硕士规划群
    
    Carefully analyze the chat history and look for any indications of the user's active desire in joining a specific group. Pay attention to:
    1. Explicit mentions of wanting to join a group
    
    Guidelines for determining the desired group:
    - If the user shows interest in improving their vocabulary or language skills, they likely want to join the '单词群'
    - If the conversation involves planning for young students (e.g., primary or secondary school), the user probably wants to join the '低龄规划群'
    - If the user discusses undergraduate studies or applying to bachelor's programs, they are likely interested in the '本科规划群'
    - If the user talks about master's programs or postgraduate studies, they probably want to join the '硕士规划群'
    
    If you cannot determine which group the user wants to join based on the conversation, or if there are no clear indications of interest in any specific group, return 'null'.
    
    Provide your answer in the following format:
    <answer>
    [Group name only or 'null']
    </answer>
    
    Do not include any explanations or additional text outside the <answer> tags.`).format({
      chat_history: chat_history
    })
  }
}