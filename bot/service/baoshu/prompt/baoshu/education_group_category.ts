import { PromptTemplate } from '@langchain/core/prompts'

/**
 * 意向客户判断 Prompt
 */
export class EducationGroupCategoryPrompt {
  public static async format(currentLevelOfEducation: string, grade: string) {
    return PromptTemplate.fromTemplate(` You are tasked with classifying a customer based on their grade level and current educational background. Here are the categories you will use:
    1. 初一及以下
    2. 初二到初四
    3. 高中
    4. 本科
    5. 大专
    6. 研究生
    7. 博士
    8. 日语生
    9. 高考生
    
    <grade_level>
    {grade}
    </grade_level>
    
    <educational_background>
    {currentLevelOfEducation}
    </educational_background>
    
    To classify the customer, follow these steps:
    1. If the grade level is "初一" or below, classify as "1. 初一及以下".
    2. If the grade level is between "初二" and "初四" 或者 "初中", classify as "2. 初二到初四".
    3. If mentions "High School" or 高中的年级 or '职高', '中专', '技校', classify as "3. 高中".
    4. If mentions "本科" 或 "大一", "大二", "大三", "大四" 或 双非 或 一本 二本 或 985 或 211, classify as "4. 本科".
    5. If mentions "大专" 或 "专科", classify as "5. 专科".
    6. If mentions "研究生" 或 "硕士", classify as "6. 研究生".
    7. If mentions "博士" 或 "博士后", classify as 7. 博士".
    
    Based on the inputs, determine the most appropriate category. If there are overlaps, choose the highest educational level mentioned.
    
    Write your classification inside <answer> tags. For example:
    <answer>
    3. 高中
    </answer>
    
    If the information provided does not clearly fit any category, respond with:
    <answer>
    Unable to classify based on the provided information.
    </answer>`).format({
      grade: grade,
      currentLevelOfEducation: currentLevelOfEducation
    })
  }
}