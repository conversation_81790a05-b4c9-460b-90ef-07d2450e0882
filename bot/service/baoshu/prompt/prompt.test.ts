import { LLM } from '../../../lib/ai/llm/LLM'
import { IsSuitableJoinGroupPrompt } from './baoshu/is_suitable_join'
import { XMLHelper } from '../../../lib/xml/xml'
import { JSONHelper } from '../../../lib/json/json'
import { ExtractEducationLevelPrompt } from './baoshu/extract_education_level'
import { DoctorCategorizePrompt } from './baoshu/doctor_categorize'

describe('Test', function () {
  beforeAll(() => {

  })

  it('博士分类', async () => {
    const prompt = await DoctorCategorizePrompt.format(`那个，我本科普通一本，现在在这个哈萨克斯坦国立大学的新闻学读研，就是感觉以后对这个未来很迷茫
之前考公失败，我父母说有关系可以进安徽省电视台，但是那个待遇。。。`)
    const openai = new LLM()
    const llmRes = await openai.predict(prompt)

    console.log(llmRes)
  }, 60000)

  it('测试', async () => {
    const prompt = await IsSuitableJoinGroupPrompt.format('韩国有什么推荐的学校么')
    const openai = new LLM()
    const llmRes = await openai.predict(prompt)

    const xmlRes = XMLHelper.extractContent(llmRes, 'answer')
    if (!xmlRes) {
      return false
    }

    const jsonRes = JSONHelper.parse(xmlRes)
    console.log(typeof jsonRes)
  }, 30000)

  it('prompt 测试', async () => {
    console.log(await LLM.predict(await ExtractEducationLevelPrompt.format('我是滑铁卢本硕，想问下孩子的规划，孩子刚上幼儿园')))
  }, 60000)

  it('12321', async () => {
    const prompt = await ExtractEducationLevelPrompt.format('我是滑铁卢本硕，想问下孩子的规划，孩子刚上幼儿园')
    const llmRes = await LLM.predict(prompt)
    const xmlRes = XMLHelper.extractContent(llmRes, 'extracted_info')

    if (!xmlRes) {
      return null
    }

    const regex = /"current_level_of_education"\s*:\s*"(低龄|小学|初中|职高|中专|技校|高中|大专|本科|硕士|博士)"/
    const match = xmlRes.match(regex)
    if (match) {
      console.log(match[1])
      return match[1]
    }

    return null
  }, 60000)
})