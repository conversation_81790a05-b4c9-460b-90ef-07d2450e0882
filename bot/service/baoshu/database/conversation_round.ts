// import { PrismaMongoClient } from '../../mongodb/prisma'
// import { UUID } from '../../../lib/uuid/uuid'
// import { ChatDB } from './chat'
// import { IDBBaseMessage } from '../components/chat_history'
//
// import { Config } from '../../../config/config'
// import { ChatStateStore, ChatStatStoreManager } from '../storage/chat_state_store'
// import { AoChuang } from '../../../lib/auchuang/openapi/aochuang'
// import { JuziAPI } from '../../../lib/juzi/api'
//
// interface IStageInfo {
//     name: string
//     input: string
//     output: string
//     chat_history: IDBBaseMessage[]
// }
//
// export enum SourceType {
//     Conversation = '对话',
//     Welcome = '欢迎语',
//     PushAsk = '追问',
//     AdminTest = '后台测试',
//     HumanSend = '人工发送'
// }
//
// export class ConversationRound {
//   private input: string = ''
//   private output: string = ''
//   private readonly source: SourceType
//   private stages: IStageInfo[] = []
//   public round_id: string
//   private score: string | undefined
//   private messages: IDBBaseMessage[] = []
//
//   constructor(private chat_id: string, private senderId: string,  source = SourceType.Conversation) {
//     this.round_id = UUID.short()
//     this.source = source
//   }
//
//   public addStage(stage: IStageInfo) {
//     this.stages.push(stage)
//   }
//
//   public setInput(input: string) {
//     this.input = input
//   }
//
//   public setOutput(output: string) {
//     this.output += `\n${  output}`
//   }
//
//   public setScore(score: string) {
//     // 分数格式为 +/- 分数/总分
//     this.score = score
//   }
//
//   public async save() {
//     if (this.source === SourceType.AdminTest) {
//       return
//     }
//
//     await ChatStatStoreManager.saveState(this.chat_id)
//
//     if (await ChatDB.getById(this.chat_id) === null) {
//       const currentSender = await JuziAPI.getCustomerInfo(Config.setting.wechatConfig?.id as string, this.senderId)
//       if (!currentSender) {
//         throw new Error('发送方不存在')
//       }
//
//       console.log(currentSender.name)
//
//       await ChatDB.create({
//         id: this.chat_id,
//         round_ids: [],
//         contact: {
//           wx_id: this.senderId,
//           wx_name: currentSender.name,
//         },
//         wx_id: Config.setting.wechatConfig?.id as string,
//         created_at: new Date(),
//       })
//     }
//
//     await PrismaMongoClient.getInstance().conversation_round_log.create({
//       data: {
//         id: this.round_id,
//         chat_id: this.chat_id,
//         input: this.input,
//         output: this.output,
//         source: this.source,
//         created_at: new Date(),
//         chat_history: this.messages,
//         chat_state: ChatStateStore.get(this.chat_id)
//       }
//     })
//
//     await ChatDB.pushRoundId(this.chat_id, this.round_id)
//   }
//
//   public setChatHistory(messages: IDBBaseMessage[]) {
//     this.messages = messages
//   }
// }