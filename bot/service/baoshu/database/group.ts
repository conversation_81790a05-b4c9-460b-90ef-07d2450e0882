import { PrismaMongoClient } from '../../../model/mongodb/prisma'


export class GroupDB {
  // 获取当前要推送的群
  public static async getCurrentGroup() {
    return PrismaMongoClient.getInstance().group.findFirst({
      where: {
        isCurrent: true,
      },
    })
  }

  // 切换要推送的群
  public static async switchGroup(groupId: string, groupName: string) {
    // 首先取消当前群组的推送状态
    await PrismaMongoClient.getInstance().group.updateMany({
      where: {
        isCurrent: true,
      },
      data: {
        isCurrent: false,
      },
    })

    // 然后设置新的推送群组
    return PrismaMongoClient.getInstance().group.upsert({
      where: {
        id: groupId,
      },
      update: {
        isCurrent: true,
      },
      create: {
        id: groupId,
        name: groupName,
        isCurrent: true,
      },
    })
  }
}