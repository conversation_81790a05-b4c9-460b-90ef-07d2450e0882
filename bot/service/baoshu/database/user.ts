import { PrismaMongoClient } from '../../../model/mongodb/prisma'


interface WechatUser {
    id: string
    name: string
    avatar: string | null
}

export class WechatAccountDB {
  public static async create(user: WechatUser): Promise<WechatUser> {
    return PrismaMongoClient.getInstance().account.create({
      data: user
    })
  }

  public static async getById(id: string): Promise<WechatUser | null> {
    return PrismaMongoClient.getInstance().account.findUnique({
      where: {
        id
      }
    })
  }
}