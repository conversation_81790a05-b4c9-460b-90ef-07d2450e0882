import { PrismaMongoClient } from '../../../model/mongodb/prisma'


interface NewFriend {
    id: string
    friendId: string
    name: string
    wechatId: string // 当前账号的 wechatId
}

export class NewFriendDB {
  public static async getByTaskId(taskId: string): Promise<null | NewFriend> {
    return PrismaMongoClient.getInstance().new_friend.findUnique({
      where: {
        id: taskId
      }
    })
  }

  public static async create(friend: NewFriend) {
    return PrismaMongoClient.getInstance().new_friend.create({
      data: friend
    })
  }
}