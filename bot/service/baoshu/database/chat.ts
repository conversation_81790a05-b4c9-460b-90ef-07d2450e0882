import { IChatState } from '../storage/chat_state_store'
import { PrismaMongoClient } from '../../../model/mongodb/prisma'

interface Chat {
    id: string
    round_ids: string[]
    contact: {
        wx_id: string
        wx_name: string
    }
    wx_id: string
    created_at: Date | null
    chat_state: IChatState
}
export class ChatDB {
  public static async updateState(chat_id: string, chatState: IChatState) {
    return PrismaMongoClient.getInstance().chat.update({
      where: {
        id: chat_id
      },
      data: {
        chat_state: chatState
      }
    })
  }

  public static async create(chat: Chat): Promise<Chat> {
    // @ts-ignore ignore
    return PrismaMongoClient.getInstance().chat.create({
      data: chat
    })
  }

  public static async pushRoundId(chatId: string, roundId: string) {
    if (!await this.getById(chatId)) {
      return
    }

    return PrismaMongoClient.getInstance().chat.update({
      where: {
        id: chatId
      },
      data: {
        round_ids: {
          push: roundId
        }
      }
    })
  }

  public static async getById(id: string): Promise<Chat | null> {
    // @ts-ignore type ignore
    return PrismaMongoClient.getInstance().chat.findUnique({
      where: {
        id
      }
    })
  }

  public static async isHumanInvolvement(chatId: string): Promise<boolean> {
    const chat = await PrismaMongoClient.getInstance().chat.findUnique({
      where: {
        id: chatId
      }
    })

    if (chat) {
      return Boolean(chat.is_human_involved)
    }

    return false
  }

  public static async setHumanInvolvement(chatId: string, isHumanInvolved: boolean) {
    return PrismaMongoClient.getInstance().chat.update({
      where: {
        id: chatId
      },
      data: {
        is_human_involved: isHumanInvolved
      }
    })
  }

}