import { ChatState } from './chat_state'

import { ChatDB } from '../database/chat'
import { UserEducationGroup } from '../components/flow/nodes/intentionCalculate'
import { IProject } from '../../elastic_search/baoshu_projects'

export enum ConversationState { // 会话状态，用于替换 Flag, 假设同一时间同一用户只能有一个状态，并发下会进入到同一节点
  Start = 'Start', // 开始
  Pending = 'Pending', // 临时中间态
  End = 'Ended', // 结束

  ConsultGroupInvited = 'Consult Group Invited', // 邀请了进入顾问群
  ConsultGroupEntered = 'Consult Group Entered', // 进入了顾问群
  OperationGroupInvited = 'Operation Group Invited', // 邀请了进入运营群
  OperationGroupEntered = 'Operation Group Entered', // 进入了运营群

  FormFilled = 'Form Filled', // 填写了表单
  ConfirmedIsParent = 'Confirmed Is Parent', // 邀请家长进行聊天
  AskedParentSupport = 'Asked Parent Support', // 询问家长支持
  BudgetSupplemented = 'Budget Supplemented', // 已补充了预算预算信息补充
}

interface IUserStatus {
  [key: string]: boolean | undefined
  consultGroupInvited?: boolean
  consultGroupEntered?: boolean
  operationGroupInvited?: boolean
  operationGroupEntered?: boolean

  labeledUser?: boolean

  doctorRoute?: boolean
  budgetSupplemented?: boolean
  is_update_is_parent_support?: boolean

  is_send_promotion_poster?: boolean // 是否发送了香港活动海报
}

export interface IChatState {
  slotAskedCount: Record<string, number> // 记录每个槽位被问的次数
  nodeInvokeCount: Record<string, number> // 记录每个节点被调用的次数
  nextStage: string

  pickedMiddleProject?: string // 中间根据用户槽位选出的项目
  projects?: IProject[] // 匹配出来的项目
  userSlots?: IUserSlot // 用户具体信息槽位

  intentions: string[] // 记录初始阶段推测的意图
  state: string // 会话状态，已拉群，已邀请入群等

  status?: IUserStatus // 会话状态，已拉群，已邀请入群等
  counselor_id?: string // 拉给的顾问
}

export interface IUserSlot { // 用户信息槽位参考
  [key: string]: any  // 注意这里是为了 prisma 类型检查，不要删除
  is_japanese_student?: boolean
  application_status?: '未知' | '未开始' | '申请中' | '已完成'
  budget?: number | null
  budget_is_per_year?: boolean
  gpa?: string
  language_test_score?: string
  current_level_of_education?: '低龄' | '小学' | '初中' | '职高' | '中专' | '技校' | '高中' | '大专' | '本科' | '硕士' | '博士'
  grade?: '一年级' | '二年级' | '三年级' | '四年级' | '五年级' | '六年级' | '初一' | '初二' | '初三' | '初四' | '高一' | '高二' | '高三' | '大一' | '大二' | '大三' | '大四' | '研一' | '研二' | '研三'
  goal?: string
  user_intended_country?: string[]
  application_stage?: '高中' | '大专' | '本科' | '硕士' | '博士'
  preferred_plan?: string
  city?: string
  school?: string
  major?: string
  user_intended_school?: string[]
  user_intended_project?: string[]
  is_study_abroad?: 'true' | 'false' | 'later' | 'domestic' | boolean
  is_parent?: boolean
  education_group?: UserEducationGroup
  has_agent?: boolean
}


export class ChatStatStoreManager {
  private static stateInitialized = new ChatState<boolean>()

  public static async initState(chat_id: string) {
    if (!this.stateInitialized.get(chat_id) && await ChatDB.getById(chat_id)) { // 从数据库中读取配置，进行覆盖
      const chat = await ChatDB.getById(chat_id)
      if (!chat)
        return

      ChatStateStore.set(chat_id, chat.chat_state)
      this.stateInitialized.set(chat_id, true)
    }

  }

  public static async clearState(chat_id: string) { // 强行将内存中状态重置
    ChatStateStore.clear(chat_id)
  }
}


/**
 * 这里的 State 是内存中的状态，每轮对话后，刷新到数据库中
 * 初始化时后，可以从数据库中读取
 */
export class ChatStateStore {
  private static state = new ChatState<IChatState>()

  public static hasState(chat_id: string) {
    return this.state.get(chat_id) !== undefined
  }

  public static get(chat_id: string) {
    // 数据库读取
    if (!this.state.has(chat_id)) {
      this.state.set(chat_id, {
        nextStage: 'intention_calculate',
        nodeInvokeCount: {},
        slotAskedCount: {},
        intentions: [],
        state: ConversationState.Start
      })

    }

    return this.state.get(chat_id) as IChatState
  }

  public static clear(chat_id: string) {
    this.state.delete(chat_id)
  }

  /**
   * 注意 name 传递的是类名
   * @param chat_id
   * @param name
   */
  public static getNodeEnterCount(chat_id: string, name: string) {
    const state = this.get(chat_id)
    if (!state.nodeInvokeCount[name]) {
      state.nodeInvokeCount[name] = 0
    }
    return state.nodeInvokeCount[name]
  }
  public static increNodeCount(chat_id: string, name: string) {
    const state = this.get(chat_id)
    // 初始化节点计数为0（如果还不存在）
    if (!state.nodeInvokeCount[name]) {
      state.nodeInvokeCount[name] = 0
    }
    // 增加调用次数
    state.nodeInvokeCount[name] += 1
    // 保存更新后的 state
    this.set(chat_id, state)
  }
  public static getNodeCount(chat_id: string, name: string) {
    const state = this.get(chat_id)
    if (!state.nodeInvokeCount[name]) {
      state.nodeInvokeCount[name] = 0
    }
    return state.nodeInvokeCount[name]
  }

  public static set(chat_id: string, state: IChatState) {
    this.state.set(chat_id, state)
  }

  public static update (chat_id: string, partialState: Partial<IChatState>): void {
    const currentState = this.get (chat_id)
    const updatedState = this.deepMerge (currentState, partialState)
    this.set (chat_id, updatedState as IChatState)
  }

  private static deepMerge (target: any, source: any): any {
    const output = { ...target }
    if (Array.isArray(target) && Array.isArray(source)) {
      return source
    } else if (typeof target === 'object' && typeof source === 'object') {
      for (const key in source) {
        if (source [key] instanceof Object && target instanceof Object && key in target) {
          output [key] = this.deepMerge (target [key], source [key])
        } else {
          output [key] = source [key]
        }
      }
    }
    return output
  }

  public static getStatus(chat_id: string): IUserStatus {
    if (!this.get(chat_id).status) {
      this.get(chat_id).status = {}
    }

    return this.get(chat_id).status as IUserStatus
  }

  /**
   * 获取当前状态的描述字符串
   * @param chat_id
   * @returns {string}
   */
  public static getDescription(chat_id: string): string {
    const userSlots = this.get(chat_id)?.userSlots
    const keys: string[] = ['current_level_of_education', 'grade', 'goal', 'user_intended_country', 'application_stage', 'preferred_plan', 'city', 'school', 'major', 'user_intended_school', 'user_intended_project']
    const values: string[] = []
    for (const key of keys) {
      const value = userSlots?.[key]
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          values.push(...value.map((v) => String(v)))
        } else {
          values.push(String(value))
        }
      }
    }
    return values.join(', ')
  }
}