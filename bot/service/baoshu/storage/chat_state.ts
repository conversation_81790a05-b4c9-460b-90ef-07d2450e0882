export class ChatState<T> {
  private _state: Map<string, T>

  constructor() {
    this._state = new Map<string, T>()
  }

  public has(chat_id: string): boolean {
    return this._state.has(chat_id)
  }

  public get(chat_id: string): T | undefined {
    return this._state.get(chat_id)
  }

  public set(chat_id: string, value: T) {
    this._state.set(chat_id, value)
  }

  public delete(chat_id: string) {
    this._state.delete(chat_id)
  }

}