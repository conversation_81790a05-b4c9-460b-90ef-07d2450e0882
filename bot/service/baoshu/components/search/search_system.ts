import { LLM } from '../../../../lib/ai/llm/LLM'
import { XMLHelper } from '../../../../lib/xml/xml'
import { ObjectUtil } from '../../../../lib/object'
import { RegexHelper } from '../../../../lib/regex/regex'
import { UserCategorizePrompt } from '../../prompt/baoshu/user_categorize'
import { UserEducationGroup } from '../flow/nodes/intentionCalculate'
import { BaoshuProjectSearch, IElasticProjectSearchRes, IProject } from '../../../elastic_search/baoshu_projects'
import logger from '../../../../model/logger/logger'

interface ISearchProjectResult {
  projects: IProject[]  // 找到项目了，推荐一个项目 -> 继续聊天
  userSlots?: Record<string, any> // 用户信息

  humanInvolve?: boolean // 没找到匹配项目，预算够 -> 直接拉顾问群
  lowBudget?: boolean // 没找到匹配项目，预算不够 -> 非目标用户
  askBudget?: boolean // 没提预算 -> 询问预算
  askApplicationStage?: boolean // 未知阶段 -> 询问申请什么
  askCurrentLevelOfEducation?: boolean // 当前学历

  askUserInfo?: boolean // 用户信息为空 -> 询问用户信息
}

export interface IExtractUserSlotsResult {
  budget?: number | number[]
  budget_is_per_year?: boolean
  education_group: UserEducationGroup
  gpa?: string
  country?: string | string[]
  applicationStage?: string

  languageTestScore?: string
  currentLevelOfEducation?: string // 当前学历
  currentEducationBackground?: string // 当前学历背景

  requirementType?: string // 目标用户类型
}

export interface IStoredUserSlots { // 处理过的用户信息，用于数据库存储
  budget?: number
  budget_is_per_year?: boolean
  gpa?: string
  country?: string
  applicationStage?: string

  languageTestScore?: string
  currentLevelOfEducation?: string // 当前学历
  currentEducationBackground?: string // 当前学历背景
}


export class SearchSystem {
  public static async searchProject(userSlots: Record<string, any> | undefined): Promise<ISearchProjectResult> {
    // 提取用户信息转为 ES SQL 查询
    if (!userSlots || ObjectUtil.isEmptyObject(userSlots)) {
      throw new Error('userSlots is null')
    }

    let searchRes = await BaoshuProjectSearch.search(userSlots)
    let projects = searchRes.projects

    if (projects.length === 0) {
      const maxBudget = Array.isArray(userSlots.budget) ?  Math.max(...userSlots.budget) : userSlots.budget
      if (maxBudget < 10 && userSlots.budget_is_per_year) { // 年预算 < 10
        return {
          projects: [],
          lowBudget: true
        }
      }

      if ((userSlots.application_stage === '硕士' || userSlots.education_group === UserEducationGroup.University)) {
        if (maxBudget < 15 && !userSlots.budget_is_per_year) { // 硕士总预算 < 15
          return {
            projects: [],
            lowBudget: true
          }
        }
      } else if (maxBudget < 30 && !userSlots.budget_is_per_year) { // 总预算 < 30
        return {
          projects: [],
          lowBudget: true
        }
      }

      // 否则，再粗筛下项目
      searchRes = await BaoshuProjectSearch.search({
        budget: maxBudget + 10,
        education_group: userSlots.education_group
      })

      projects = searchRes.projects
    }

    if (projects.length === 0) {
      return { // 预算够，但没找到匹配项目
        projects: [],
        humanInvolve: true
      }
    }

    const formatProjects = this.formatProjects(projects)

    return {
      projects: formatProjects,
      userSlots: userSlots
    }
  }

  private static formatProjects(projects: IElasticProjectSearchRes[]) {
    return projects.map((project) => ObjectUtil.removeEmptyKeys(project._source)) as IProject[]
  }

  private static async getUserCategory(chat_history: string) {
    const llm  = new LLM()
    const prompt = await UserCategorizePrompt.format(chat_history)
    const llmRes = await llm.predict(prompt)
    const answer = XMLHelper.extractContent(llmRes, 'classification')
    const categories = ['学霸登顶', '普娃冲名校', '差生拿文凭', '留学移民', '低龄规划']

    if (!answer) {
      logger.error('answer is null', llmRes)
      return categories[1]
    }

    const categoryIndex =  RegexHelper.extractNumber(answer)

    if (!categoryIndex || categoryIndex < 1 || categoryIndex > categories.length) {
      logger.error('categoryIndex is null', answer)
      return categories[1]
    }

    return categories[categoryIndex - 1]
  }
}