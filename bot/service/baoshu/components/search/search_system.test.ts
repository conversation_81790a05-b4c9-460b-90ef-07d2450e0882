import { SearchSystem } from './search_system'
import { UUID } from '../../../../lib/uuid/uuid'
import { ChatHistoryService } from '../chat_history'

describe('Test', function () {
  beforeAll(() => {
  })

  it('search', async () => {
    console.log(JSON.stringify(await SearchSystem.searchProject({
      education_group: 2,
      budget: 40,
      budget_is_per_year: false,
      is_study_abroad: 'true',
    }), null, 4))
  }, 1E8)

  it('searchSystem', async () => {
    // await search('大专，预算10w够不够')
  }, 30000)

  it('reRankProjects', async () => {
    console.log(JSON.stringify(await (SearchSystem as any).reRankProjects([{
      name: '美国留学',
      description: '冲顶级名校，费用较高，需要提前规划',
    }, {
      name: '韩国留学',
      description: '费用不高，好申请，但是学历认可度不是很高',
    }], {
      'budget': 10,
      'currentLevelOfEducation': '本科',
      'currentEducationBackground': '二本大三在读',
      'applicationStage': '硕士',
      'budget_is_per_year': false
    }), null, 4))
  }, 30000)

  it('category user', async () => {
    console.log(JSON.stringify(await (SearchSystem as any).getUserCategory('暴叔，你好，我今年31了，当年是成教大专毕业，工作了很多年，也做了几年管理'), null, 4))
  }, 30000)

  it('test', async () => {
    interface IProject {
      projectName: string
      // 其他项目属性
    }

    const order = ['英国方向', '澳洲方向', '香港方向', '新加坡方向', '美国方向', '国内念中外合作办学硕士']

    function sortProjectsByOrder(projects: IProject[], order: string[]): IProject[] {
      // 创建一个 Map 来存储 order 中的项目名及其顺序
      const orderMap = new Map(order.map((name, index) => [name, index]))

      // 对 projects 进行排序
      return projects.sort((a, b) => {
        const orderA = orderMap.get(a.projectName)
        const orderB = orderMap.get(b.projectName)

        // 如果 order 中没有该项目，则放到后面
        if (orderA === undefined) return 1
        if (orderB === undefined) return -1

        // 按照 order 中的顺序排序
        return orderA - orderB
      })
    }

    // 示例项目列表
    const projects: IProject[] = [
      { projectName: '美国方向' },
      { projectName: '英国方向' },
      { projectName: '其他方向' },
      { projectName: '澳洲方向' },
    ]

    // 调用函数进行排序
    const sortedProjects = sortProjectsByOrder(projects, order)
    console.log(sortedProjects)
  }, 60000)
})