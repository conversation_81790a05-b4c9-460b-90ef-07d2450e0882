import { IMessage } from '../../message/message'
import { ChatHistoryService } from './chat_history'
import { sleep } from '../../../lib/schedule/schedule'
import { Config } from '../../../config/config'
import chalk from 'chalk'
import { JuziAPI } from '../../../lib/juzi/api'
import { IWecomMessage, IWecomMsgType } from '../../../lib/juzi/type'
import logger from '../../../model/logger/logger'

interface IWechatSendMsg {
    user_id?: string
    room_id?: string // 群聊的 id
    chat_id: string
    ai_msg: string // 给 openai 发送的消息，大部分情况下与 send_msg 相同， 当 ai_msg 和 send_msg 不同时，可以单独设置 send_msg。当发送文件或资料时，此处有可能是占位符。
    send_msg?: IWecomMessage
    mention?: string[] // 被 @ 的人
}

export class WechatMessageSender {

  public static async sendById(msg: IWechatSendMsg, addBotMessage = true) {
    if (!msg.ai_msg) {
      return
    }

    // 如果是后台测试，不发送消息
    if (Config.setting.localTest) {
      // logger.log(msg.chat_id, chalk.greenBright(msg.ai_msg)) // add Bot Message 中会 Print 消息
    } else {
      let sendMsg: IWecomMessage
      if (!msg.send_msg) {
        sendMsg =  {
          type: IWecomMsgType.Text,
          text: msg.ai_msg,
          mention: msg.mention
        }
      } else {
        sendMsg = msg.send_msg
      }

      if (msg.room_id) {
        logger.debug('发送群消息', {
          imBotId: Config.setting.wechatConfig?.id as string,
          imContactId: msg.user_id,
          imRoomId: msg.room_id,
          msg: sendMsg
        })
      }

      await JuziAPI.sendMsg({
        imBotId: Config.setting.wechatConfig?.id as string,
        imContactId: msg.user_id,
        imRoomId: msg.room_id,
        msg: sendMsg
      })
    }

    if (addBotMessage) {
      await ChatHistoryService.addBotMessage(msg.chat_id, msg.ai_msg)
    }
  }

  public static getWxSendMessageFunction(chat_id: string, user_id: string) {
    // 返回一个异步函数，该函数将执行发送消息的行为
    return async (sentence: string, isFirstLine: boolean) => {
      if (!isFirstLine) { // 第一次回复除外
        await sleep(0.6 * sentence.length * 1000)
      }
      await WechatMessageSender.sendById({ user_id: user_id, chat_id: chat_id, ai_msg: sentence })
    }
  }
}
