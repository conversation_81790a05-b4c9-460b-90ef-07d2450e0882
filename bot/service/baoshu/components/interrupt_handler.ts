import { ChatHistoryService, IDBBaseMessage } from './chat_history'
import { HashSum } from '../../../lib/hash/hash'
import logger from '../../../model/logger/logger'


export class InterruptError extends Error {
  constructor(message: string) {
    super(message)
    this.name = 'InterruptError'
  }
}


export class HashMessagesHandler {
  private readonly hashedMessageKey: string

  constructor(history: IDBBaseMessage[]) {
    this.hashedMessageKey = HashMessagesHandler.getHashedUserMessages(history)
  }


  public static getHashedUserMessages(history: IDBBaseMessage[]): string {
    const userHistory: string[] = []

    history.forEach((message) => {
      if (message.role === 'user') {
        userHistory.push(message.content)
      }
    })

    return HashSum.hash(userHistory.join(''))
  }

  public async interruptProcess(chat_id: string) {
    const chatHistory = await ChatHistoryService.getChatHistory(chat_id)

    if (this.isSameUserMessage(chatHistory)) {
      return
    }

    logger.trace({ chat_id }, '当前用户有新消息，当前流程被打断')
    throw new InterruptError('当前用户有新消息，当前流程被打断')
  }

  public async hasNewUserMessage(chat_id: string) {
    const chatHistory = await ChatHistoryService.getChatHistory(chat_id)

    return !this.isSameUserMessage(chatHistory)
  }

  private isSameUserMessage(history: IDBBaseMessage[]): boolean {
    return this.hashedMessageKey === HashMessagesHandler.getHashedUserMessages(history)
  }


}