import { IntentionCalculateNode, UserEducationGroup } from '../intentionCalculate'
import { JuziAPI } from '../../../../../../lib/juzi/api'
import { Config } from '../../../../../../config/config'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    // console.log(await IntentionCalculateNode.getCountryBudgetList(UserEducationGroup.University))
  }, 60000)

  it('ai 拉群测试', async () => {
    await JuziAPI.createRoom({
      botUserId: 'BaoShuXiaoZhuShou_3',
      userIds: ['WuYuCong'],
      name: '对接群',
      greeting: 'AI拉群测试',
      externalUserIds: ['wmagdSKgAAwax00OkOQH81zvMYndO5sQ']
    })
  }, 60000)
})