import { Config } from '../../../../../../config/config'
import { UUID } from '../../../../../../lib/uuid/uuid'
import { getChatId } from '../../../../../../config/chat_id'
import { InternalPlanNode, SelfPlanNode } from '../studyAbroadPlan'
import { ChatStateStore, ConversationState } from '../../../../storage/chat_state_store'
import { ChatHistoryService } from '../../../chat_history'
import { HashMessagesHandler } from '../../../interrupt_handler'
import { schoolsToCountries } from '../helper/getProjectBudgets'

describe('Test', function () {
  beforeAll(() => {
  })

  it('should pass', async () => {
    Config.setting.localTest = true
    const user_id = UUID.short()
    const chat_id = getChatId(user_id)
    ChatStateStore.set(chat_id, {
      nextStage: 'intention_calculate',
      slotAskedCount: {},
      nodeInvokeCount: {},
      userSlots: {
        education_group: 2,
        is_study_abroad: 'true',
        budget: 1000,
      },
      state: ConversationState.Start,
      intentions: []
    })

    await ChatHistoryService.setChatHistory(chat_id, [
      { role: 'user', content: '我高三，成绩还不错，能去哪里，大概 1000w 预算' },
    ])

    await InternalPlanNode.invoke({
      chat_id,
      user_id,
      userMessage: '推荐几个项目给我',
      interruptHandler: new HashMessagesHandler([]),
      round_id: UUID.short()
    })
  }, 60000)

  it('schools to countries', async () => {
    console.log(await schoolsToCountries(['MIT', '滑铁卢']))
  }, 60000)
})