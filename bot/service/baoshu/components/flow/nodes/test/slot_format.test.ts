import { UserSlotsExtract } from '../helper/slotsExtract'
import { cityToProvince } from '../helper/xbbHelper'
import { BudgetFromCounselorPrompt } from '../../../../prompt/baoshu/budget_form_counselor'
import { LLMXMLHelper } from '../helper/xmlHelper'
import { InviteToGroupHelper } from '../helper/inviteToGroup'
import { Config } from '../../../../../../config/config'
import { DailyCounter } from '../helper/countByDate'

describe('Test', function () {
  beforeAll(() => {

  })

  it('test', async () => {
    // console.log(await DailyCounter.getCount('Fk'))
    // console.log(await DailyCounter.incrementCount('Fk'))
    // console.log(await DailyCounter.getCount('Fk'))
  }, 60000)

  it('should pass', async () => {
    console.log(await UserSlotsExtract.formatSlots({
      'budget': 100,
      'budget_is_per_year': false,
      'current_level_of_education': '本科',
      'gpa': '73',
      'goal': '出国读研',
      'city': '烟台',
      'school': '滑铁卢',
      'is_study_abroad': 'true',
      'is_parent': false,
      'is_japanese_student': false,
      'is_gaokao_student': false
    }))
  }, 60000)

  it('city to Province', async () => {
    console.log(await cityToProvince('哈哈哈哈'))
    console.log(await cityToProvince('莱州'))
    console.log(await cityToProvince('北京'))
    console.log(await cityToProvince('滑铁卢'))
  }, 60000)

  it('预算提取', async () => {
    // 使用模型判断下 新预算是否是从 顾问侧提取的
    const prompt = await BudgetFromCounselorPrompt.format(String('10万'), '暴叔: 韩国大约每年10w, 英美每天50w')
    const res = await LLMXMLHelper.extractBooleanAnswer(prompt, { tagName: 'Result', trueFlag: 'YES', falseFlag: 'NO', model: 'gpt-4o-mini' })
    console.log(res)
  }, 60000)
})

function deepMerge (target: any, source: any): any {
  const output = { ...target }
  if (Array.isArray(target) && Array.isArray(source)) {
    return source
  } else if (typeof target === 'object' && typeof source === 'object') {
    for (const key in source) {
      if (source [key] instanceof Object && target instanceof Object && key in target) {
        output [key] = deepMerge (target [key], source [key])
      } else {
        output [key] = source [key]
      }
    }
  }
  return output
}

describe('Test', function () {
  beforeAll(() => {
    Config.setting.wechatConfig =   {
      orgToken: 'e0d70927040a4efa92b79b7279ecb1c1',
      id: '1688858091484181',
      botUserId: 'BaoShuXiaoZhuShou_3',
      name: '暴叔',
      counselorIds: ['Ming'],
      notifyGroupId: 'R:10829337560927503'
    }
  })

  it('should pass', async () => {
    console.log(JSON.stringify(deepMerge({
      a: 1,
      b: 2,
      c: {
        d: 3
      }
    }, {
      b: 4,
      c: {
        e: 5
      }
    }), null, 4))
  }, 60000)

  it('groupName', async () => {
    const groupName = await InviteToGroupHelper.getGroupName('7881300007300759')

    console.log(groupName)
  }, 60000)
})