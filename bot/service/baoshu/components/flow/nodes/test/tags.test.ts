import { XiaoBangBangAPI } from '../../../../../../lib/xiaobangbang/xiaobangbang'
import { XbbHelper } from '../helper/xbbHelper'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    // 打标签
    const xbbUsers = await XiaoBangBangAPI.getCluesByName('Sunny')
    console.log(JSON.stringify(xbbUsers, null, 4))

    // 获取当前时间的时间戳
    const now = new Date('2024-10-20').getTime() / 1000

    // 使用 reduce 找出与当前时间最接近的客户
    const closestUser = xbbUsers.list.reduce((prev, curr) => {
      return Math.abs(curr.addTime - now) < Math.abs(prev.addTime - now) ? curr : prev
    }, xbbUsers.list[0])

    console.log('离当前时间最近的客户:', JSON.stringify(closestUser, null, 4))
  }, 60000)

  it('', async () => {
    console.log(new Date(1729432807 * 1000).toLocaleString())
  }, 60000)

  it('测试', async () => {
    // const xbbUser = await XbbHelper.getLastClueByName('Sunny', new Date())
    // console.log(JSON.stringify(xbbUser, null, 4))

    console.log(JSON.stringify(await XiaoBangBangAPI.getCluesByName('Sunny'), null, 4))
  }, 60000)
})