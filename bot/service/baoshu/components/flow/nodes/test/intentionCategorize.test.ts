import { IntentionCalculateNode, UserIntentionType } from '../intentionCalculate'
import { FillFormNode } from '../fillForm'
import { UUID } from '../../../../../../lib/uuid/uuid'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    //     console.log(await IntentionCalculateNode.intentionCategorize('test', `感觉自己寄了
    // 怎么办暴叔
    // 稳不了了`))o
    const type = await IntentionCalculateNode.intentionCategorize(UUID.short(), '高')

    console.log(type === UserIntentionType.DirectFindCounselor)
  }, 60000)


  it('isFormFilled should pas', async () => {
    console.log(JSON.stringify(await (FillFormNode as any).isFormFilled('用户: 宝贝，先补充下这个表，方便看看你的情况：\n' +
        '姓名：王\n' +
        '电话：\n' +
        '年级：高三\n' +
        '高考总分：457\n' +
        '英语成绩：93\n' +
        '所在城市：杭州\n' +
        '想【国际本科/中外合办】还是【海外留学】：都不太了解\n' +
        '本科总预算（学费+生活费）：40\n' +
        '意向专业（文商科/理工科/艺术类）：都可以，但是高中读的文科\n'), null, 4))
  })
})