import { Config } from '../../../../../../config/config'
import { <PERSON>bb<PERSON><PERSON><PERSON> } from '../helper/xbbHelper'
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '../helper/juziHelper'
import { XbbFormId, XiaoBangBangAPI } from '../../../../../../lib/xiaobangbang/xiaobangbang'
import { uuid } from 'short-uuid'
import { getChatId } from '../../../../../../config/chat_id'
import { ChatStateStore } from '../../../../storage/chat_state_store'
import logger from '../../../../../../model/logger/logger'

describe('Test', function () {
  beforeAll(() => {
    Config.setting.wechatConfig = {
      orgToken: 'e0d70927040a4efa92b79b7279ecb1c1',
      id: '1688855135509802',
      botUserId: 'momoLiao<PERSON>iuXue',
      name: '支持组2(暴叔)',
      notifyGroupId: 'R:10840704064745792',
      counselorIds: []
    }
    Config.setting.localTest = false
  })


  it('', async () => {
    await XbbHelper.transferClueToCustomer('7881300493936853_1688855135509802', '7881300493936853')
  }, 60000)

  it('see', async () => {
    console.log(JSON.stringify(await XiaoBangBangAPI.getLastCustomerByName('SYQ'), null, 4))
  }, 60000)

  it('should pass', async () => {
    const user_id =  uuid()
    const chat_id = getChatId(user_id)
    ChatStateStore.update(chat_id, {
      userSlots: {
        'budget': 100,
        'budget_is_per_year': false,
        'current_level_of_education': '本科',
        'gpa': '73',
        'goal': '出国读研',
        'city': '烟台',
        'school': '滑铁卢',
        'is_study_abroad': 'true',
        'is_parent': false,
        'is_japanese_student': false,
        'is_gaokao_student': false
      }
    })

    const tags = await XbbHelper.getTags(chat_id)
    const userName = '哈哈哈'

    try {
      await XbbHelper.labelUser(userName, tags, XbbFormId.Clue, new Date())
    } catch (e) {
      logger.error(e)
    }
  }, 1E8)

  it('查看线索表和客户表的描述', async () => {
    // console.log(JSON.stringify(await XiaoBangBangAPI.explainForm(XbbFormId.Clue), null, 4))

    console.log(JSON.stringify(await XiaoBangBangAPI.explainForm(XbbFormId.Customer), null, 4))
  }, 60000)
})