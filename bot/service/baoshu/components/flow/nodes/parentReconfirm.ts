import { BaoshuWorkFlowNode, trackInvoke } from './baseNode'
import { IWorkflowState } from '../flow'
import { BaoshuNode } from '../type'
import { ChatStateStore } from '../../../storage/chat_state_store'
import { LLMNode } from './llm'
import { IntentionCalculateNode, UserEducationGroup } from './intentionCalculate'
import { ChatHistoryService } from '../../chat_history'
import { UserInfoExtractPrompt } from '../../../prompt/baoshu/user_info_extract'
import { BooleanUserInfoExtractPrompt } from '../../../prompt/baoshu/study_abroad_and_is_parent'
import { UserSlotsExtract } from './helper/slotsExtract'
import { EarlyAgeInvitePendingNode } from './earlyAge'
import { StudyPlanRoutingNode } from './studyAbroadPlan'
import chalk from 'chalk'
import logger from '../../../../../model/logger/logger'
import { IsParentSupportedPrompt } from '../../../prompt/baoshu/is_parent_support'
import { EndNode } from './end'


export class ParentReconfirmNode extends BaoshuWorkFlowNode {
  @trackInvoke
  public static async invoke(state: IWorkflowState) {
    logger.log({ chat_id: state.chat_id }, '再次确认下用户家里是否支持')

    const chatState = ChatStateStore.get(state.chat_id)
    const userSlots = chatState.userSlots
    const nodeInvokeCount = chatState.nodeInvokeCount[ParentReconfirmNode.name]
    if (nodeInvokeCount <= 1) {
      if (userSlots) {
        const chatHistory = await ChatHistoryService.formatHistory(state.chat_id)
        userSlots.is_parent_supported = await UserSlotsExtract.isParentSupported(await IsParentSupportedPrompt.format(chatHistory))
        if (userSlots.is_parent_supported)  {
          ChatStateStore.getStatus(state.chat_id).is_update_is_parent_support = true
          return IntentionCalculateNode.invoke(state)
        }
      }

      return BaoshuNode.ParentSupportReconfirm
    } else {
      return EndNode.invoke(state)
    }
  }
}