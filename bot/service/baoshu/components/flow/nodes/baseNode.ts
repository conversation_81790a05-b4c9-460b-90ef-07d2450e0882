import { IWorkflowState } from '../flow'
import { ChatStateStore } from '../../../storage/chat_state_store'
import { BaoshuNode } from '../type'
import logger from '../../../../../model/logger/logger'


export abstract class BaoshuWorkFlowNode {
  public static async invoke(state: IWorkflowState): Promise<BaoshuNode> {
    return BaoshuNode.IntentionCalculate
  }
}


export function trackInvoke (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
  const originalMethod = descriptor.value

  descriptor.value = async function (...args: any []) {
    const state: IWorkflowState = args [0] // 假设 state 对象存在于第一个参数
    const chatId = state.chat_id // 假设 state 中包含 chatId
    const roundId = state.round_id // 假设 state 中包含 roundId

    const nodeInvokeCount: Record<string, number> = ChatStateStore.get(state.chat_id).nodeInvokeCount
    const node_name = target.name

    if (!nodeInvokeCount [node_name]) {
      nodeInvokeCount [node_name] = 0
    }

    logger.debug({ chat_id: chatId, round_id: roundId }, `进入 ${target.name}`, `调用次数: ${nodeInvokeCount [node_name]}`) // 输出进入节点的信息

    const start = Date.now()

    const res =  await originalMethod.apply (this, args)

    const end = Date.now()
    logger.debug({ chat_id: chatId, round_id: roundId }, `结束 ${target.name}`, `执行时长: ${((end - start) / 1000).toFixed(1)}s`) // 输出结束节点的信息

    nodeInvokeCount [node_name]++ // 消息有可能被打断，计数放到后面
    return res
  }

  return descriptor
}