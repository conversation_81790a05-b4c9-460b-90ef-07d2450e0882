import { IWorkflowState } from '../flow'
import { JuziAPI } from '../../../../../lib/juzi/api'
import { Config } from '../../../../../config/config'
import { WechatMessageSender } from '../../message_send'
import { ChatHistoryService } from '../../chat_history'
import { ChatStateStore, ConversationState } from '../../../storage/chat_state_store'
import { UserSlotsExtract } from './helper/slotsExtract'
import { sleep } from '../../../../../lib/schedule/schedule'
import logger from '../../../../../model/logger/logger'
import { XbbHelper } from './helper/xbbHelper'
import { ABTest } from '../../../../../model/a_btest/a_b_test'

export class PostSaleInviteNode {

  public static async invoke(state: IWorkflowState, transferCase: 'complain' | 'cooperation') {
    const externalUserId = await JuziAPI.wxIdToExternalUserId(state.user_id)
    if (!externalUserId) {
      throw new Error('获取 ExternalUserId 失败')
    }

    let counselors: string[] = []

    if (transferCase === 'complain') {
      counselors = ['ZhaiShuai']
    } else if (transferCase === 'cooperation') {
      counselors = ['ZhangJinXuan', 'afy-aimee']
    } else {
      throw new Error(`transferCase 未定义：${transferCase}`)
    }

    const index = ABTest.getStrategyForUser(state.chat_id, counselors.length)
    const counselor = counselors[index]

    // 直接拉群
    const { data } = await JuziAPI.createRoom({
      botUserId: Config.setting.wechatConfig?.botUserId as string,
      userIds: [counselor],
      name: '对接群',
      greeting: '您好，具体可以这里处理',
      externalUserIds: [externalUserId]
    })

    // 发送聊天记录 到群里，不影响主流程
    try {
      const userInfo = await JuziAPI.getCustomerInfo(Config.setting.wechatConfig?.id as string, state.user_id)
      let name = ''
      if (userInfo) {
        name = userInfo.name
      }

      await sleep(3000)
      // 发送格式
      const chat_history = `${(await ChatHistoryService.formatHistory(state.chat_id)).replace(/客户：/g, `${name}：`)}`
      const send_msg = `聊天记录：

${chat_history}
- 由企微微工具生成`

      await WechatMessageSender.sendById({
        room_id: data.roomWxid,
        chat_id: state.chat_id,
        ai_msg: send_msg
      }, false)
    } catch (e) {
      logger.error('发送聊天记录失败', e)
    }

    // 更新进群状态
    ChatStateStore.update(state.chat_id, {
      state: ConversationState.ConsultGroupEntered,
      status: {
        consultGroupEntered: true
      }
    })
  }
}