import { BaoshuWorkFlowNode, trackInvoke } from './baseNode'
import { IWorkflowState } from '../flow'
import { BaoshuNode } from '../type'
import { ChatStateStore, ConversationState } from '../../../storage/chat_state_store'
import { LLMNode } from './llm'
import logger from '../../../../../model/logger/logger'
import { ChatHistoryService } from '../../chat_history'
import { LLM } from '../../../../../lib/ai/llm/LLM'
import { XMLHelper } from '../../../../../lib/xml/xml'
import { RegexHelper } from '../../../../../lib/regex/regex'
import { DoctorCategorizePrompt } from '../../../prompt/baoshu/doctor_categorize'
import { ObjectUtil } from '../../../../../lib/object'
import { WechatMessageSender } from '../../message_send'
import { sleep } from 'openai/core'
import { IWecomMsgType } from '../../../../../lib/juzi/type'
import { HumanTransfer, HumanTransferType } from '../../human_transfer'
import { InviteToConsultantNode } from './inviteToGroup'

export enum DoctorConsultType {
  phd = 1,
  careerPlanning = 2,
  undecided = 3,
  unknown = 4
}


export class DoctorConsultNode extends BaoshuWorkFlowNode {
  @trackInvoke
  public static async invoke(state: IWorkflowState) {
    const nodeInvokeCount = ChatStateStore.get(state.chat_id).nodeInvokeCount[DoctorConsultNode.name]

    const userSlots = ChatStateStore.get(state.chat_id).userSlots

    // 如果预算 >= 10w, 走新路线，直接拉群。 如果预算 < 10w 走之前的路线
    if (userSlots && userSlots.budget  && userSlots.budget >= 10) {
      return await InviteToConsultantNode.invoke(state)
    } else {
      if (nodeInvokeCount === 0) {
        await LLMNode.invoke({
          state,
          dynamicPrompt: '因为用户正在咨询硕博的问题，不要直接解答客户的具体问题，含糊的宽泛的回答，并引导到后续由专业老师解答。',
        })
        return BaoshuNode.DoctorConsult
      } else {
        return await this.doctorConsult(state)
      }
    }
  }

  private static async doctorConsult(state: IWorkflowState) {
    // 对客户情况进行分类
    const category = await this.classifyCustomer(state)

    if (category === DoctorConsultType.phd) {
      await WechatMessageSender.sendById({
        user_id: state.user_id,
        chat_id: state.chat_id,
        ai_msg: '具体博士规划比较专业和复杂，真枪实弹在你的领域有学术经验的博士才能有效帮助你， 我们有一个付费的博士咨询。会安排【同专业方向】的藤校背景的老师给你具体的咨询和指导'
      })

      await sleep(3000)

      await WechatMessageSender.sendById({
        user_id: state.user_id,
        chat_id: state.chat_id,
        ai_msg: '如有需要，可以扫码购买（记得写上姓名）。 购买后告知我，给咱们拉群'
      })

      await sleep(3000)

      await WechatMessageSender.sendById({
        user_id: state.user_id,
        chat_id: state.chat_id,
        ai_msg: '[[硕博付费]]',
        send_msg:  {
          type: IWecomMsgType.Image,
          url: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/106021719028439_.pic.jpg'
        }
      })

      await HumanTransfer.transfer(state.chat_id, state.user_id, HumanTransferType.DoctorCounselor)
      return  BaoshuNode.END
    } else if (category === DoctorConsultType.careerPlanning) {
      await WechatMessageSender.sendById({
        user_id: state.user_id,
        chat_id: state.chat_id,
        ai_msg: '咱这问题还比较复杂，一两句说不清，我们有职业规划的付费咨询，可以帮助你确定自己的就业方向，也可以约同专业的在职大佬给你答疑解惑，需要的话，可以给咱们拉个群，跟我们顾问老师先了解了解'
      })

      ChatStateStore.update(state.chat_id, {
        state: ConversationState.ConsultGroupInvited,
        status: {
          consultGroupInvited: true
        }
      })
      return BaoshuNode.InviteConsultant
    } else {
      const isDoctorRoute = ChatStateStore.getStatus(state.chat_id).doctorRoute
      const askUserPrefer = ChatStateStore.getStatus(state.chat_id).askedUserPrefer

      if (!isDoctorRoute) {
        await LLMNode.invoke({
          state,
          dynamicPrompt: '客户正在咨询博士或者工作相关的问题，对客户进行共情并参考下列思路进行回答: 1. 咱们对研究方向感兴趣，可以继续读博士，我们有博士的付费咨询，可以安排同专业方向的藤校背景的老师给咱们做咨询和指导 2. 如果咱们是对于这个行业，对是否需要读到博士不太清楚，可以做个职业方向的付费咨询。让行业大佬给你分析，答疑解惑 3. 需要的话，可以给咱们拉个群，跟我们顾问老师先了解了解',
          temperature: 1
        })

        ChatStateStore.update(state.chat_id, {
          status: {
            doctorRoute: true
          }
        })
      } else if (!askUserPrefer) {
        await LLMNode.invoke({
          state,
          dynamicPrompt: '询问下客户现在自己什么想法， 例如："咱们现在是倾向于就业还是读博?"',
          temperature: 1,
          checkRepeat: true
        })
        ChatStateStore.update(state.chat_id, {
          status: {
            askedUserPrefer: true
          }
        })
      } else {
        await LLMNode.invoke({
          state,
          dynamicPrompt: '回答客户问题，引导客户做决定，为后续本机构提供的付费职业规划或者博士规划做铺垫',
          temperature: 1,
          checkRepeat: true
        })
      }

      return BaoshuNode.DoctorConsult
    }
  }

  private static async classifyCustomer(state: IWorkflowState) {
    const chatHistory = await ChatHistoryService.formatHistory(state.chat_id)
    const llm = new LLM({ meta: { promptName: 'DoctorCategorize', description: '博士分类', chat_id:state.chat_id } })
    const llmRes = await llm.predict(await DoctorCategorizePrompt.format(chatHistory))

    logger.debug({ chat_id: state.chat_id }, '博士分类结果: ', llmRes)
    const intention =  XMLHelper.extractContent(llmRes, 'category')

    if (!intention) {
      return DoctorConsultType.unknown
    }

    const intentionType =  RegexHelper.extractNumber(intention) as DoctorConsultType

    if (!ObjectUtil.enumValues(DoctorConsultType).includes(intentionType)) {
      return DoctorConsultType.unknown
    }
    logger.debug({ chat_id: state.chat_id }, '博士分类结果: ', intentionType)

    return intentionType
  }
}