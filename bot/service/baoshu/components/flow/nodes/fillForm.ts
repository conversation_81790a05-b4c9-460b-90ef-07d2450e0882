import { trackInvoke } from './baseNode'
import { IWorkflowState } from '../flow'
import { ChatStateStore, ConversationState } from '../../../storage/chat_state_store'
import { BaoshuNode } from '../type'
import { WechatMessageSender } from '../../message_send'
import { IntentionCalculateNode } from './intentionCalculate'
import { sleep } from '../../../../../lib/schedule/schedule'
import { LLM } from '../../../../../lib/ai/llm/LLM'
import { isFillFormPrompt } from '../../../prompt/is_fill_form.prompt'
import { XMLHelper } from '../../../../../lib/xml/xml'
import { LLMNode } from './llm'
import { ChatHistoryService } from '../../chat_history'
import { IWecomMsgType } from '../../../../../lib/juzi/type'
import logger from '../../../../../model/logger/logger'

export class FillFormNode {
  @trackInvoke
  public static async invoke(state: IWorkflowState) {
    if (ChatStateStore.get(state.chat_id).state === ConversationState.FormFilled) { // 表已经填了，进入到 IntentionCalculate 节点
      return IntentionCalculateNode.invoke(state)
    }

    const nodeInvokeCount = ChatStateStore.get(state.chat_id).nodeInvokeCount[FillFormNode.name]

    if (nodeInvokeCount === 0) {
      await WechatMessageSender.sendById({
        chat_id: state.chat_id,
        user_id: state.user_id,
        ai_msg: '因为这2天高考出分，实在是太忙了。提高下效率，把你的信息帮我规整下，我来高效看下'
      })

      await sleep(1000)

      await WechatMessageSender.sendById({
        chat_id: state.chat_id,
        user_id: state.user_id,
        ai_msg: `宝贝，先补充下这个表，方便看看你的情况：
姓名：
电话：
年级：
高考总分：
英语成绩：
（或）日语成绩：
所在城市：
想【国际本科/中外合办】还是【海外留学】：
本科总预算（学费+生活费）：
意向专业（文商科/理工科/艺术类）：`
      })
      await WechatMessageSender.sendById({
        chat_id: state.chat_id,
        user_id: state.user_id,
        ai_msg: '😊',
        send_msg: {
          type: IWecomMsgType.Emoticon,
          imageUrl: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/a36d7270254a788cb8753434fbff233e.gif'
        },
      })

      return BaoshuNode.FillFormNode
    }

    // 判断表是否填完
    if (await this.isFormFilled(state.chat_id, state.userMessage)) {
      ChatStateStore.update(state.chat_id, {
        state: ConversationState.FormFilled
      })
      return IntentionCalculateNode.invoke(state)
    }

    await LLMNode.invoke({
      state,
      dynamicPrompt: `Your task is to guide the user through filling out the form fields and provide relevant information when needed.

    Follow these rules when helping the user fill out the form:
    1. For language test scores, if the user studied English in high school, ask for English test scores. If they studied Japanese, ask for Japanese test scores.
    2. If the user doesn't have a clear idea about their budget, provide information about typical study abroad budgets. Use this template:
    "一般主流国家，美国每年60-80w, 英国要准备每年40-50w，新加坡每年25-35w，国际本科平均每年20w左右，总共40-80w，性价比高的韩国/马来西亚，每年10-15w差不多就可以的。 看看大概能卡在哪个档呢，出去预算还是一个很硬的门槛的。"
    
    3. If the user doesn't respond to a specific field or asks an unrelated question, gently guide them back to filling out the form. You can say: "高考季叔实在非常忙，规整下表格信息我，比较高效。"
    
    4. Be polite and patient throughout the conversation.`
    })

    if (nodeInvokeCount > 4) {
      return BaoshuNode.END
    }


    return BaoshuNode.FillFormNode
  }

  private static async isFormFilled(chat_id: string, userMessage: string) {
    const llm = new LLM()
    const llmRes = await llm.predict(await isFillFormPrompt.format(userMessage))

    logger.log({ chat_id }, `表填写结果：${llmRes}`)

    const xmlRes = XMLHelper.extractContent(llmRes, 'answer')
    if (xmlRes && xmlRes.includes('YES')) {
      return true
    }

    return false
  }
}