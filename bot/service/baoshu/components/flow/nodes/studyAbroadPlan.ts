import { BaoshuWorkFlowNode, trackInvoke } from './baseNode'
import { IWorkflowState } from '../flow'
import { SearchSystem } from '../../search/search_system'
import { ChatStateStore, ConversationState } from '../../../storage/chat_state_store'
import { WechatMessageSender } from '../../message_send'
import { LLMNode } from './llm'

import { LLM } from '../../../../../lib/ai/llm/LLM'
import { BaoshuNode } from '../type'
import { InviteToConsultantNode } from './inviteToGroup'

import { IntentionCalculateNode, UserEducationGroup } from './intentionCalculate'
import { ProjectsRerankPrompt } from '../../../prompt/baoshu/projects_rerank'
import { RegexHelper } from '../../../../../lib/regex/regex'
import { JSONHelper } from '../../../../../lib/json/json'
import { ChatHistoryService } from '../../chat_history'
import { RecommendProjectsPrompt } from '../../../prompt/baoshu/recommend_projects'
import { XMLHelper } from '../../../../../lib/xml/xml'
import { IsSuitableJoinGroupPrompt } from '../../../prompt/baoshu/is_suitable_join'
import { SilentReAsk } from '../../silent_requestion'
import { getProjectBudgets } from './helper/getProjectBudgets'
import logger from '../../../../../model/logger/logger'
import { IProject } from '../../../../elastic_search/baoshu_projects'


/**
 * 答疑到进群的中间环节
 */
export class StudyAbroadPlanningConsultNode extends BaoshuWorkFlowNode {
  @trackInvoke
  public static async invoke(state: IWorkflowState) {
    // 围绕项目，给出咨询建议
    const chatState = ChatStateStore.get(state.chat_id)
    const status = ChatStateStore.getStatus(state.chat_id)
    const userDescription = ChatStateStore.getDescription(state.chat_id)
    if (!chatState.projects || !chatState.projects.length) {
      chatState.projects = []
    }

    if (status.consultGroupInvited) {
      return await InviteToConsultantNode.invoke(state)
    }

    const roundCount = chatState.nodeInvokeCount[StudyAbroadPlanningConsultNode.name]

    // 判断是否应该进群
    const chatHistory = await ChatHistoryService.getRecentConversations(state.chat_id, 3)
    if (roundCount > 1 && await this.shouldInviteToGroup(ChatHistoryService.formatHistoryHelper(chatHistory))) { // 首先要推一轮路径
      return await InviteToConsultantNode.invoke(state)
    }

    if (roundCount >= 1) {
      // 追问拉群
      await SilentReAsk.schedule(state.chat_id, async () => {
        const chatHistory = await ChatHistoryService.formatHistory(state.chat_id)
        if (chatHistory.includes('这样吧，我给你拉个定向规划老师给你具体规划规划，咱们尽早准备，叫上你家长一起，沟通比较高效') || chatHistory.includes('咱们真考虑出去的话，我给你安排个专业方向很强的老师给你具体规划下')) {
          return
        }

        // TODO 如果用户没有表示出否定，如再考虑一下之类，或暂时不准备出去

        // 拉群任务
        if (chatState.userSlots && chatState.userSlots.education_group && chatState.userSlots.education_group <= UserEducationGroup.HighSchool && chatState.userSlots.is_parent === false) {
          await WechatMessageSender.sendById({
            user_id: state.user_id,
            chat_id: state.chat_id,
            ai_msg: '这样吧，我给你拉个定向规划老师给你具体规划规划，咱们尽早准备，叫上你家长一起，沟通比较高效'
          })
        } else {
          await WechatMessageSender.sendById({
            user_id: state.user_id,
            chat_id: state.chat_id,
            ai_msg: '咱们真考虑出去的话，我给你安排个专业方向很强的老师给你具体规划下'
          })
        }

        ChatStateStore.update(state.chat_id, {
          state: ConversationState.ConsultGroupInvited,
          status: {
            consultGroupInvited: true
          }
        })
      }, 3 * 60, true)
    }

    await LLMNode.invoke({
      state,
      referenceChatHistory: true,
      customPrompt: await RecommendProjectsPrompt.format(chatState.projects.map((project, index) => {
        return `${index + 1}. ${project.projectName}\n${project.projectDescription}\n${project.projectAdvantages}\n适合的用户画像:${project.userStory}\n预算范围：${project.budgetLowerBound === project.budgetUpperBound ? `${project.budgetLowerBound}+` : `${project.budgetLowerBound}-${project.budgetUpperBound}`}`
      }).join('\n\n'), userDescription),
      regenerate: true
    })

    return BaoshuNode.StudyAbroadPlanningConsult
  }

  private static async shouldInviteToGroup(userMessage: string) {
    const llm = new LLM()
    const llmRes = await llm.predict(await IsSuitableJoinGroupPrompt.format(userMessage))
    const xmlRes = XMLHelper.extractContent(llmRes, 'answer')
    if (!xmlRes) {
      return false
    }

    const jsonRes = JSONHelper.parse(xmlRes)

    if (jsonRes === true) {
      return true
    } else if (jsonRes === false) {
      return false
    } else {
      return false
    }
  }
}

/**
 * 留学规划开始节点，需要补充信息，或者有具体项目了，则进入项目咨询节点
 */
export class StudyPlanRoutingNode extends BaoshuWorkFlowNode {
  @trackInvoke
  public static async invoke(state: IWorkflowState) {
    const userState = ChatStateStore.get(state.chat_id)
    // 如果用户有意向国家，或者意向学校
    if (userState.userSlots && userState.userSlots.user_intended_country && userState.userSlots.user_intended_country.length > 0 && userState.userSlots.user_intended_country.length < 3) {
      return SelfPlanNode.invoke(state)
    }

    if (userState.userSlots && userState.userSlots.user_intended_school && userState.userSlots.user_intended_school.length > 0 && userState.userSlots.user_intended_school.length < 3) {
      return SelfPlanNode.invoke(state)
    }

    // if (userState.userSlots && userState.userSlots.user_intended_project && userState.userSlots.user_intended_project.length > 0 && userState.userSlots.user_intended_project.length < 3) {
    //   return SelfPlanNode.invoke(state)
    // }

    // 没有
    return InternalPlanNode.invoke(state)
  }
}

export class SelfPlanNode extends BaoshuWorkFlowNode {
  @trackInvoke
  public static async invoke(state: IWorkflowState) {
    const userSlots = ChatStateStore.get(state.chat_id).userSlots
    if (!userSlots) {
      throw new Error('userSlots 为空')
    }
    const { isBudgetEnough, projectBudgets } = await getProjectBudgets(state.chat_id)

    logger.log({ chat_id: state.chat_id }, '预算筛选结果：isBudgetEnough: ', isBudgetEnough, 'projectBudgets: ', projectBudgets)

    if (isBudgetEnough) {
      return await SelfPlanConsultNode.invoke(state)
    } else if (projectBudgets.length === 0) { // 库里一个项目都匹配不上
      logger.warn('预算表缺少项目：', userSlots.user_intended_country, userSlots.user_intended_school)
      return await SelfPlanConsultNode.invoke(state)
    } else { // 预算不够，询问是否需要提高
      const requiredBudgets  =  projectBudgets.map((project) => {
        if (!project) return ''
        return `${project.projectName} 预算范围：${project.budgetLowerBound}-${project.budgetUpperBound}万`
      }).join('\n')

      await LLMNode.invoke({
        state,
        dynamicPrompt: `用户的预算对于想要去的国家或学校不够，礼貌的询问，预算可以提高吗。并告知合理预算范围多少。
用户的预算是 ${userSlots.budget}。
想要去的项目的预算为: ${requiredBudgets}
例如：你这个预算可能不太够啊，去 xx 大概需要 xx, 你预算能提高么`,
        referenceChatHistory: true,
      })

      return InternalPlanNode.invoke(state)
    }
  }

}


export class SelfPlanConsultNode extends BaoshuWorkFlowNode {

  @trackInvoke
  public static async invoke(state: IWorkflowState) {
    const status =  ChatStateStore.get(state.chat_id).status
    if (status && status.consultGroupInvited) {
      return await InviteToConsultantNode.invoke(state)
    }

    const nodeInvokeCount = ChatStateStore.getNodeEnterCount(state.chat_id, SelfPlanConsultNode.name)

    if (nodeInvokeCount <= 1) {
      await LLMNode.invoke({
        state,
        dynamicPrompt: `1. 依据用户提到的意向国家、学校或项目提供建议，不要推荐用户未提及的项目。
2. 评估用户条件，例如学历背景（大专生或高考成绩不佳的学生），并据此提供适合的建议。
3. 确保不会推荐用户能力范围以外的顶尖学校。对于成绩不佳或背景不足的学生，请避免推荐顶尖学校，如世界知名的研究型大学等。

For example:
Example 1:
customer demand：离家近一些，毕业比较快，对新加坡感兴趣
暴叔：新加坡可以考虑私立院校，像新加坡管理学院（SIM），对接英澳名校，毕业后拿名校学历。

Example 2：
customer demand:在国内卷不过别人，所以希望去个性价比高，教育水平还不错国家留学，意向国家马来西亚
暴叔：马来西亚，性价比高，可以
5所公立大学QS都在前200，后续申请英美院校也的很方便，这几年去的同学也很多，你这边英语四级的话，还是要努力下，雅思考个6以上要的`
      })

      if (nodeInvokeCount >= 0) {
        // 追问拉群
        await SilentReAsk.schedule(state.chat_id, async () => {
          const chatHistory = await ChatHistoryService.formatHistory(state.chat_id)
          if (chatHistory.includes('这样吧，我给你拉个定向规划老师给你具体规划规划，咱们尽早准备，叫上你家长一起，沟通比较高效') || chatHistory.includes('咱们真考虑出去的话，我给你安排个专业方向很强的老师给你具体规划下')) {
            return
          }

          // TODO 如果用户没有表示出否定，如再考虑一下之类，或暂时不准备出去
          const chatState = ChatStateStore.get(state.chat_id)
          // 拉群任务
          if (chatState.userSlots && chatState.userSlots.education_group && chatState.userSlots.education_group <= UserEducationGroup.HighSchool && chatState.userSlots.is_parent === false) {
            await WechatMessageSender.sendById({
              user_id: state.user_id,
              chat_id: state.chat_id,
              ai_msg: '这样吧，我给你拉个定向规划老师给你具体规划规划，咱们尽早准备，叫上你家长一起，沟通比较高效'
            })
          } else {
            await WechatMessageSender.sendById({
              user_id: state.user_id,
              chat_id: state.chat_id,
              ai_msg: '咱们真考虑出去的话，我给你安排个专业方向很强的老师给你具体规划下'
            })
          }

          ChatStateStore.update(state.chat_id, {
            state: ConversationState.ConsultGroupInvited,
            status: {
              consultGroupInvited: true
            }
          })
        }, 3 * 60, true)
      }

      return BaoshuNode.SelfPlanConsult
    }

    return await InviteToConsultantNode.invoke(state)
  }
}


export class InternalPlanNode extends BaoshuWorkFlowNode {
  @trackInvoke
  public static async invoke(state: IWorkflowState) {
    // 问答，检索知识库，回答用户问题。匹配出产品，给出建议。然后提问用户是否需要进群。
    // 用户的槽位 和 项目槽位，成功获取之后，存一下，后续围绕进行回答。
    const chatState = ChatStateStore.get(state.chat_id)
    if (chatState.projects && chatState.projects.length > 0) {
      return await StudyAbroadPlanningConsultNode.invoke(state)
    }

    const queryProjects = await SearchSystem.searchProject(chatState.userSlots)
    logger.debug({ chat_id: state.chat_id }, '项目筛选：', JSON.stringify(queryProjects, null, 4))

    if (queryProjects.lowBudget) {
      logger.debug({ chat_id: state.chat_id }, '用户预算不够，礼貌结束', chatState.userSlots?.budget)

      await LLMNode.invoke({
        state,
        dynamicPrompt: '用户当前预算太低，可以用类似的语句委婉回复：“咱这个预算可能不太够，可以先存存钱，之后再来找叔”。'
      })
      return BaoshuNode.END
    }

    if (queryProjects.humanInvolve) {
      logger.debug({ chat_id: state.chat_id }, '预算够，匹配不到项目', chatState.userSlots?.budget)
      ChatStateStore.update(state.chat_id, { // 这里是拉群话术了
        state: ConversationState.ConsultGroupInvited,
        status: {
          consultGroupInvited: true
        }
      })

      await WechatMessageSender.sendById({
        user_id: state.user_id,
        chat_id: state.chat_id,
        ai_msg: '这样吧，咱这个情况需要具体分析，我找个这个方面的专业老师跟你对接下'
      })

      return BaoshuNode.InviteConsultant
    }

    if (queryProjects.projects && queryProjects.projects.length > 0) {
      // 项目进行一下重排序
      if (queryProjects.projects.length > 1) {
        queryProjects.projects = await this.reRankProjects(queryProjects.projects, chatState.userSlots as Record<string, any>, await ChatHistoryService.formatHistory(state.chat_id))
      }

      // 存一下状态
      ChatStateStore.update(state.chat_id, {
        projects: queryProjects.projects,
        userSlots: queryProjects.userSlots
      })

      return StudyAbroadPlanningConsultNode.invoke(state)
    }

    return BaoshuNode.InternalPlan
  }

  private static async reRankProjects(projects: IProject[], userSlots: Record<string, any>, chatHistory: string) {
    if (!userSlots.education_group) {
      return projects
    }

    // 不同的 education_group 有不同的项目排序规则
    let order: string[] = []
    switch (userSlots.education_group) {
      case UserEducationGroup.BelowJuniorOne: // 初中以下
        return projects
      case UserEducationGroup.JuniorTwoToFour: // 初中
        order = ['国内读国际高中', '国内本科预科+新加坡本科课程', '去读新西兰高中', '去读加拿大高中', '去读美国高中', '快速本科路径-新加坡私立大学', '快速本科路径-新西兰初升本']
        break
      case UserEducationGroup.HighSchool: // 高中
        order = ['国际本科3+1', '国际本科2+2', '香港方向', '澳门方向', '新加坡方向', '韩国方向', '马来西亚方向', '英国方向', '澳洲方向', '中外合办4+0/3+0']
        break
      case UserEducationGroup.College: // 大专
        order = ['韩国3+1中文授课专升本', '新加坡方向的专科升学', '英国TOP-UP专升本', '澳洲专升本',  '香港专升本',  '中外合办2+0专升硕']
        break
      case UserEducationGroup.University: // 本科
        order = ['英国方向', '澳洲方向', '香港方向', '新加坡方向', '美国方向', '国内念中外合作办学硕士']
        break
      case UserEducationGroup.Master:
      case UserEducationGroup.Doctor:
        return projects
    }

    // 根据优先级 和 用户画像 选出 4个项目
    // 将 Projects 按照 order 中顺序
    function sortProjectsByOrder(projects: IProject[], order: string[]): IProject[] {
      // 创建一个 Map 来存储 order 中的项目名及其顺序
      const orderMap = new Map(order.map((name, index) => [name, index]))

      // 对 projects 进行排序
      return projects.sort((a, b) => {
        const orderA = orderMap.get(a.projectName)
        const orderB = orderMap.get(b.projectName)

        // 如果 order 中没有该项目，则放到后面
        if (orderA === undefined) return 1
        if (orderB === undefined) return -1

        // 按照 order 中的顺序排序
        return orderA - orderB
      })
    }

    let sortedProjects = sortProjectsByOrder(projects, order)

    if (userSlots.education_group !== UserEducationGroup.HighSchool && sortedProjects.length > 4) {
      // LLM pick 4 projects
      sortedProjects = await this.pickTop4Projects(sortedProjects, chatHistory)
      logger.debug('AI 筛选结果：', JSON.stringify(sortedProjects, null, 4))

      return sortedProjects
    }
    return sortedProjects.slice(0, 4)
  }

  private static async pickTop4Projects(sortedProjects: IProject[], chatHistory: string) {
    const llm  = new LLM()
    // format projects
    const project_list = sortedProjects.map((project, index) => {
      return `${index + 1}. ${project.projectName}\n${project.projectDescription}\n${project.projectAdvantages}\n适合的用户画像:${project.userStory}\n预算范围：${project.budgetLowerBound}-${project.budgetUpperBound}`
    }).join('\n\n')

    const prompt = await ProjectsRerankPrompt.format(chatHistory, project_list)

    const llmRes = await llm.predict(prompt)

    const resultList = RegexHelper.extractList(llmRes)

    if (!resultList) {
      return sortedProjects.slice(0, 4)
    }

    const orderList = JSONHelper.parse(resultList)
    if (orderList.some((index: number) => index < 1 || index > project_list.length)) {
      return sortedProjects.slice(0, 4)
    }

    return orderList.map((index: number) => sortedProjects[index - 1])
  }
}
