import { RedisDB } from '../../../../../../model/redis/redis'

export class DailyCounter {
  private static redisClient = RedisDB.getInstance()

  /**
   * 获取指定 key 的统计次数。
   * @param key Redis 中存储计数的键
   * @returns 该 key 对应的统计次数，如果不存在则返回 0
   */
  public static async getCount(key: string): Promise<number> {
    const count = await this.redisClient.get(key)
    return count ? parseInt(count, 10) : 0
  }

  /**
   * 增加指定 key 的统计次数。如果是第一次设置，将设置过期时间为当天剩余秒数，确保每天 0 点重置。
   * @param key Redis 中存储计数的键
   */
  public static async incrementCount(key: string): Promise<void> {
    // 使用 Redis 的 INCR 来增加计数
    const newCount = await this.redisClient.incr(key)

    // 如果是第一次设置，设置键的过期时间为当天的剩余时间，确保每天 0 点重置
    if (newCount === 1) {
      const secondsUntilMidnight = this.getSecondsUntilMidnight()
      console.log(secondsUntilMidnight)
      await this.redisClient.expire(key, secondsUntilMidnight)
    }
  }

  /**
   * 获取当前时间到今天 0 点的剩余秒数
   * @returns 到当天 0 点的秒数
   */
  private static getSecondsUntilMidnight(): number {
    const now = new Date()
    const midnight = new Date(now)
    midnight.setHours(24, 0, 0, 0) // 设置到今天的 0 点
    return Math.floor((midnight.getTime() - now.getTime()) / 1000)
  }
}