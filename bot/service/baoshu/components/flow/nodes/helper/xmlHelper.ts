import { LLM } from '../../../../../../lib/ai/llm/LLM'
import { XMLHelper } from '../../../../../../lib/xml/xml'


export class LLMXMLHelper {
  public static async extractBooleanAnswer(prompt: string, options: { tagName?: string;  trueFlag: string; falseFlag: string; model?: string}, logInfo?: Record<string, any>): Promise<boolean | null> {
    const llm = new LLM({ model: options.model, meta: logInfo })
    const llmRes = await llm.predict(prompt)

    const xmlRes = XMLHelper.extractContent(llmRes, options?.tagName || 'answer')

    if (!xmlRes) {
      return null
    }

    if (xmlRes.toLowerCase() === options.trueFlag.toLowerCase()) {
      return true
    } else if (xmlRes.toLowerCase() === options.falseFlag.toLowerCase()) {
      return false
    } else {
      return null
    }
  }
}