import { IWorkflowState } from '../../flow'
import { LLM } from '../../../../../../lib/ai/llm/LLM'
import { isUserAgreedToJoinConsultGroupPrompt } from '../../../../prompt/baoshu/confirm'
import { XMLHelper } from '../../../../../../lib/xml/xml'
import chalk from 'chalk'
import { JuziAPI } from '../../../../../../lib/juzi/api'
import { Config } from '../../../../../../config/config'
import { WechatMessageSender } from '../../../message_send'
import { ChatStateStore, ConversationState } from '../../../../storage/chat_state_store'
import { BaoshuNode } from '../../type'
import { LLMNode } from '../llm'
import { <PERSON>Trans<PERSON>, HumanTransferType } from '../../../human_transfer'
import { IWecomMsgType } from '../../../../../../lib/juzi/type'
import { IntentionCalculateNode, UserEducationGroup } from '../intentionCalculate'
import { ChatHistoryService } from '../../../chat_history'
import { trackInvoke } from '../baseNode'
import { JuziHelper } from './juziHelper'
import logger from '../../../../../../model/logger/logger'
import { sleep } from '../../../../../../lib/schedule/schedule'
import { cityToProvince, XbbHelper } from './xbbHelper'
import { ABTest } from '../../../../../../model/a_btest/a_b_test'
import { DailyCounter } from './countByDate'
import { RegexHelper } from '../../../../../../lib/regex/regex'


export class InviteToGroupHelper {

  @trackInvoke
  public static async handleEnterGroup(state: IWorkflowState) {
    const status = ChatStateStore.getStatus(state.chat_id)
    if (status.operationGroupInvited) {
      const chatHistory = await ChatHistoryService.formatHistory(state.chat_id)
      if (!chatHistory.includes('进群二维码')) {
        await this.sendGroupQRCode(state)
      }

      return BaoshuNode.END
    }

    const chatState = ChatStateStore.get(state.chat_id)
    const userSlots = chatState.userSlots
    // 符合条件的大一进运营群
    if (userSlots && userSlots.grade === '大一') {
      await IntentionCalculateNode.inviteOperationGroup(state)
      return BaoshuNode.END
    }

    // 根据过去的对话，判断用户是否同意进群
    const nodeRound = ChatStateStore.getNodeEnterCount(state.chat_id, InviteToGroupHelper.name)

    const isAgree  = await InviteToGroupHelper.isUserAgreeToEnterGroup(state) // 'true' | 'false' | 'not yet'
    logger.debug({ chat_id: state.chat_id }, chalk.redBright(`用户是否同意进群：${ isAgree }`))

    if (isAgree === 'true' || (nodeRound >= 1 && isAgree !== 'false')) { // 多问一轮后，用户仍然没有明确拒绝，直接强行拉群
      await this.inviteToCounselor(state)
      return BaoshuNode.END
    } else if (isAgree === 'not yet') {
      // 如果已经邀请过，不进行回复
      const chatHistory = await ChatHistoryService.formatHistory(state.chat_id)
      if (chatHistory.includes('咱们真考虑出去的话，我给你安排个专业方向很强的老师给你具体规划下')) {
        if (!chatHistory.includes('咱们的情况需要进一步的询问和讨论。我们一句两句也说不太清楚，我还是建议让专业的顾问老师跟你沟通下，聊聊是免费的，了解清楚了才能给你最佳的方案。稍等。我拉群给你。')) {
          await WechatMessageSender.sendById({
            user_id: state.user_id,
            chat_id: state.chat_id,
            ai_msg: '咱们的情况需要进一步的询问和讨论。我们一句两句也说不太清楚，我还是建议让专业的顾问老师跟你沟通下，聊聊是免费的，了解清楚了才能给你最佳的方案。稍等。我拉群给你。'
          })
        }

        await this.inviteToCounselor(state)
        return BaoshuNode.END
      } else {
        // 用户未同意进群，可以再询问下用
        // 户还有什么问题，然后再次邀请
        await LLMNode.invoke({
          state,
          dynamicPrompt: '你当前已经邀请过用户进入顾问群，用户仍处于没有直接同意或犹豫的状态，你可以先回答用户问题， 然后以你很忙，时间不够的方式，让顾问老师帮助规划的方式再次引导用户进群。或者参考这个话术："咱们的情况需要进一步的询问和讨论。我们一句两句也说不太清楚，我还是建议让专业的顾问老师跟你沟通下，聊聊是免费的，了解清楚了才能给你最佳的方案。稍等。我拉群给你。"',
          referenceChatHistory: true,
        })
      }

      return BaoshuNode.InviteConsultant
    } else {
      const chatState = ChatStateStore.get(state.chat_id)
      const isParent = chatState?.userSlots?.is_parent
      const budget = chatState?.userSlots?.budget || 0
      if (budget > 10 || isParent) {
        logger.debug('发送运营群二维码')
        let operationGroupInvite = '你需要参考顾问和用户的聊天记录，邀请顾问进入运营群。参考话术："可以的，同学，我理解。我邀请你进入我们的(本科，硕士）升学群，群里都是跟你一样年纪的同学，群里不定期会有各种关于升学，实习，各个国家的学校的信息，你可以在群里先学习和关注起来，随时在群里问问题，也有老师回复和解答。你也可以随时留言给我，我们都在的，稳了！"'

        if (isParent)  {
          operationGroupInvite = '你需要参考顾问和用户的聊天记录，邀请顾问进入运营群。参考话术："可以的，家长，我理解。我邀请你进入我们的(本科，硕士）升学群，群里不定期会有各种关于升学，实习，各个国家的学校的信息，你可以在群里先学习和关注起来，随时在群里问问题，也有老师回复和解答。你也可以随时留言给我，我们都在的，稳了！"'
        }

        ChatStateStore.update(state.chat_id, {
          state: ConversationState.OperationGroupInvited,
          status: {
            operationGroupInvited: true,
          }
        })

        // 拉运营群
        await LLMNode.invoke({
          state,
          referenceChatHistory: true,
          dynamicPrompt: operationGroupInvite,
          noInterrupt: true
        })

        await this.sendGroupQRCode(state)

        ChatStateStore.update(state.chat_id, {
          state: ConversationState.OperationGroupInvited,
          status: {
            operationGroupInvited: true,
            operationGroupEntered: true,
          }
        })

        return BaoshuNode.END
      } else {
        // 用户不同意进群，结束对话
        await LLMNode.invoke({
          state,
          dynamicPrompt: '当前用户不同意进群，礼貌的结束对话'
        })

        return BaoshuNode.END
      }
    }
  }

  public static async sendGroupQRCode(state: IWorkflowState) {
    const chatState = ChatStateStore.get(state.chat_id)
    const userSlots = chatState.userSlots

    let qrCode = 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/baoshu/qrcode/university.jpg'
    if (userSlots && (userSlots.education_group === UserEducationGroup.HighSchool || userSlots.education_group === UserEducationGroup.JuniorTwoToFour || userSlots.education_group === UserEducationGroup.BelowJuniorOne)) {
      if (userSlots.is_parent) {
        qrCode = 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/baoshu/qrcode/university_parent.jpg'
      } else if (userSlots.has_offer) {
        qrCode = 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/baoshu/qrcode/master.jpg'
      } else if (userSlots.has_agent) {
        qrCode = 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/baoshu/qrcode/master.jpg'
      } else {
        qrCode = 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/baoshu/qrcode/university.jpg'
      }
    } else if (userSlots && (userSlots.education_group === UserEducationGroup.College || userSlots.education_group === UserEducationGroup.University || userSlots.application_stage === '硕士')) {
      qrCode = 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/baoshu/qrcode/master.jpg'
    }

    await WechatMessageSender.sendById({
      user_id: state.user_id,
      chat_id: state.chat_id,
      ai_msg: '[[进群二维码]]',
      send_msg: {
        type: IWecomMsgType.Image,
        url: qrCode
      }
    })
  }


  public static async isUserAgreeToEnterGroup(state: IWorkflowState) {
    const llm = new LLM()
    // 这里用最后一轮数据作为参考
    const lastRound = await ChatHistoryService.getRecentConversations(state.chat_id, 2)
    const messages = ChatHistoryService.formatHistoryHelper(lastRound)
    const llmRes = await llm.predict(await isUserAgreedToJoinConsultGroupPrompt.format(messages))

    // 提取出 answer 的标签中，true or false
    const xmlAnswer = XMLHelper.extractContent(llmRes, 'answer')

    if (!xmlAnswer) {
      logger.debug({ chat_id: state.chat_id }, 'isUserAgreedToJoinConsultGroupPrompt 提取 answer 失败', llmRes)
      return 'not yet'
    }

    if(['true', 'false', 'not yet'].includes(xmlAnswer)) {
      return xmlAnswer
    } else {
      logger.error('isUserAgreeToEnterConsultGroup Validation failed!', xmlAnswer)
      return 'not yet'
    }
  }

  public static async inviteToCounselor(state: IWorkflowState, earlyAge = false) {
    const status  = ChatStateStore.getStatus(state.chat_id)

    // 防止重复拉群
    if (status.consultGroupEntered || status.operationGroupEntered) {
      return BaoshuNode.END
    }

    // 更新进群状态
    ChatStateStore.update(state.chat_id, {
      state: ConversationState.ConsultGroupEntered,
      status: {
        consultGroupEntered: true
      }
    })

    try {
      const externalUserId = await JuziAPI.wxIdToExternalUserId(state.user_id)
      if (!externalUserId) {
        throw new Error('获取 ExternalUserId 失败')
      }

      const { counselor } = await this.getInvitedCounselor(state)
      const groupName = await this.getGroupName(state.user_id)

      const { data } = await JuziAPI.createRoom({
        botUserId: Config.setting.wechatConfig?.botUserId as string,
        userIds: [counselor],
        name: groupName,
        greeting: '咱们在这里',
        externalUserIds: [externalUserId]
      })

      // 发送聊天记录 到群里，不影响主流程
      try {
        const userInfo = await JuziAPI.getCustomerInfo(Config.setting.wechatConfig?.id as string, state.user_id)
        let name = '客户：'
        if (userInfo) {
          name = userInfo.name
        }

        // 发送格式
        const chat_history = `${(await ChatHistoryService.formatHistory(state.chat_id)).replace(/客户：/g, `${name}：`)}`
        const send_msg = `聊天记录：

${chat_history}
- 由企微微工具生成`

        await sleep(5000)

        await WechatMessageSender.sendById({
          room_id: data.roomWxid,
          chat_id: state.chat_id,
          ai_msg: send_msg
        }, false)
      } catch (e) {
        logger.error('发送聊天记录失败', e)
      }

      await sleep(5000)

      if (earlyAge) {
        await WechatMessageSender.sendById({
          room_id: data.roomWxid,
          chat_id: state.chat_id,
          ai_msg: '家长， 我已经安排了我们低龄规划的专家给你。 老师会跟孩子做一个专业的家庭测评，再看怎么一对一定制规划。放心，都稳啦！ '
        }, false)
      } else {
        await WechatMessageSender.sendById({
          room_id: data.roomWxid,
          chat_id: state.chat_id,
          ai_msg: '有什么问题都可以咨询暴叔团队的专业老师。可以的话麻烦留下 姓名+电话，老师可以直接给咱们打电话，沟通更高效。'
        }, false)

        await sleep(5000)

        function isCurrentTimeBetweenMidnightAnd5AM (): boolean {
          const now = new Date ()
          const hours = now.getHours ()
          return hours >= 0 && hours <= 5
        }

        await WechatMessageSender.sendById({
          room_id: data.roomWxid,
          chat_id: state.chat_id,
          ai_msg: isCurrentTimeBetweenMidnightAnd5AM() ?  '比较晚的话，老师明天联系咱们。有时候老师可能在电话或者实地咨询中，回复不及时还请见谅。 但是看到后会马上第一时间回复！' : '有时候老师可能在电话或者实地咨询中，回复不及时还请见谅。 但是看到后会马上第一时间回复！'
        }, false)
      }

      ChatStateStore.update(state.chat_id, {
        counselor_id: counselor
      })

      await sleep(4000)

      const counselorWxIdMap = {
        'ZhangZiHao': '1688855833489166',
        'HuRong_1': '1688854611469274',
        'ZhaiShuai': '1688858168481824',
        'ZhuYangYang': '1688857131482878',
        'QueBi': '1688858345585865',
        'GaoTianJiao': '1688857681495061',
        'WeiQingPing': '1688855884406713',
        'LaoYuLei': '1688855662665360',
        'WuYuCong': '1688857407564471',
      }

      await WechatMessageSender.sendById({
        room_id: data.roomWxid,
        chat_id: state.chat_id,
        ai_msg: '老师，麻烦看一下',
        mention: counselorWxIdMap[counselor] ? [counselorWxIdMap[counselor]] : counselorWxIdMap[counselor]
      }, false)

      await WechatMessageSender.sendById({
        user_id: state.user_id,
        chat_id: state.chat_id,
        ai_msg: '好的，已经拉咱进群了，有什么问题都可以直接问专业老师',
      })

      XbbHelper.transferClueToCustomer(state.chat_id, state.user_id) // 异步处理
    } catch (e) {
      logger.error('拉群失败', e)
      // 通知进群失败
      await HumanTransfer.transfer(state.chat_id, state.user_id, HumanTransferType.FailedToJoinGroup)
    }
  }

  private static async getInvitedCounselor(state: IWorkflowState) {
    const counselorGroup =  Config.setting.wechatConfig?.counselorIds as string[]

    let counselors = counselorGroup

    const chatState = ChatStateStore.get(state.chat_id)
    const userSlots = chatState.userSlots
    if (!userSlots) {
      throw new Error('获取 userSlots 失败')
    }

    //  初二以及以下的拉群给低龄（保持原来的不变）。初三的，一半给低龄，一半给组长；
    const earlyAgeCounselors = ['momo']

    if (userSlots.education_group === UserEducationGroup.BelowJuniorOne || userSlots.grade === '初二') {
      counselors = earlyAgeCounselors

      const index = await ABTest.getGlobalStrategyForUser(state.chat_id, counselors.length)
      return { counselor: counselors[index] }
    } else if (userSlots.education_group === UserEducationGroup.JuniorTwoToFour) { // 初三，初四。低龄组和顾问组均分
      counselors = [...earlyAgeCounselors, ...counselorGroup]

      const index = await ABTest.getGlobalStrategyForUser(state.chat_id, counselors.length)
      return { counselor: counselors[index] }
    }

    if (userSlots.city) {
      const xianDailyCount = await DailyCounter.getCount('XianCenter')
      // const xianIsInLimit = xianDailyCount <= 10
      const guangzhouDailyCount = await DailyCounter.getCount('GuangzhouCenter')
      const guangzhouIsInLimit = guangzhouDailyCount <= 30
      const hangzhouDailyCount = await DailyCounter.getCount('HangzhouCenter')

      if  (userSlots.city.includes('西安')) {
        counselors = ['ZhangZiHao']
        await DailyCounter.incrementCount('XianCenter')
        logger.debug(`西安中心已拉${xianDailyCount}人`)
      } else {
        const province = await cityToProvince(userSlots.city)
        if (province && ['河南', '陕西', '山西', '甘肃'].includes(province)) {
          counselors = ['ZhangZiHao']
          await DailyCounter.incrementCount('XianCenter')
          logger.debug(`西安中心已拉${xianDailyCount}人`)
          return { counselor: counselors[0] }
        } else if (province && ['广东', '广西'].includes(province) && guangzhouIsInLimit) {
          counselors = ['LaoYuLei', 'WuYuCong']
          await DailyCounter.incrementCount('GuangzhouCenter')
          logger.debug(`广州中心已拉${guangzhouDailyCount}人`)
          const index = guangzhouDailyCount % counselors.length
          return { counselor: counselors[index] }
        } else {
          await DailyCounter.incrementCount('HangzhouCenter')
          logger.debug(`杭州中心已拉${hangzhouDailyCount}人`)
          const index = hangzhouDailyCount % counselors.length
          return { counselor: counselors[index] }
        }
      }
    }

    // 其他学生，默认轮流拉老师
    if (counselors.length >= 1) {
      const index = await ABTest.getGlobalStrategyForUser(state.chat_id, counselors.length)
      return { counselor: counselors[index] }
    } else {
      const randomIndex = Math.floor (Math.random () * counselors.length)
      const counselor = counselors[randomIndex]

      return { counselor }
    }
  }

  public static async getGroupName(userId: string) {
    const date = new Date()
    const month = date.getMonth () + 1
    const day = date.getDate ()

    // 如果是一位数字，则在前面补 0
    const monthStr = month < 10 ? `0${month}` : String(month)
    const dayStr = day < 10 ? `0${day}` : String(day)
    const dateStr = `${monthStr}${dayStr}`

    let name = await JuziHelper.getWechatNameByUserId(userId)
    name = name.slice(0, 7)
    // 名称要移除 emoji，否则会出问题
    name = name.replaceAll(RegexHelper.toGlobalRegex(RegexHelper.NOT_COMMON_CHAR), '')

    return `${dateStr} ${name} 暴叔留学规划沟通群`
  }
}