import { UserEducationGroup, UserSlotType } from '../intentionCalculate'
import { ObjectUtil } from '../../../../../../lib/object'
import logger from '../../../../../../model/logger/logger'


interface ISlotPrompt {
    task_description: string
    examples: string[]
}

export class SlotPromptHelper {
  public static getPrompt(slot: UserSlotType, educationGroup: UserEducationGroup) {
    if (!ObjectUtil.enumValues(UserSlotType).includes(slot)) {
      logger.error('未知的槽位类型', slot)
      return undefined
    }

    const slotQuestionMap = new Map<string, ISlotPrompt>(
      [
        // 初一及以下
        [`${UserEducationGroup.BelowJuniorOne}_${UserSlotType.City}`, {
          task_description: '结合上下文，询问顾客成长的城市。',
          examples: ['咱们哪里人？']
        }],
        [`${UserEducationGroup.BelowJuniorOne}_${UserSlotType.Budget}`, {
          task_description: '客户在上文中未告知预算，但是为了帮助后续做规划/提供信息，预算是一个必须问的字段。孩子现在在初一以下，还比较小，你可以问预算到本科大概有多少。',
          examples: ['规划到本科的话，咱大概有多少预算？']
        }],
        [`${UserEducationGroup.BelowJuniorOne}_${UserSlotType.IsStudyAbroad}`, {
          task_description: '对话背景是，客户在描述自己方案偏好的时候没有具体说是否留学的诉求。',
          examples: ['考虑出国吗？']
        }],
        [`${UserEducationGroup.BelowJuniorOne}_${UserSlotType.IsBudgetPerYear}`, {
          task_description: '需要确认下预算是每年还是总共',
          examples: ['咱这个预算是每年么？']
        }],

        // 初二初三
        [`${UserEducationGroup.JuniorTwoToFour}_${UserSlotType.IsParent}`, {
          task_description: '在上文对话过程中，无法判断对话主体是否是家长，所以结合上下文询问是否是家长。',
          examples: ['咱这边是家长还是同学？']
        }],
        [`${UserEducationGroup.JuniorTwoToFour}_${UserSlotType.Goal}`, {
          task_description: '在上文对话过程中，无法判断客户当前遇到的问题和想达成目标是什么，你可以结合上下文问出当前客户的诉求和目标。一般来说来访的需求大概有3类你可以参考提问：家长认为孩子成绩不错，希望通过规划去上更好资源的学校；家长认为孩子成绩普通，希望为其保留更多可能性，未来既有体制外的选择，也有国内教育体制的可能性；家长认为孩子成绩不是很好，但是希望有文凭，怎么规划拿到相关文凭的可能性高。',
          examples: ['孩子什么情况现在，具体遇到什么问题？']
        }],
        [`${UserEducationGroup.JuniorTwoToFour}_${UserSlotType.PreferredPlan}`, {
          task_description: '为了后续提供更符合其想法的升学规划，先询问客户对实现自己目标的路径有什么想法。',
          examples: ['咱们目前自己有什么想法呢？家里有没有大致的规划？']
        }],
        [`${UserEducationGroup.JuniorTwoToFour}_${UserSlotType.IsStudyAbroad}`, {
          task_description: '需要明确询问其出国的时间点的考虑。比如高中阶段就出国，还是本科阶段出国还是其他想法。如果已经明确告知不打算出国，其实我们也就不必问出国时间了。',
          examples: ['考虑出国的吗？打算高中出去，还是本科再出去？']
        }],
        [`${UserEducationGroup.JuniorTwoToFour}_${UserSlotType.City}`, {
          task_description: '为了对后续规划更准确，需要询问其城市的情况。',
          examples: ['咱们是在哪个城市？']
        }],
        [`${UserEducationGroup.JuniorTwoToFour}_${UserSlotType.GPA}`, {
          task_description: '为了对后续规划更准确，需要询问其成绩的情况。',
          examples: ['平时成绩怎么样？']
        }],
        [`${UserEducationGroup.JuniorTwoToFour}_${UserSlotType.Budget}`, {
          task_description: '客户未告知预算，但是为了帮助后续做规划/提供信息，需要询问预算。',
          examples: ['假设把本科读完的话，学费加生活费一起的总预算，大致有多少？']
        }],
        [`${UserEducationGroup.JuniorTwoToFour}_${UserSlotType.IsBudgetPerYear}`, {
          task_description: '需要确认下预算是每年还是总共',
          examples: ['咱这个预算是每年么？']
        }],

        // 高一～高三
        [`${UserEducationGroup.HighSchool}_${UserSlotType.IsGaokaoStudent}`, {
          task_description: '在上文对话过程中，无法判断对用户是否为高考生，需要进行确认',
          examples: ['咱这边是高考生么？']
        }],
        [`${UserEducationGroup.HighSchool}_${UserSlotType.IsParent}`, {
          task_description: '在上文对话过程中，无法判断对话主体是否是家长，所以结合上下文询问是否是家长。',
          examples: ['咱这边是家长还是同学？']
        }],
        [`${UserEducationGroup.HighSchool}_${UserSlotType.IsParentSupported}`, {
          task_description: '询问父母是否支持留学的计划',
          examples: ['咱这边家里什么想法，家里支持么？']
        }],
        [`${UserEducationGroup.HighSchool}_${UserSlotType.Goal}`, {
          task_description: '询问客户当前遇到的问题和想达成目标是什么。',
          examples: ['孩子什么情况现在，具体遇到什么问题？', '孩子，你现在什么情况，有什么要咨询？']
        }],
        [`${UserEducationGroup.HighSchool}_${UserSlotType.PreferredPlan}`, {
          task_description: '为了后续提供更符合其想法的升学规划，询问客户对实现自己目标的路径有什么想法。',
          examples: ['咱们目前自己有什么想法呢？家里有没有大致的规划？']
        }],
        [`${UserEducationGroup.HighSchool}_${UserSlotType.IsStudyAbroad}`, {
          task_description: '需要询问其出国的考虑。',
          examples: ['考虑出国的吗？打算什么时候出去？']
        }],
        [`${UserEducationGroup.HighSchool}_${UserSlotType.IntendedProject}`, {
          task_description: '需要询问客户是否已经有意向的目标国家或项目。',
          examples: ['有自己想去的国家或者学校吗？']
        }],
        [`${UserEducationGroup.HighSchool}_${UserSlotType.City}`, {
          task_description: '为了对后续规划更准确，需要询问其城市的情况。',
          examples: ['咱们是在哪个城市？']
        }],
        [`${UserEducationGroup.HighSchool}_${UserSlotType.GPA}`, {
          task_description: '为了对后续规划更准确，需要询问其成绩的情况。',
          examples: ['平时成绩怎么样？']
        }],
        [`${UserEducationGroup.HighSchool}_${UserSlotType.LanguageTestScore}`, {
          task_description: '为了对后续规划更准确，需要询问其英语成绩的情况。如果用户没有明确说明可以进一步提问："具体多少分？"',
          examples: ['平时英语成绩怎么样？']
        }],
        [`${UserEducationGroup.HighSchool}_${UserSlotType.Budget}`, {
          task_description: '客户未告知预算，但是为了帮助后续做规划/提供信息，需要询问预算',
          examples: ['学费加生活费一起的总预算，大致有多少？']
        }],
        [`${UserEducationGroup.HighSchool}_${UserSlotType.IsBudgetPerYear}`, {
          task_description: '需要确认下预算是每年还是总共',
          examples: ['咱这个预算是每年么？']
        }],

        // 本科
        [`${UserEducationGroup.University}_${UserSlotType.Goal}`, {
          task_description: '询问客户当前遇到的问题和想达成目标是什么。',
          examples: ['有什么诉求？咱们读完书之后有什么规划？']
        }],
        [`${UserEducationGroup.University}_${UserSlotType.PreferredPlan}`, {
          task_description: '为了后续提供更符合其想法的升学规划，询问客户对实现自己目标的路径有什么想法。',
          examples: ['咱们目前自己有什么想法？']
        }],
        [`${UserEducationGroup.University}_${UserSlotType.IsStudyAbroad}`, {
          task_description: '通过前文沟通不能确定客户国内升学，还是去海外升学，所以结合上下文进行询问。',
          examples: ['咱们考虑出国吗？']
        }],
        [`${UserEducationGroup.University}_${UserSlotType.Budget}`, {
          task_description: '客户未告知预算，但是为了帮助后续做规划/提供信息，需要询问预算',
          examples: ['学费加生活费一起的总预算，大致有多少？']
        }],
        [`${UserEducationGroup.University}_${UserSlotType.IsBudgetPerYear}`, {
          task_description: '需要确认下预算是每年还是总共',
          examples: ['咱这个预算是每年么？']
        }],
        [`${UserEducationGroup.University}_${UserSlotType.School}`, {
          task_description: '不了解学生的学习情况，后续难以匹配规划建议，所以需要询问就读院校的信息。',
          examples: ['咱们在哪里就读？']
        }],
        [`${UserEducationGroup.University}_${UserSlotType.GPA}`, {
          task_description: '不了解学生的学习情况，后续难以匹配规划建议，所以需要询问绩点的信息。',
          examples: ['绩点是多少？']
        }],
        [`${UserEducationGroup.University}_${UserSlotType.Major}`, {
          task_description: '不了解学生的学习情况，后续难以匹配规划建议，所以需要询问所学专业的信息。',
          examples: ['咱是什么专业？']
        }],
        [`${UserEducationGroup.University}_${UserSlotType.City}`, {
          task_description: '结合上下文，询问顾客成长的城市。',
          examples: ['咱们哪里人？']
        }],

        // 大专
        [`${UserEducationGroup.College}_${UserSlotType.Goal}`, {
          task_description: '询问客户当前遇到的问题和想达成目标是什么。',
          examples: ['有什么诉求？咱们读完书之后有什么规划？']
        }],
        [`${UserEducationGroup.College}_${UserSlotType.PreferredPlan}`, {
          task_description: '为了后续提供更符合其想法的升学规划，询问客户对实现自己目标的路径有什么想法。',
          examples: ['咱们目前自己有什么想法？有什么想去的国家吗？']
        }],
        [`${UserEducationGroup.College}_${UserSlotType.IsParent}`, {
          task_description: '在上文对话过程中，无法判断对话主体是否是家长，所以结合上下文询问是否是家长。',
          examples: ['咱这边是家长还是同学？']
        }],
        [`${UserEducationGroup.College}_${UserSlotType.IsStudyAbroad}`, {
          task_description: '通过前文沟通不能确定客户是学生父母还是学生本人，所以需要进行询问。',
          examples: ['咱们是家长，还是学生本人？']
        }],
        [`${UserEducationGroup.College}_${UserSlotType.IsStudyAbroad}`, {
          task_description: '通过前文沟通不能确定客户国内升学，还是去海外升学，所以结合上下文进行询问。',
          examples: ['咱们考虑出国吗？']
        }],
        [`${UserEducationGroup.College}_${UserSlotType.Budget}`, {
          task_description: '客户未告知预算，但是为了帮助后续做规划/提供信息，所以询问预算',
          examples: ['学费加生活费一起的总预算，大致有多少？']
        }],
        [`${UserEducationGroup.College}_${UserSlotType.IsBudgetPerYear}`, {
          task_description: '需要确认下预算是每年还是总共',
          examples: ['咱这个预算是每年么？']
        }],
        [`${UserEducationGroup.College}_${UserSlotType.LanguageTestScore}`, {
          task_description: '不了解学生的学习情况，后续难以匹配规划建议，所以需要询问英语成绩',
          examples: ['英语成绩怎么样？']
        }],
        [`${UserEducationGroup.College}_${UserSlotType.Major}`, {
          task_description: '不了解学生的学习情况，后续难以匹配规划建议，所以需要所学专业',
          examples: ['咱读什么专业？']
        }],
        [`${UserEducationGroup.College}_${UserSlotType.City}`, {
          task_description: '结合上下文，询问顾客成长的城市。',
          examples: ['咱们哪里人？']
        }],

        // 硕士
        [`${UserEducationGroup.Master}_${UserSlotType.Goal}`, {
          task_description: '询问客户当前遇到的问题和想达成目标是什么',
          examples: ['有什么诉求？咱们读完书之后有什么规划？']
        }],
        [`${UserEducationGroup.Master}_${UserSlotType.PreferredPlan}`, {
          task_description: '为了后续提供更符合其想法的升学规划，询问客户对实现自己目标的路径有什么想法。',
          examples: ['咱们目前自己有什么想法？有什么想去的国家吗？']
        }],
        [`${UserEducationGroup.Master}_${UserSlotType.IsStudyAbroad}`, {
          task_description: '通过前文沟通不能确定客户国内升学，还是去海外升学，所以结合上下文进行询问。',
          examples: ['咱们考虑出国吗？']
        }],
        [`${UserEducationGroup.Master}_${UserSlotType.Budget}`, {
          task_description: '客户未告知预算，但是为了帮助后续做规划/提供信息，所以需要询问预算',
          examples: ['学费加生活费一起的总预算，大致有多少？']
        }],
        [`${UserEducationGroup.Master}_${UserSlotType.School}`, {
          task_description: '不了解学生的学习情况，后续难以匹配规划建议，所以需要询问就读院校',
          examples: ['咱们之前哪里就读？']
        }],
        [`${UserEducationGroup.Master}_${UserSlotType.GPA}`, {
          task_description: '不了解学生的学习情况，后续难以匹配规划建议，所以需要询问绩点',
          examples: ['绩点是多少？']
        }],
        [`${UserEducationGroup.Master}_${UserSlotType.Major}`, {
          task_description: '不了解学生的学习情况，后续难以匹配规划建议，所以需要询问专业',
          examples: ['咱是什么专业？']
        }],
        [`${UserEducationGroup.Master}_${UserSlotType.City}`, {
          task_description: '结合上下文，询问顾客成长的城市。',
          examples: ['咱们哪里人？']
        }],

        // 博士线
        [`${UserEducationGroup.Doctor}_${UserSlotType.Goal}`, {
          task_description: '询问客户当前遇到的问题和想达成目标是什么',
          examples: ['有什么诉求？咱们读完书之后有什么规划？']
        }],
        [`${UserEducationGroup.Doctor}_${UserSlotType.PreferredPlan}`, {
          task_description: '为了后续提供更符合其想法的升学规划，询问客户对实现自己目标的路径有什么想法。',
          examples: ['咱们目前自己有什么想法？有什么想去的国家吗？']
        }],
        [`${UserEducationGroup.Doctor}_${UserSlotType.IsStudyAbroad}`, {
          task_description: '通过前文沟通不能确定客户国内升学，还是去海外升学，所以结合上下文进行询问。',
          examples: ['咱们考虑出国吗？']
        }],
        [`${UserEducationGroup.Doctor}_${UserSlotType.Budget}`, {
          task_description: '客户未告知预算，但是为了帮助后续做规划/提供信息，所以需要询问预算',
          examples: ['学费加生活费一起的总预算，大致有多少？']
        }],
        [`${UserEducationGroup.Doctor}_${UserSlotType.School}`, {
          task_description: '不了解学生的学习情况，后续难以匹配规划建议，所以需要询问就读院校',
          examples: ['咱们之前哪里就读？']
        }],
        [`${UserEducationGroup.Doctor}_${UserSlotType.GPA}`, {
          task_description: '不了解学生的学习情况，后续难以匹配规划建议，所以需要询问绩点',
          examples: ['绩点是多少？']
        }],
        [`${UserEducationGroup.Doctor}_${UserSlotType.Major}`, {
          task_description: '不了解学生的学习情况，后续难以匹配规划建议，所以需要询问专业',
          examples: ['咱是什么专业？']
        }],
        [`${UserEducationGroup.Doctor}_${UserSlotType.City}`, {
          task_description: '结合上下文，询问顾客成长的城市。',
          examples: ['咱们哪里人？']
        }],

        // 日语生
        [`${UserEducationGroup.JapanHighSchool}_${UserSlotType.IsParent}`, {
          task_description: '在上文对话过程中，无法判断对话主体是否是家长，所以结合上下文询问是否是家长。',
          examples: ['咱这边是家长还是同学？']
        }],
        [`${UserEducationGroup.JapanHighSchool}_${UserSlotType.PreferredPlan}`, {
          task_description: '为了后续提供更符合其想法的升学规划，询问客户对实现自己目标的路径有什么想法。',
          examples: ['咱们目前自己有什么想法呢？家里有没有大致的规划？']
        }],
        [`${UserEducationGroup.JapanHighSchool}_${UserSlotType.IsStudyAbroad}`, {
          task_description: '需要询问其出国的考虑。',
          examples: ['考虑出国的吗？打算什么时候出去？']
        }],
        [`${UserEducationGroup.JapanHighSchool}_${UserSlotType.IntendedProject}`, {
          task_description: '需要询问客户是否已经有意向的目标国家或项目。',
          examples: ['有自己想去的国家或者学校吗？']
        }],
        [`${UserEducationGroup.JapanHighSchool}_${UserSlotType.City}`, {
          task_description: '为了对后续规划更准确，需要询问其城市的情况。',
          examples: ['咱们是在哪个城市？']
        }],
        [`${UserEducationGroup.JapanHighSchool}_${UserSlotType.GPA}`, {
          task_description: '为了对后续规划更准确，需要询问其成绩的情况。',
          examples: ['平时成绩怎么样？']
        }],
        [`${UserEducationGroup.JapanHighSchool}_${UserSlotType.LanguageTestScore}`, {
          task_description: '为了对后续规划更准确，需要询问其日语成绩的情况。如果用户没有明确说明可以进一步提问："具体多少分？"',
          examples: ['平时日语成绩怎么样？']
        }],
        [`${UserEducationGroup.JapanHighSchool}_${UserSlotType.Budget}`, {
          task_description: '客户未告知预算，但是为了帮助后续做规划/提供信息，需要询问预算',
          examples: ['学费加生活费一起的总预算，大致有多少？']
        }],
        [`${UserEducationGroup.JapanHighSchool}_${UserSlotType.IsBudgetPerYear}`, {
          task_description: '需要确认下预算是每年还是总共',
          examples: ['咱这个预算是每年么？']
        }],
      ])

    logger.log(`槽位提问：${slot}`)

    let prompt: undefined | ISlotPrompt

    if (educationGroup === UserEducationGroup.Gaokao) {
      prompt = slotQuestionMap.get(`${UserEducationGroup.HighSchool}_${slot}`)
    } else {
      prompt = slotQuestionMap.get(`${educationGroup}_${slot}`)
    }

    if (!prompt) {
      logger.error(`请填充槽位问题提取 ${educationGroup}_${slot}`)
      prompt = {
        task_description: `询问一下客户的${slot}`,
        examples: [],
      }
    }

    let examples = ''
    if (prompt.examples.length > 0) {
      examples = `例如：${prompt.examples.map((example) => `"${example}"`).join(',')}`
    }

    return `${prompt.task_description} ${examples}`
  }
}