import { ChatStateStore } from '../../../../storage/chat_state_store'
import { UserEducationGroup } from '../intentionCalculate'
import { getProjectBudgets } from './getProjectBudgets'
import { ProjectSelectPrompt } from '../../../../prompt/baoshu/pick_project'
import { UserSlotsExtract } from './slotsExtract'
import { BaoshuBudgetSearch } from '../../../../../elastic_search/baoshu_budget'
import { LLM } from '../../../../../../lib/ai/llm/LLM'
import { XMLHelper } from '../../../../../../lib/xml/xml'
import { LLMXMLHelper } from './xmlHelper'
import { ShouldPushProjectPrompt } from '../../../../prompt/baoshu/should_push_project'
import logger from '../../../../../../model/logger/logger'

export class UserQuerySupplement {
  public static async getSupplementPrompt(chat_id: string, userSlots: any, education_group: number, chat_history: string) {
    if (!education_group) {
      return ''
    }

    if (education_group < 0 || education_group > 8) {
      return ''
    }

    const supplementProjects = new Map<UserEducationGroup, string>([
      [UserEducationGroup.JuniorTwoToFour, `以下是初中升学经常推荐的路径，以推荐的频次作为优先级排序，可以进行参考，但是注意推荐的项目一定要符合客户当前告知你的预算。预算内没有符合路径，就不推荐任何项目。**注意预算要严格按照以下提供的数字进行解答，这对我的工作非常重要**
1. 国内读国际高中
  - 特点：总预算80w，学习3年，对于高中不放心出国，又想未来读海外名校本科的首选。
  - 优势：提前适应国外课程体系，拓展人脉，不同的国际高中体系对应不同的国家升学，本科升学优势明显。

2. 国内读本科预科 + 新加坡本科课程
  - 特点：国内2年，新加坡两年，省时，省钱。总预算85w
  - 预算：85W搞定高中和本科。
  - 适用对象：对于不想孩子过早出国留学的家庭。

3. 出国读高中
  - 特点：提前适应海外生活，海外高中的学习经历和成绩，对于冲刺海外名校优势明显。新西兰高中学制3年总预算90w，加拿大高中3年总预算90w，美国高中4年总预算200w。
  - 适用对象：初中毕业直接到海外大学本科学位的首选。

4. 出国快速本科
  - 特点：海外大学本科直通车，省时，省钱。新加坡私立大学方向预计总预算100w，新西兰初升本方向150w
  - 优势：比国内同年龄人更早拿到本科文凭。`],
      [UserEducationGroup.HighSchool, `以下是高中生升学经常推荐的路径，以推荐的频次作为优先级排序，可以进行参考，但是注意推荐的项目一定要符合客户当前告知你的预算。预算内没有符合路径，就不推荐任何项目。**注意预算要严格按照以下提供的数字进行解答，这对我的工作非常重要**
1. 国际本科3+1/2+2
  - 高考失利和无法上本科的同学的海外升学捷径，对于想体验留学生活的同学，意味着花更少的钱，本科保底，逆袭海外名校
  - 国家本科3+1，总预算50-80w，每年15w以上，分数在本科线下，英语单科在60以上，对接学校一般在QS200-300；2+2的总预算60-80w，成绩一般，英语单科80分上。不想在国内读普通本科的同学。
  
2. 港澳方向
  - 澳门总预算80w+，香港100w+；每年20-35w,“高考+留学”双保险。港澳申请与高考时间不冲突，可提前申请港澳学校，又可参加高考志愿填报，并且港澳离大陆近，对于留学不想去太远的家庭的首选。需要学生成绩过一本线。

3. 新加坡
  - 总预算65-80w，对于高考失利，成绩一般或者较弱的学生，通过新加坡私立大学学习，能够逆袭海外名校（QS200以内）

4. 韩国、马来西亚
  - 适合低预算，想出国留学的同学，4年总预算50W以内。既离家不远，又省钱，还有机会申请进QS前200的海外大学
  - 韩国总预算50-60w，每年10w以上，韩语成绩在韩国大学的录取中占有较大的权重，即便高中成绩一般，但是韩语好，仍然有机会申请进QS前100的韩国大学，所以适合不想上专科，英语一般，想拿个本科文凭的人。

5. 英国、澳洲方向
  - 2个国家总预算都要125w多，每年至少30w，热门留学国家，名校多，升学方式多样，预科、国际大一或者高考成绩，可冲击高排名海外大学

6. 中外合办4+0
  - 总预算25-50；每年10w以上，达到本科线，不甘心只读个普通双非，英语单科100分以上，但又没有充足预算出国，想拿一个海外名校本科文凭的同学首选。 注意不要提及能拿国内外双文凭，一般非高考渠道只能拿海外文凭。
    4+0, 3+1 是指国内4年/3年， 国外0年或1年这个意思

7. 日本
  - 总预算40-60，每年15w左右`],
      [UserEducationGroup.JapanHighSchool, `以下是日语高中生升学经常推荐的路径，以推荐的频次作为优先级排序，可以进行参考。但是注意推荐的项目一定要符合客户当前告知你的预算。预算内没有符合路径，就不推荐任何项目。**注意预算要严格按照以下提供的数字进行解答，这对我的工作非常重要**
1. 日本大学直通车1+4
   - 国内指定学校读1年日语，后4年直入日本大学。适合国内日语生或英语较弱、想往日本方向发展的学生。
   - 总预算 75-80w，每年15-20w。最低要求日语达到N5。

2. 国际本科厦门大学2+2-日本方向
   - 厦门大学读2年，后2年在日本静冈产业大学就读，获得日本大学本科学位，管理类学科和管理心理学科。
   - 总预算：50-60w，每年12-15w。免试录取条件：高考总分达本科线且英语/日语单科成绩90分及以上。

3. 南京传媒学院2+2-日本方向
   - 南京传媒学院读2年，后2年对接日本城西国际大学，艺术类留学方向。
   - 总预算 55-65w，每年14-17w。适合国内日语艺术生。

4. 集美大学2+2 日本方向
   - 集美大学读2年，后2年对接日本环太平洋大学，综合经管方向（经济学、管理学等）。
   - 总预算：40-50w，每年10-13w。适合预算有限、向往日本留学的学生。

5. 大连大学2+2/3 日本方向
   - 大连大学读2年，后2-3年根据专业不同，对接日本大学，专业选择包括漫画/动画、国际艺术设计、旅游文化等。
   - 总预算：40-50w，每年10-13w。适合国内日语生。接受高中或中专毕业生，材料审核不合格者不予录取。

6. 日本当地考学
   - 1-2年在日本语言学校学习日语，备考EJU和校内考，考上大学后在日本读4年本科。适合预算和时间充裕、对日本名校有追求的学生。
   - 总预算：80-90w，每年16-20w。适合希望考上排名靠前的日本大学（如早庆上理）。

日语较差或成绩较差推荐 1，2，3，4，5 方向，成绩不错推荐 6 可以冲名校。2，3，4，5 跟 1的区别是想尽快出国，还是缓一年再出国。`],
      [UserEducationGroup.College, `以下是大专生升学经常推荐的路径，以推荐的频次作为优先级排序，可以进行参考。但是注意推荐的项目一定要符合客户当前告知你的预算。预算内没有符合路径，就不推荐任何项目。**注意预算要严格按照以下提供的数字进行解答，这对我的工作非常重要**
1. 韩国3+1中文授课专升本
    - 总预算10-15w，省钱、省时，也省去学语言的精力，离国内近。0语言入读，低预算，1年拿到韩国本科文凭。

2. 新加坡
    - 总预算30-65w，新加坡私立大学升学选择多，升本升硕项目多。既可以申对接的海外名校的本科，也可以选择一步到位升硕士。若去英澳预算不足，那新加坡就是英语系首选。

3. 英国和澳洲方向
    - 英国总预算35-45w，学制一般1年。 澳洲80-90w，学制一般2-2.5年。热门留学国家，名校多，澳洲和英国都有对应的专升本项目，拿到英澳大学的本科学位。作为跳板，可申请世界名校硕士。

4. 香港方向
    - 学制2年的，总预算30w+，离家近，有中文授课，2年专升本学习，既拿到了本科学位，又可以考虑在当地工作，工作5年之后，拿到香港身份。`],
      [UserEducationGroup.University, `以下是本科生升学经常推荐的路径，以推荐的频次作为优先级排序，可以进行参考。但是注意推荐的项目一定要符合客户当前告知你的预算。预算内没有符合路径，就不推荐任何项目。**注意预算要严格按照以下提供的数字进行解答，这对我的工作非常重要**
1. 英国
    - 学制1年，预算40-60w，偏好国内985、211学生，其次是高分双非，对学术要求相对高。1年制硕士，省时省钱，回国认可度高。

2. 澳洲
    - 每年预算40-55w，学制一般1-2年，宽进严出，澳洲硕士申请门槛低于英美，低分也能逆袭前100，其次澳洲具备移民属性，硕士毕业可留在当地工作拿身份。

3. 香港
    - 每年预算30-50w，香港硕士1年制，省时省钱，认可度高，离家近，毕业之后留港工作，可拿香港身份。

4. 新加坡
    - 每年预算30-35w，学制1-2年，去新加坡读硕士包容性强，学霸可以冲新二；而普娃和成绩较弱的学生，可以冲私立大学的合作办学项目，拿英美澳加的大学的硕士文凭。城市虽小，但是法制严格，留学安全。

5. 美国
    - 每年预算60-100w，1-2年学制，硕士阶段的TOP1，国际学历含金量和认可度高。美研申请不聚焦在学生的学术成绩，更看重学生全面的综合能力，就业机会多。

6. 中外合作办学硕士
    - 总预算15-35w，1-2年学制，省钱不出国，可以选择非全日制的学习方式，工作和学历提升两不误，拿海外大学的硕士文凭。

7. 韩国
    - 总预算10-30w, 1-2年学制，可以半工半读，性价比较高，三种不同语言的硕士授课项目，适合不同语言类型的学生，不会韩语、英语较弱的学生，花1-2年的时间，也可以拿韩国名校的硕士学位`],
      [UserEducationGroup.Gaokao, `以下是高中生升学经常推荐的路径，以推荐的频次作为优先级排序，可以进行参考，但是注意推荐的项目一定要符合客户当前告知你的预算。预算内没有符合路径，就不推荐任何项目。**注意预算要严格按照以下提供的数字进行解答，这对我的工作非常重要**
1. 国际本科3+1/2+2
  - 高考失利和无法上本科的同学的海外升学捷径，对于想体验留学生活的同学，意味着花更少的钱，本科保底，逆袭海外名校
  - 国家本科3+1，总预算50-80w，每年15w以上，分数在本科线下，英语单科在60以上，对接学校一般在QS200-300；2+2的总预算60-80w，成绩一般，英语单科80分上。不想在国内读普通本科的同学。
  
2. 港澳方向
  - 澳门总预算80w+，香港100w+；每年20-35w,“高考+留学”双保险。港澳申请与高考时间不冲突，可提前申请港澳学校，又可参加高考志愿填报，并且港澳离大陆近，对于留学不想去太远的家庭的首选。需要学生成绩过一本线。

3. 新加坡
  - 总预算65-80w，对于高考失利，成绩一般或者较弱的学生，通过新加坡私立大学学习，能够逆袭海外名校（QS200以内）

4. 韩国、马来西亚
  - 适合低预算，想出国留学的同学，4年总预算50W以内。既离家不远，又省钱，还有机会申请进QS前200的海外大学
  - 韩国总预算50-60w，每年10w以上，韩语成绩在韩国大学的录取中占有较大的权重，即便高中成绩一般，但是韩语好，仍然有机会申请进QS前100的韩国大学，所以适合不想上专科，英语一般，想拿个本科文凭的人。

5. 英国、澳洲方向
  - 2个国家总预算都要125w多，每年至少30w，热门留学国家，名校多，升学方式多样，预科、国际大一或者高考成绩，可冲击高排名海外大学

6. 中外合办4+0
  - 总预算25-50；每年10w以上，达到本科线，不甘心只读个普通双非，英语单科100分以上，但又没有充足预算出国，想拿一个海外名校本科文凭的同学首选。 注意不要提及能拿国内外双文凭，一般非高考渠道只能拿海外文凭。
    4+0, 3+1 是指国内4年/3年， 国外0年或1年这个意思

7. 日本
  - 总预算40-60，每年15w左右`]
    ])

    const projectsDescription = supplementProjects.get(education_group) || ''

    // 从项目列表中中选出合适的项目
    const selectedProject = await this.pickProject(chat_id, userSlots, projectsDescription, chat_history)
    if (selectedProject && selectedProject.budgetNotEnough) { // 没有合适预算的项目
      ChatStateStore.update(chat_id, { pickedMiddleProject: selectedProject.projectDescription })
      return selectedProject.projectDescription
    } else if (selectedProject && !selectedProject.budgetNotEnough) {
      // Cache 一下，推荐项目
      ChatStateStore.update(chat_id, { pickedMiddleProject: selectedProject.projectDescription })
      return selectedProject.projectDescription
    } else { // 无需推送项目
      return '**一定要注意：现在没有了解用户完整情况，一定不要给用户主动推荐国家或项目，因为项目推荐会不准确, 只进行提问即可**'
    }
  }


  private static async pickProject(chat_id: string, userSlots: any, projects: string, chat_history: string) {
    //  判断是否需要进行推荐项目
    const isNeedPushProject = await this.isNeedPushProject(chat_history)
    if (isNeedPushProject) {
      const userProjects = await getProjectBudgets(chat_id)
      // 用户想去的项目预算不够
      if (!userProjects.isBudgetEnough) {
        const requiredBudgets  =  userProjects.projectBudgets.map((project) => {
          if (!project) return ''
          return `${project.projectName} 预算范围：${project.budgetLowerBound}-${project.budgetUpperBound}万`
        }).join('\n')

        return {
          budgetNotEnough: true,
          projectDescription: `用户的预算对于想要去的国家或学校不够，礼貌的询问，预算可以提高吗。并告知合理预算范围多少。
用户的预算是 ${userSlots.budget}。
想要去的项目的预算为: ${requiredBudgets}
例如：你这个预算可能不太够啊，去 xx 大概需要 xx, 你预算能提高么`
        }
      }

      const userProjectBudget = userProjects.projectBudgets.map((project) => {
        if (!project) return ''
        return `${project.projectName} 预算范围：总共${project.budgetLowerBound}-${project.budgetUpperBound}万， 每年 ${project.annualBudgetLowerBound}-${project.annualBudgetUpperBound}万`
      }).join('\n')

      if (userProjectBudget === '') {
        projects += `\n\n${  await this.getCountryBudgetList(userSlots.education_group)}`
      } else {
        projects += `\n\n${userProjectBudget}`
      }

      return {
        budgetNotEnough: false,
        projectDescription: await this.selectProject(chat_id, await ProjectSelectPrompt.format(UserSlotsExtract.formatSlots(userSlots), projects))
      }
    } else {
      return null
    }
  }

  public static async getCountryBudgetList(education_group: UserEducationGroup | undefined) {
    const educationGroupMap = {
      [UserEducationGroup.JuniorTwoToFour]: '高中',
      [UserEducationGroup.HighSchool]: '本科',
      [UserEducationGroup.University]: '硕士',
      [UserEducationGroup.College]: '大专'
    }
    let queryEducationGroup

    if (!education_group || [UserEducationGroup.BelowJuniorOne].includes(education_group)) {
      queryEducationGroup = '本科'
    } else {
      queryEducationGroup = educationGroupMap[education_group]

      if (!queryEducationGroup) {
        queryEducationGroup = '本科'
      }
    }

    const budgets = await BaoshuBudgetSearch.listProjectsByEducationGroup(queryEducationGroup)

    // console.log(JSON.stringify(searchResponse, null, 4))

    let res = `参考以下不同国家${queryEducationGroup}留学费用区间（以人民币计）:
`

    for (const budget of budgets) {
      const project = budget._source
      const budgetRange = project.budgetLowerBound === project.budgetUpperBound ? `${project.budgetLowerBound}+` : `${project.budgetLowerBound}-${project.budgetUpperBound}`

      res += `${project.projectName} ${budgetRange}万\n`
    }

    return res
  }

  private static async selectProject(chat_id: string, prompt: string) {
    const llmRes =  await new LLM({ meta: { chat_id, name: 'projectPick' } }).predict(prompt)
    let res = XMLHelper.extractContent(llmRes, 'recommendation')
    if (!res) {
      res = llmRes
    }
    logger.log({ chat_id }, '推荐项目：', res)

    return `参考建议：\n${res}`
  }

  private static async isNeedPushProject(chat_history: string) {
    const res = await LLMXMLHelper.extractBooleanAnswer(await ShouldPushProjectPrompt.format(chat_history), {
      tagName: 'answer',
      trueFlag: 'true',
      falseFlag: 'null'
    })

    logger.debug('是否需要推荐项目：', res)

    if (!res) {
      return false
    }

    return res
  }
}