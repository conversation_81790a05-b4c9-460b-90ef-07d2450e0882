import { ChatStateStore } from '../../../../storage/chat_state_store'

import { LLM } from '../../../../../../lib/ai/llm/LLM'
import { XMLHelper } from '../../../../../../lib/xml/xml'
import { JSONHelper } from '../../../../../../lib/json/json'
import { BaoshuBudgetSearch } from '../../../../../elastic_search/baoshu_budget'
import logger from '../../../../../../model/logger/logger'

export async function getProjectBudgets(chat_id: string) {
  // 优先以国家进行匹配
  const userSlots = ChatStateStore.get(chat_id).userSlots
  if (!userSlots) {
    throw new Error('用户槽位为空')
  }

  let matchProjects: string[] = []

  if (userSlots.user_intended_country && userSlots.user_intended_country.length > 0) {
    matchProjects = userSlots.user_intended_country
  } else if (userSlots.user_intended_school && userSlots.user_intended_school.length > 0) {
    matchProjects = await schoolsToCountries(userSlots.user_intended_school)
    // 更新下国家列表
    ChatStateStore.update(chat_id, {
      userSlots: {
        user_intended_country: matchProjects
      }
    })
    logger.log({ chat_id: chat_id }, 'schoolsToCountries: ', userSlots.user_intended_school, '->', matchProjects)
  } else if (userSlots.user_intended_project && userSlots.user_intended_project.length > 0) {
    matchProjects = userSlots.user_intended_project
  } else {
    return {
      isBudgetEnough: true,
      projectBudgets: []
    }
  }

  return await BaoshuBudgetSearch.isBudgetEnough({
    education_group: userSlots.education_group as number,
    budget: userSlots.budget ?? 9999,
    budget_is_per_year: userSlots.budget_is_per_year,
    projectNames: matchProjects,
    application_stage: userSlots.application_stage
  })
}



export  async function schoolsToCountries(user_intended_school: string[]) {
  const llm = new LLM()
  const llmRes = await llm.predict(`You are tasked with identifying the country of origin for each university listed in the input. Here's how you should proceed:
    
    1. For each university in the list, use your internal knowledge database to determine the country where the university is located. If a university's country cannot be determined, label it as "Unknown".
    
    2. Compile the results into a list where each entry corresponds to the country of the respective university in the order they appear in the input list.
    
    3. Present the final list of countries in Chinese. Ensure that the list of countries is in the same order as the list of universities provided. Put the answer in <countries> tag.
    
    For example, if the input list is:
    <universities>
    ["MIT", "滑铁卢"]
    </universities>
    
    Your output should be:
    <countries>
    ["美国", "加拿大"]
    </countries>
    
    This task requires careful attention to detail to ensure accuracy in identifying the correct country for each university. Use reliable sources from your internal database and ensure the output format is strictly adhered to as shown in the example.
    
    <universities>
    {$UNIVERSITIES}
    </universities>
`.replace('{$UNIVERSITIES}', JSON.stringify(user_intended_school)))

  const xmlRes = XMLHelper.extractContent(llmRes, 'countries')

  if (!xmlRes)
    return []

  const countries = JSONHelper.parse(xmlRes)

  if (!Array.isArray(countries)) {
    return []
  }

  return countries
}