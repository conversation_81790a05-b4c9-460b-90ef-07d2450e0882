import { BaoshuWorkFlowNode, trackInvoke } from './baseNode'
import { IWorkflowState } from '../flow'
import { BaoshuNode } from '../type'
import { ChatStateStore } from '../../../storage/chat_state_store'
import { LLMNode } from './llm'
import { UserEducationGroup } from './intentionCalculate'
import { ChatHistoryService } from '../../chat_history'
import { UserInfoExtractPrompt } from '../../../prompt/baoshu/user_info_extract'
import { BooleanUserInfoExtractPrompt } from '../../../prompt/baoshu/study_abroad_and_is_parent'
import { UserSlotsExtract } from './helper/slotsExtract'
import { EarlyAgeInvitePendingNode } from './earlyAge'
import { StudyPlanRoutingNode } from './studyAbroadPlan'

import chalk from 'chalk'
import logger from '../../../../../model/logger/logger'
import { LLM } from '../../../../../lib/ai/llm/LLM'


export class BudgetReConfirmNode extends BaoshuWorkFlowNode {
  @trackInvoke
  public static async invoke(state: IWorkflowState) {
    logger.log({ chat_id: state.chat_id }, '用户没有说预算，需要再确认一次')

    // 如果没有预算，进行多次询问，最多询问3次，如果还没有就进入结束节点，有的话返回正常流程
    const chatState = ChatStateStore.get(state.chat_id)
    const nodeInvokeCount = chatState.nodeInvokeCount[BudgetReConfirmNode.name]
    const chatHistory = await ChatHistoryService.formatHistory(state.chat_id)
    const prompt = await UserInfoExtractPrompt.format(chatHistory)
    const booleanPrompt = await BooleanUserInfoExtractPrompt.format(chatHistory)
    const currentUserSlots = await UserSlotsExtract.extract(prompt, booleanPrompt, { chat_id: state.chat_id, user_id: state.user_id, round_id: state.round_id })

    // 提取到预算了，返回原来的流程
    if (currentUserSlots && currentUserSlots.budget) {
      // 预算合适，拉回正常流程
      ChatStateStore.update(state.chat_id, {
        userSlots: {
          budget_is_per_year: currentUserSlots.budget_is_per_year,
          budget: currentUserSlots.budget
        }
      })

      // 总预算 < 10, 直接 pass
      if ((!currentUserSlots.budget_is_per_year && currentUserSlots.budget < 10) || (currentUserSlots.budget_is_per_year && currentUserSlots.budget < 5)) {
        await LLMNode.invoke({
          state,
          dynamicPrompt: '用户当前预算太低，可以用类似的语句委婉回复：“咱这个预算可能不太够，可以先存存钱，之后再来找叔”。'
        })

        return BaoshuNode.END
      }

      logger.log({ chat_id: state.chat_id }, chalk.redBright('更新预算：', JSON.stringify({
        budget_is_per_year: currentUserSlots.budget_is_per_year,
        budget: currentUserSlots.budget
      })))

      if (chatState.userSlots && chatState.userSlots.education_group === UserEducationGroup.BelowJuniorOne) {
        return await EarlyAgeInvitePendingNode.invoke(state)
      }

      return await StudyPlanRoutingNode.invoke(state)
    } else {
      if (nodeInvokeCount <= 2) {
        await LLMNode.invoke({
          state,
          dynamicPrompt: '在过去的对话中确认不了用户的预算，这里做一次最后的主动确认。例如："预算这边还是要有个大体的范围，这样才能做更精准的方案匹配", 如果已经说过同样的话术，可以换一种方式重新讲一下。',
          referenceChatHistory: true // 以完成任务为优先
        })
        return BaoshuNode.BudgetReConfirm
      } else {
        // 多轮确认后，用户依然没有提供预算，结束
        await LLMNode.invoke({
          state,
          dynamicPrompt: '用户当前预算太低，可以用类似的语句委婉回复：“预算不清楚的话，这边无法做准确的方案推荐”。'
        })

        return BaoshuNode.END
      }
    }
  }
}