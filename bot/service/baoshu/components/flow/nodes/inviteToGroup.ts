import { BaoshuWorkFlowNode, trackInvoke } from './baseNode'
import { IWorkflowState } from '../flow'
import { ChatStateStore, ConversationState } from '../../../storage/chat_state_store'
import { WechatMessageSender } from '../../message_send'
import { BaoshuNode } from '../type'

import { InviteToGroupHelper } from './helper/inviteToGroup'
import { LLMNode } from './llm'
import { UserEducationGroup } from './intentionCalculate'
import logger from '../../../../../model/logger/logger'
import { sleep } from '../../../../../lib/schedule/schedule'

export class InviteToConsultantNode extends BaoshuWorkFlowNode {
  @trackInvoke
  public static async invoke(state: IWorkflowState) {
    // 邀请顾问拉群
    const chatState = ChatStateStore.get(state.chat_id)
    const status = ChatStateStore.getStatus(state.chat_id)
    if (!status.consultGroupInvited && !status.consultGroupEntered) {
      ChatStateStore.update(state.chat_id, {
        state: ConversationState.ConsultGroupInvited,
        status: {
          consultGroupInvited: true
        }
      })

      await LLMNode.invoke({
        state,
        dynamicPrompt: '这里回答的作用是方便过渡到邀请专业老师解答，所以不用做太具体的解答，给用户提供情绪价值，并简单回答用户的问题'
      })
      await sleep(4000)

      if (chatState.userSlots && chatState.userSlots.education_group === UserEducationGroup.HighSchool && chatState.userSlots.is_parent === false) {
        await WechatMessageSender.sendById({
          user_id: state.user_id,
          chat_id: state.chat_id,
          ai_msg: '这样吧，我给你拉个定向规划老师给你具体规划规划，咱们尽早准备，叫上你家长一起，沟通比较高效'
        })
      } else if (chatState.userSlots && chatState.userSlots.has_offer) {
        await WechatMessageSender.sendById({
          user_id: state.user_id,
          chat_id: state.chat_id,
          ai_msg: '我这会有点忙，我拉个老师给你讲讲，后续申请如果想去外面，咱们也能提早规划起来？'
        })
      } else if (chatState.userSlots && (chatState.userSlots.education_group === UserEducationGroup.Doctor || chatState.userSlots.education_group === UserEducationGroup.Master)) {
        await WechatMessageSender.sendById({
          user_id: state.user_id,
          chat_id: state.chat_id,
          ai_msg: '博士的申请，比较看重你的科研成果、文章发表和语言能力的，这些都是需要提前开始做准备的。咱们的情况需要进一步的规划和讨论，一句两句也说不太清楚，我安排专业老师给你沟通一下吧，给你规划建议和具体方案。稍等，我给你拉群'
        })
      } else {
        await WechatMessageSender.sendById({
          user_id: state.user_id,
          chat_id: state.chat_id,
          ai_msg: '咱们真考虑出去的话，我给你安排个专业方向很强的老师给你具体规划下'
        })
      }

      return BaoshuNode.InviteConsultant
    } else {
      // 如果已经进群，进入通知用户直接跟顾问沟通阶段
      if (status.consultGroupEntered) {
        logger.debug('用户已经进群，进入通知用户直接跟顾问沟通阶段')
        return BaoshuNode.END
      }

      return await InviteToGroupHelper.handleEnterGroup(state)
    }
  }
}