import { getChatId } from '../../../../../config/chat_id'
import { Config } from '../../../../../config/config'
import { ChatHistoryService } from '../../chat_history'
import { ChatStateStore, ChatStatStoreManager } from '../../../storage/chat_state_store'
import { BaoshuFlow } from '../flow'
import { LogStoreService } from '../../log_store'
import { ChatDB } from '../../../database/chat'
import { UUID } from '../../../../../lib/uuid/uuid'

Config.setting.localTest = true
const user_id = UUID.short()
const chat_id = getChatId(user_id)

async function chat(messages: string[]) {
  for (const message of messages) {
    await BaoshuFlow.step(chat_id, user_id, message)
  }
}

let messages: string[] = []

describe('Test', function () {
  beforeAll(() => {
    Config.setting.wechatConfig =   {
      orgToken: 'e0d70927040a4efa92b79b7279ecb1c1',
      id: '1688854546332791',
      botUserId: 'ShengYueQing',
      name: '暴叔',
      notifyGroupId: 'R:10829337560927503',
      counselorIds: []
    }
  })

  beforeEach(async () => {
    // await ChatHistoryService.clearChatHistory(chat_id)
    // await LogStoreService.clearChatLog(chat_id)
    // await ChatStatStoreManager.clearState(chat_id)
    // if (await ChatDB.getById(chat_id)) {
    //   await ChatDB.setHumanInvolvement(chat_id, false)
    // }
  }, 1E8)

  afterEach(async () => {
    await chat(messages)
  }, 5 * 60 * 1000)

  it('红包活动', async () => {
    messages = [
      '暴叔，你那个红包怎么搞',
      '这个是红包封面？',
      '好的'
    ]
  }, 60000)

  it('香港活动1', async () => {
    messages = [
      '暴叔，12月14号您们展位号发下哦!',
      '香港的那个',
      '好的好的'
    ]
  }, 60000)

  it('香港活动', async () => {
    messages = [
      '暴叔，你那个活动怎么搞',
      '香港的那个',
      '好的好的'
    ]
  }, 60000)

  it('大一计算机 进群', async () => {
    messages = [
      '你好',
      '我大一，学的是计算机，但是感觉学的东西不是很实用，想转专业，有什么建议吗？',
    ]
  })

  it('不是意向客户', async () => {
    messages = [
      '暴叔',
      '不打算留学',
      '我就想问问物理，化学，地理',
      '选什么专业好啊？',
      '我有点迷茫',
    ]
  })

  it('不是意向客户2', async () => {
    messages = [
      '就是问一下职高',
      '怎么规划一下留学会对家庭没什么负担',
      '嗯，意思是负担不那么大',
      '每年几万块吧',
    ]
  })

  it('台湾', async () => {
    messages = [
      '暴叔！我是台湾人，考到咱们大陆上海戏剧学院的本科了，想再读一下研究生，您有什么建议吗！？有没有更好的办法深造一下啊！！！',
      '那预算要准备多少呀',
    ]
  })

  it('36想要出国进修或留学', async () => {
    messages = [
      '你好！',
      '本人今年36岁，工作经验10+年。计划出国留学进修或国内MBA，求专业意见与指导。谢谢',
      '另，也在考虑澳洲或新西兰，读幼教类专业后当地就业，拿PR',
      '非常犹豫、彷徨和纠结……',
    ]
  })

  it('flow test 昆士兰理工和科廷', async () => {
    messages = [
      '爆叔你好',
      '想问一下昆士兰理工和科廷怎么选',
      '国内会参考usnews吗',
      '我看昆士兰理工只有usnews前200',
    ]
  })

  it('留学规划 -> 韩国硕士留学咨询', async () => {
    messages = [
      '你好 我想了解一下 韩国学习语言',
      '学多长时间可以通过他们的那个语言奖学金',
      '咱们有推荐学习语言的地方吗暴叔',
      '就是要直接去韩国读预科吗 就是毕业以后要读多长时间预科',
      '现在大三本科暴叔',
      '[捂脸]对不起哈哈哈 我在抖音上给你说了 我以为在这说过了[捂脸]',
      '我是学钢琴专业的',
      '就是不算生活费',
      '光学费是吧暴叔',
      '奥 2～三年硕士毕业是吧',
      '我暂时的打算是想要去读完博士再回来',
      '本来是了解的俄罗斯 但是他们现在战争太严重不太敢去了',
      '那要是通过咱们过去 咱们这边怎么收费暴叔',
      '就是咱这边收两万 韩国的另算是吧暴叔',
      '好的暴叔 我先了解一下',
      '祝你生意兴隆暴叔',
    ]
  })

  it('留学规划测试 -> 进群', async () => {
    messages = [
      '你好',
      '我是在职硕士毕业，34岁，需要申请博士，国内不行要脱产，工作走不开，看看有没合适的方案呢谢谢',
      '预算不离谱就行，不太是问题',
      '我比较想去国内分校的类型',
      '大概 300 左右。暴叔有什么具体点的推荐吗',
      '谢谢',
    ]
  })

  it('留学规划测试 -> 不同意进群', async () => {
    messages = [
      '暴叔您好，我看你b站视频很久了，我个人是自考专科和，专科23年底毕业，本科也是自考预计是24年毕业，我想水个国外硕士，但是本科学位证要25年才能拿到等的太久了，我听朋友说蒙古国留学水硕士可以国内也认',
      '我是非全日制本科没学位证想混个一年制硕士研究生预算五六万之间可出境或者不出境',
      '多少预算有办法呢',
      '蒙古国的不行吗，都说蒙古国的便宜',
      '我对qs排名没要求',
    ]
  })

  it('留学规划测试 -> 英国硕士留学咨询', async () => {
    messages = [
      '暴叔 您好关注您好久了。我是就读于武汉一所大学的专科的学生，明年5月份是专升本考试。虽然家庭一般但是我想有条能让我通向国外学校的路，所以来向您咨询一下。',
      '年级：大三（专科）英语情况：其他（托福/雅思/四六级/其他）目标国家：英国意向学校：伯明翰城市大学整体预算：50w其他需求：珠宝设计或其他美术专业（发展前景好的专业也可以接受）',
      '因为预算有限 所以就考虑性价比了',
      '升硕是不是省时性价比也高些',
      '想升硕吧',
      '专业',
      '谢谢叔',
      'mba其实也可以考虑一下',
      '好的好的 我考虑一下',
      '谢谢暴叔',
    ]
  })

  it('留学规划测试 -> G5大学申请', async () => {
    messages = [
      '暴叔 专科可以上G5吗 专科成绩很烂 现在在考cpa 一年过了3门',
      'cpa成绩有用不',
      '读专升本',
      '要多久呀',
      '暴叔 我打算明天读topup',
      '上g5',
      '我现在要开始准备什么',
    ]
  })

  it('留学规划测试 -> 留学咨询会话', async () => {
    messages = [
      'hi，你好',
      '1. 马来西亚理科大学；GPA-A：3.26（截止为第三学期，现在第四学期在读）；年纪：22；想要申请环境相关的偏文科专业；雅思5分',
      '2. 暂时没有明确目的国，欧洲，香港，澳洲，新加坡都在考虑。一年15w以下可以接受，越少越好??',
      '那欧洲其他小国家呢',
      '如果只读一年20w也可以',
      '我北京的，我本科读的是环境科学，研究生不想再读这么理科的了',
      '具体有什么学校推荐？',
      '那说一下'
    ]
  })

  it('test', async () => {
    messages = [
      'hi',
      '我大专，预算30去哪里合适',
      '哪个好的，感觉这两个不行',
      '太偏了吧',
      'ok',
    ]
  })

  it('commonSlots 提取失败', async () => {
    messages = [
      '暴叔我大学学的广告学专业 \n 以后适合申请什么大学',
      '可以考虑',
      '我刚上大一，如果想申请海外学校，需要准备什么条件现在。',
    ]
  })

  it('意图覆盖问题', async () => {
    messages = [
      '暴叔您好，我先是一名普高高三学生，是在英语学科上有优势。大型考试英语单科一般在140左右，2021年年底首考雅思7.0。但是其他学科并没有像英语一样的成绩，故想咨询留学途径以获得更好的大学教育。',
      '一年30个左右',
      '具体有什么学校吗',
    ]
  })

  it('路线3', async () => {
    messages = [
      '暴叔您好，我想问下斯坦福和哈弗哪个好？',
      '我已经都被录取了，但是不知道选哪个',
      '能再具体一点么',
      '谢谢',
      '嗯嗯',
      '好的呢',
    ]
  })

  it('路线2 测试', async () => {
    messages = [
      '你好，暴叔',
      '我想咨询下申研究生',
      '我滑铁卢毕业，均分有点低，可以申请哪里。75分左右',
      '100w 左右',
      '好的呢',
    ]
  })

  it('路线3 测试', async () => {
    messages = [
      '你好，暴叔',
      '我想问下滑铁卢和多伦多哪个好？',
      '想学计算机，人工智能这块',
      '100w 左右',
      '好的呢',
    ]
  })

  it('韩国测试', async () => {
    messages = [
      '你好 我想了解一下 韩国学习语言\n学多长时间可以通过他们的那个语言奖学金',
      '咱们有推荐学习语言的地方吗暴叔\n现在大三本科',
      '非首尔地区',
      '可以可以 暴叔有推荐的项目吗 我钢琴专业的',
      '嗯嗯 我想问下具体我音乐适合申请的项目',
    ]
  })

  it('100w', async () => {
    messages = [
      '高三 本科 口腔医学 一年15w 英语不行 无目的国家',
      '想去读硕士',
      '在华中科技大学，成绩也就中等吧，均分80',
      '能推荐下项目么',
    ]
  })

  it('无上限测试', async () => {
    messages = [
      '暴叔英语不好能不能上港大',
      '今年高三',
      '高考2个月后才考',
      '前2年没好好学，现在勉强考个211，感觉不如出国',
      '预算没上限的',
    ]
  })

  it('11', async () => {
    messages = [
      '你好\n上那些预科班的话高考有什么要求\n我高三生今年',
      '高考感觉不是很好，想走预科走',
    ]
  })

  it('重复问题', async () => {
    messages = [
      '你好，暴叔',
      '我现在预算 10w ，刚毕业能去哪里',
      '目前还没啥具体想法',
      '还是想好好读一下',
      '新加坡能具体讲下么',
      '我目标想冲一波名校，有啥推荐么',
    ]
  })

  it('槽位提问测试', async () => {
    messages = [
      '你好，暴叔，我毕业三年了，滑铁卢的，想申硕士，去哪比较好',
      '找工作吧',
      '75左右',
      '专业计算机，目前还没有',
      '烟台的',
    ]
  })

  it('槽位提问测试1', async () => {
    messages = [
      '想问下职高，怎么规划下留学对家庭不要有太大的负担',
      '同学',
      '没什么规划，我学习差，想冲下名校，预算100w，我天津的，英语不好',
      '平时成绩不好',
      '怎么说',
    ]
  })

  it('Bad case 1', async () => {
    messages = [
      '你好',
      '暴叔，我现在高三，还有一个月就要高考了，怎么办啊？',
      '成绩差怎么办啊',
      '具体怎么办啊',
      '考虑啊，去哪里合适',
      '上海',
      '英语也一般',
      '100',
      '有啥建议么',
      '可以具体讲讲么',
    ]
  })

  it('Bad case 2', async () => {
    messages = [
      '你好',
      '暴叔，我现在高三，还有一个月就要高考了，怎么办啊？',
      '成绩差怎么办啊',
      '具体怎么办啊',
      '考虑啊，去哪里合适',
      '上海',
      '英语也一般',
      '100',
      '有啥建议么',
      '可以具体讲讲么',
    ]
  })

  it('初一 学生', async () => {
    messages = [
      '暴叔，救救孩子\n怎么办啊，成绩差死啦',
      '初一',
      '不知道\n成绩差，感觉自己也努力了\n所以来问你',
      '预算100个',
      '上海\n那我现在要怎么做啊，你能帮帮我吗',
      '高中出去吧',
      '好的',
    ]
  })

  it('初一 父母', async () => {
    messages = [
      '暴叔，\n怎么办啊，我孩子成绩差死啦，初一， 在上海',
      '不知道\n成绩差，感觉自己也努力了\n所以来问你',
      '预算100个',
      '那我现在要怎么做啊，你能帮帮我吗',
      '高中出去吧',
      '好的',
    ]
  })

  it('初一 父母 不进群', async () => {
    messages = [
      '暴叔，\n怎么办啊，我孩子成绩差死啦，初一， 在上海',
      '不知道\n成绩差，感觉自己也努力了\n所以来问你',
      '预算100个',
      '那我现在要怎么做啊，你能帮帮我吗',
      '高中出去吧',
      '那算了吧，想好再来找你',
    ]
  })

  it('初一 父母 不进群 10w', async () => {
    messages = [
      '暴叔，\n怎么办啊，我孩子成绩差死啦，初一， 在上海',
      '不知道\n成绩差，感觉自己也努力了\n所以来问你',
      '预算10个',
      '那我现在要怎么做啊，你能帮帮我吗',
      '高中出去吧',
      '那算了吧，想好再来找你',
    ]
  })

  it('自带方案 预算符合', async () => {
    messages = [
      '你好，暴叔，我工作两年了，滑铁卢毕业，想去读个研，咋整',
      '哈哈，不牛啊。我已经回国工作一段时间了，想去北美或者澳洲吧，继续读计算机吧',
      '读完，继续工作吧',
      '100w吧',
      '75分',
      '山东的',
      '啊？好学校申请不了么',
      '还是想冲一波'
    ]
  }, 60000)


  it('自带方案 预算不符合 10w', async () => {
    messages = [
      '你好，暴叔，我工作两年了，滑铁卢毕业，想去读个研，咋整',
      '哈哈，不牛啊。我已经回国工作一段时间了，想去北美或者澳洲吧，继续读计算机吧',
      '读完，继续工作吧',
      '10w吧',
      '75分',
      '山东的',
      '啊？好学校申请不了么',
      '还是想冲一波'
    ]
  }, 60000)

  it('无方案 预算 100w', async () => {
    messages = [
      '你好，暴叔，我工作两年了，滑铁卢毕业，想去读个研，咋整',
      '哈哈，不牛啊。我已经回国工作一段时间了，继续读计算机吧',
      '读完，继续工作吧',
      '100w吧',
      '75分',
      '山东的',
      '啊？好学校申请不了么',
      '还是想冲一波',
      '好的，你们这里有规划么'
    ]
  }, 60000)

  it('无方案 预算 10w', async () => {
    messages = [
      '你好，暴叔，我工作两年了，滑铁卢毕业，想去读个研，咋整',
      '哈哈，不牛啊。我已经回国工作一段时间了，继续读计算机吧',
      '读完，继续工作吧',
      '10w吧',
      '75分',
      '山东的',
      '啊？好学校申请不了么',
      '还是想冲一波',
      '好的，你们这里有规划么'
    ]
  }, 60000)


  it('其他人群', async () => {
    messages = [
      '你好，暴叔，你点的外卖到了',
      '你点的披萨么'
    ]
  }, 60000)

  it('合作', async () => {
    messages = [
      '你好，暴叔，我是乌克兰的留学机构，想要合作下',
      '怎么交流'
    ]
  }, 60000)


  it('情绪价值', async () => {
    messages = [
      '暴叔，你好',
      '你是本人吗',
      '哈哈哈，看了怎么样的直播',
      '很喜欢您！',
      '很幽默',
      '没有，单纯很喜欢您',
      '先关注下',
      '后续有问题再请教您',
      '谢谢',
      '嗯嗯',
      '好的呢'
    ]
  })

  it('初三', async () => {
    messages = [
      '我现在初三，我这成绩应该是没有什么好高中可以考了，家里也没有啥想法，有什么其他什么路吗',
      '预算只有15'
    ]
  })

  it('高一 ', async () => {
    messages = [
      '暴叔您好！我现在是高一 成绩也不是很理想，然后我想大学去留学，一般英语要考多少分呀',
      '预算只有15',
      '有什么出路'
    ]
  })

  it('高二 ', async () => {
    messages = [
      '暴叔您好！我现在是高二 成绩也不是很理想，然后我想大学去留学，一般英语要考多少分呀',
      '预算只有15',
      '有什么出路'
    ]
  })

  it('高三 ', async () => {
    messages = [
      '你好',
      '暴叔，我现在高三，还有一个月就要高考了，怎么办啊？',
      '成绩差怎么办啊',
      '具体怎么办啊',
      '考虑啊，去哪里合适',
      '上海',
      '英语也一般',
      '100w',
      '有啥建议么',
      '可以具体讲讲么',
    ]
  })

  it('高三 高考', async () => {
    messages = [
      '暴叔，我刚高考完，感觉考砸了，咋整',
      '感觉国内没学上来，出去好，还是在国内好啊，有没有啥方案',
      '家里预算 30个'
    ]
  })

  it('硕士', async () => {
    messages = [
      '暴叔，我是清华计算机，想申个麻省的博士',
      '我GPA 不高，80多吧，GRE 还没考'
    ]
  })

  it('博士', async () => {
    messages = [
      '暴叔，我是清华博士，想出国看看机会',
    ]
  })

  it('3+1', async () => {
    messages = [
      '暴叔你好。\n我安徽考生理科的 二本线没过\n想走国际本科3+1',
      '我是同学',
      '预算60总共，英语考得一般，100吧',
      '作文失误啦',
      '暴叔3+1不好吗？',
      '3+1和韩国申请学校QS排名那个更好呢',
      '3+1大概是多少钱呀'
    ]
  }, 60000)

  it('预算推荐', async () => {
    messages = [
      '你好暴叔 我是全日制三年专科毕业计算机专业的 然后现在是专升本准大一生 软件工程专业 学校是广东的一个民办本科 想问问我这个背景以后想出国读计算机研究生 有什高的 以后能留在当地it行业工作的出国目的就是想趁年轻出去看看。还有我现在在国内本科的这两年得学好什么准备什么',
      '10w'
    ]

  }, 60000)

  it('保录', async () => {
    messages = [
      '暴叔，咱们这边转学想咨询下'
    ]
  }, 60000)

  it('高三 高考， 无预算', async () => {
    messages = [
      '您好！情况是这样，我们是在新疆，孩子这次高考成绩不理想，估计280分-300分左右，这种情况，出国怎样报合适',
      '就想看看国外有没有什么好学校适合',
      '在抖音上看到暴叔说的有一个浙大复读班，这个是这样的，可以介绍下吗？',
      '多少钱？',
      '复读完是重新高考吗',
      '100分',
      '普通家庭\n就看多少了'
    ]
  })

  it('日语生1', async () => {
    messages = [
      '孩子今年高考完学日语的预估4百分左右',
      '是想问问有啥好专业可选\n或者中外合作办学的',
      '江西\n 赣州',
      '日语生',
      '100上下\n 正常100-110',
      '100w'
    ]
  }, 60000)

  it('日语生2', async () => {
    messages = [
      '真的是暴叔么',
      '爆叔大专学无人机，应用技术怎么样？\n想找你规划一下',
      '想去\n 家里没钱',
      '加拿大',
      '我高中日语',
      '安徽\n关键是我日语成绩也不怎么样',
      '有什么推荐项目么'
    ]
  }, 60000)

  it('1123', async () => {
    messages = [
      '暴叔，成绩出来了\n我g了，怎么办！！！！'
    ]
  }, 60000)

  it('123123123', async () => {
    messages = [
      '有什么问题是直接在这里说就可以了吗？',
      '我想咨询一下，高考的一些问题',
      '是的'
    ]
  }, 60000)

  it('投资人 case 测试', async () => {
    messages = [
      '暴叔',
      '我能问你些啥',
      '我家小孩想去美国学机器人，有什么大学推荐吗',
      '一个高中一年级',
      '湖南人，但以后可能来北京上学',
      '预算200万左右',
      '班里第三',
      '还没考托福，平常英语考试在135分以上',
      '咬咬牙可以提高\n最好还是纯美本',
      '那预算500万呢？两个人',
      '有哪些最好的学校呀？机器人专业\n孩子还是挺优秀的',
      '亲戚家还有个孩子，初二，想学建筑，申请欧洲的学校，您有什么建议'
    ]
  }, 60000)

  it('槽位提取问题', async () => {
    messages = [
      '暴叔晚上好，土木类的专升本学历，现在失业快两个月了，有没有什么好的方向啊？在南昌找不到工作。[流泪]，人太迷茫了，每天都失眠。好想学点什么，但是现在的就业环境真的太难了，学历本来就低，还是转行[苦涩]，想找准个方向死啃。',
      '能嘎嘎挣钱的国家和专业',
      '前期啥成本\n怎么苟着发育',
      '如果能嘎嘎挣钱，先苟着攒点钱冲了',
      '暂时不知道咋发育啊',
      '香港有用吗',
      '才存了不到十万，还没发育好，想香港吧，离家近点，广东的',
      '土木的，英语就那样，四级三百多分，请问您是暴叔本人嘛'
    ]
  }, 60000)


  it('正常推进', async () => {
    messages = [
      '暴叔晚上好，土木类的专升本学历，现在失业快两个月了，有没有什么好的方向啊？在南昌找不到工作。[流泪]，人太迷茫了，每天都失眠。好想学点什么，但是现在的就业环境真的太难了，学历本来就低，还是转行[苦涩]，想找准个方向死啃。',
      '能嘎嘎挣钱的国家和专业',
      '十万吧，还没发育好，想香港吧，离家近点，广东的',
    ]
  }, 60000)


  it('父母支持', async () => {
    messages = [
      '暴叔，我高三，有没有推荐的',
      '家里不太支持',
      '有啥推荐',
      '预算低一点的'
    ]
  }, 60000)

  it('高中申请硕士', async () => {
    messages = [
      '暴叔，我高三，有没有推荐的',
      '家里支持的',
      '我考虑研究生再出去',
      '我想去滑铁卢读研，家里烟台的，预算 100w',
      '我高考 700分，雅思7分',
      '好的，我先了解一下'
    ]
  }, 60000)


  it('正常', async () => {
    messages = [
      '暴叔，我高三，有没有推荐的',
      '家里不太支持',
      '有啥推荐',
      '预算低一点的'
    ]
  }, 60000)


  it('项目选择', async () => {
    messages = [
      '暴叔，我毕业很多年了，有没有推荐的',
      '我想去读个研，烟台的，预算 100w，本科滑铁卢，73均分',
      '去哪个国家好?',
      '美国太贵了'
    ]
  }, 60000)

  it('不问预算', async () => {
    messages = [
      '你好，我现在高二，高考在国内估计拿不到什么好学校，如果走留学的话，有什么推荐吗。',
      '英美澳加，新加坡我觉得都可以看看。有什么推荐吗，暴叔',
      '如果又合适的，家里支持的',
      '福州',
      '80分吧',
      '150分的卷子，我平时也就80分',
      '好的，还需要些什么'
    ]
  }, 60000)

  it('低龄', async () => {
    messages = [
      '小孩小学，有什么方案推荐么？',
      '上海的',
      '不清楚预算',
      '怎么说'
    ]
  }, 60000)

  it('预算不清楚或者无下限', async () => {
    messages = [
      '小孩小学，有什么方案推荐么？',
      '上海的',
      '不清楚预算',
      '怎么说',
      '考虑出去的',
      '有方案推荐么'
    ]
  }, 60000)

  it('自带方案', async () => {
    messages = [
      '我想去美国读大学, 100w 够么',
      '家里支持的，有什么学校推荐，家里上海的',
      '雅思4个8，高考 700分',
      '有方案推荐么',
    ]
  }, 60000)

  it('预算前后不一致', async () => {
    messages = [
      '你好，福建342分，能选什么专业',
      '我是同学',
      '大专的学历太难看了\n看看好有啥路\n没有的规划，成绩出来很差想看看看',
      '可以的。如果可以本科 就出去',
      '最好能9月份',
      '嗯嗯，预算合适的话，支持的',
      '20w',
      '总预算的话，够吗，不够可以加',
      '80分左右的样子',
      '20w总预算可以去韩国吗',
      '50w够吗'
    ]
  }, 60000)

  it('预算前后不一致2', async () => {
    messages = [
      `暴叔 sos
安徽高考410
不想上大侄女
大专`,
      '怎么半',
      '就像读个学历好看的',
      '家里也没想法，原来一直想着高考。预算合适，支持的',
      '按照最低算，叔',
      '最低一般要多少呀',
      `去哪里，能读上教育水平不错的学校呢
英语91`,
      '新加坡太贵了，马来西亚还可以看看。不想学韩语',
      '但是马来认可度国内可能不高呀',
      '我在福州呢',
      `需要什么条件呢
大概成绩要求之类的`,
      '高考成绩需要多少呀',
      '我这个成绩的能的申请啥样的学校'
    ]
  }, 60000)

  it('高一以下', async () => {
    messages = [
      '我目前是高一，然后想规划一下',
      '我说一下我目前的背景和情况\n我的话因为家里父母离异，我爸在美国，有绿卡目前也在给我申\n然后我目前成绩还行',
      '我爸让我自己想[捂脸][捂脸][捂脸]\n我目前也在备考雅思',
      '我的英语还行，中考满分150我是144',
      '但是目前我的问题是就是很迷茫吧\n怎么说呢'
    ]
  }, 60000)

  it('二硕', async () => {
    messages = [
      '我想咨询二硕的事情',
      '是这样个情况，我没有本科，大专毕业，用大厂工作经验申请了英国的硕士，已经念完了，然后当时英国学校的老师看着我对电影和传媒的课程成绩相对比较好，就有建议我再申请一个相关专业的硕士',
      '我的问题是，二硕是继续留在英国读还是申请美国的',
      '我想留在英国'
    ]
  }, 60000)

  it('连线', async () => {
    messages = [
      '暴叔直接在这和你咨询吗',
      '排了半天一直被突然插队[捂脸]我怕等下忘词了\n我想问下  目前国内全日制大专毕业4年左右  我想问下如果目前走国内外联办的学校（国内读1-2年，国外读1-2年，或者越短越好）  能像国内同等学历申硕一样  有这种学校吗？',

    ]
  }, 60000)

  it ('孤星重复输出', async () => {
    messages = [
      '暴叔，我现在准备在上海的一所不错的国际学校里面上ap课程，但是并没有对未来的明确目标，家里也是有点小钱的（3，500w应该是有的）请问能不能帮我规划一下\n[[表情]]',
      '我是学生\n[[表情]]',
      '麻烦暴叔了',
      '我马上是新高一',
    ]
  }, 60000)

  it ('昊昱', async () => {
    await ChatHistoryService.addBotMessage(chat_id, '你好，请说')

    messages = [
      '你好，我儿子今年上海电机学院市场营销本科毕业，雅思目前6.0分，GPA76.5，现在想出国申个硕，香港或者新加坡，想听听建议',
      '50W',
    ]
  }, 60000)
})