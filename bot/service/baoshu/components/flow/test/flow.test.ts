import { BaoshuFlow } from '../flow'
import { UUID } from '../../../../../lib/uuid/uuid'
import { getChatId } from '../../../../../config/chat_id'
import { Config } from '../../../../../config/config'

describe('Test', function () {
  beforeAll(() => {

  })

  it('留学规划测试 -> 不同意进群', async () => {
    Config.setting.localTest = true
    const user_id = UUID.short()
    const chat_id = getChatId(user_id)

    await BaoshuFlow.step(chat_id, user_id, '暴叔，想咨询留学的事情呀')
    await BaoshuFlow.step(chat_id, user_id, '我现在是国内二本大三再读 预算大概100万左右，有什么推荐吗')
    await BaoshuFlow.step(chat_id, user_id, '哦哦，暴叔有什么具体一点的建议么，因为我成绩一般，GPA 只有 70')
    await BaoshuFlow.step(chat_id, user_id, '好的，谢谢暴叔')
    await BaoshuFlow.step(chat_id, user_id, '稍等一下吧')
  }, 1E8)

  it('情绪化问题测试', async () => {
    Config.setting.localTest = true
    const user_id = UUID.short()
    const chat_id = getChatId(user_id)

    await BaoshuFlow.step(chat_id, user_id, '暴叔，我完蛋了，怎么办啊')
    await BaoshuFlow.step(chat_id, user_id, '我没啥具体问题，就是没学上了，求一下安慰')
    await BaoshuFlow.step(chat_id, user_id, '好的，谢谢暴叔')
  }, 1E8)

  it('博士拉群', async () => {
    Config.setting.localTest = true
    const user_id = UUID.short()
    const chat_id = getChatId(user_id)

    await BaoshuFlow.step(chat_id, user_id, '我是今年泰国硕士毕业，在犹豫要不要读博，工作经历是中央电视台，新华网，学习强国，一直在媒体，新闻专业，求暴叔给意见')
    await BaoshuFlow.step(chat_id, user_id, 'ok')

  }, 60000)

  it('留学规划测试 -> 进群', async () => {
    Config.setting.localTest = true
    const user_id = UUID.short()
    const chat_id = getChatId(user_id)

    await BaoshuFlow.step(chat_id, user_id, '暴叔，想咨询留学的事情呀')
    await BaoshuFlow.step(chat_id, user_id, '我现在是国内二本大三再读 预算大概100万左右，有什么推荐吗')
    await BaoshuFlow.step(chat_id, user_id, '哦哦，暴叔有什么具体一点的建议么，因为我成绩一般，GPA 只有 70')
    await BaoshuFlow.step(chat_id, user_id, '好的，谢谢暴叔')
    await BaoshuFlow.step(chat_id, user_id, '可以的')
  }, 1E8)

  it('留学规划测试 -> 不同意进群', async () => {
    Config.setting.localTest = true
    const user_id = UUID.short()
    const chat_id = getChatId(user_id)

    await BaoshuFlow.step(chat_id, user_id, '暴叔，想咨询留学的事情呀')
    await BaoshuFlow.step(chat_id, user_id, '我现在是国内二本大三再读 预算大概100万左右，有什么推荐吗')
    await BaoshuFlow.step(chat_id, user_id, '哦哦，暴叔有什么具体一点的建议么，因为我成绩一般，GPA 只有 70')
    await BaoshuFlow.step(chat_id, user_id, '好的，谢谢暴叔')
    await BaoshuFlow.step(chat_id, user_id, '稍等一下吧')
  }, 1E8)


  it('regex', async () => {
    const s =  '75分有点低，申请名校难度大\n你有考虑哪些国家吗？\n预算大概多少？'
    console.log(/.*[？?么吗啥].*/.test('好的，咱们现在本科几年级？\n'))
  }, 30000)

})