import { IntentionCalculateNode } from '../nodes/intentionCalculate'
import { BaoshuFlow } from '../flow'

const intentionCategory = async (text: string) => {
  return await (IntentionCalculateNode as any).intentionCategorize(text)
}

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    console.log(await intentionCategory('小暴 经常看你视频 很喜欢你'))
  }, 60000)

  it('hongKong', async () => {
    console.log(await BaoshuFlow.isHongKongPromotion('香港那个活动怎么搞？'))
    console.log(await BaoshuFlow.isHongKongPromotion(`暴叔，你那个活动怎么搞
香港的那个`))

    console.log(await BaoshuFlow.isHongKongPromotion('暴叔，你那个活动怎么搞'))
    console.log(await BaoshuFlow.isHongKongPromotion('暴叔，香港怎么搞'))

  }, 60000)
})