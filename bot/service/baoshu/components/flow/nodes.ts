import { BaoshuWorkFlowNode } from './nodes/baseNode'
import { EndNode } from './nodes/end'
import {
  SelfPlanConsultNode,
  SelfPlanNode,
  StudyAbroadPlanningConsultNode,
  StudyPlanRoutingNode
} from './nodes/studyAbroadPlan'
import { BaoshuNode } from './type'
import { IntentionCalculateNode } from './nodes/intentionCalculate'
import { EarlyAgeInvitedNode, EarlyAgeInvitePendingNode } from './nodes/earlyAge'
import { InviteToConsultantNode } from './nodes/inviteToGroup'
import { FillFormNode } from './nodes/fillForm'
import { DoctorConsultNode } from './nodes/doctorConsult'
import { BudgetReConfirmNode } from './nodes/budgetReconfirm'
import { ParentReconfirmNode } from './nodes/parentReconfirm'


export const BaoshuNodeMap =  new Map<BaoshuNode, typeof BaoshuWorkFlowNode>(
  [
    [BaoshuNode.IntentionCalculate, IntentionCalculateNode],
    [BaoshuNode.EarlyAgeInvitePending, EarlyAgeInvitePendingNode],
    [BaoshuNode.EarlyAgeInvited, EarlyAgeInvitedNode],
    [BaoshuNode.StudyAbroadPlanningRoute, StudyPlanRoutingNode],
    [BaoshuNode.SelfPlan, SelfPlanNode],
    [BaoshuNode.SelfPlanConsult, SelfPlanConsultNode],
    [BaoshuNode.StudyAbroadPlanningConsult, StudyAbroadPlanningConsultNode],
    [BaoshuNode.InviteConsultant, InviteToConsultantNode],
    [BaoshuNode.FillFormNode, FillFormNode],
    [BaoshuNode.DoctorConsult, DoctorConsultNode],
    [BaoshuNode.BudgetReConfirm, BudgetReConfirmNode],
    [BaoshuNode.ParentSupportReconfirm, ParentReconfirmNode],
    [BaoshuNode.END, EndNode],
  ]
)