import { ChatStateStore, ConversationState } from '../../storage/chat_state_store'

import { ChatHistoryService } from '../chat_history'
import { BaoshuNode } from './type'
import { BaoshuNodeMap } from './nodes'
import { HashMessagesHandler } from '../interrupt_handler'
import { UUID } from '../../../../lib/uuid/uuid'
import logger from '../../../../model/logger/logger'
import { SilentReAsk } from '../silent_requestion'
import { IntentionCalculateNode } from './nodes/intentionCalculate'
import { WechatMessageSender } from '../message_send'
import { HumanTransfer, HumanTransferType } from '../human_transfer'
import { ChatDB } from '../../database/chat'
import { sleep } from '../../../../lib/schedule/schedule'
import { IWecomMsgType } from '../../../../lib/juzi/type'
import { LLMXMLHelper } from './nodes/helper/xmlHelper'

export interface IWorkflowState {
  chat_id: string
  user_id: string
  userMessage: string

  interruptHandler: HashMessagesHandler // 用于辅助打断当前对话，同时只执行一个回复，避免重复
  round_id: string // 用来区分每轮对话的 id
}

export class BaoshuFlow {
  /**
   * 对话流程
   * @param chat_id
   * @param user_id
   * @param userMessage
   */
  public static async step(chat_id: string, user_id: string, userMessage: string) {
    await ChatHistoryService.addUserMessage(chat_id, userMessage)

    const entryNode = ChatStateStore.get(chat_id).nextStage
    const interruptHandler = new HashMessagesHandler(await ChatHistoryService.getChatHistory(chat_id))

    // 获取节点并执行
    await BaoshuFlow.run(entryNode as BaoshuNode, {
      chat_id,
      user_id,
      userMessage,
      interruptHandler: interruptHandler,
      round_id: UUID.v4()
    })
  }

  private static async run(entryNode: BaoshuNode, state: IWorkflowState) {
    const node = BaoshuNodeMap.get(entryNode)
    if (!node) {
      logger.error(`[BaoshuFlow] node not found: ${entryNode}`)
      return
    }

    if (await this.robotDetection(state)) {
      return
    }

    const nextStage =  await node.invoke(state)
    ChatStateStore.update(state.chat_id, { nextStage })


    if (nextStage === BaoshuNode.IntentionCalculate) {
      const chatState = ChatStateStore.get(state.chat_id)
      const userSlots =  chatState.userSlots
      const chatStatus = chatState.state
      if (userSlots && chatStatus !== ConversationState.AskedParentSupport) {
        // 追问
        await SilentReAsk.schedule(state.chat_id, async () => {
          // 已经转人工了，不再进行追问
          if (await ChatDB.isHumanInvolvement(state.chat_id)) {
            return
          }

          await IntentionCalculateNode.invoke({
            user_id: state.user_id,
            chat_id: state.chat_id,
            userMessage: '',
            interruptHandler: state.interruptHandler,
            round_id: UUID.v4()
          })
        }, 10 * 60, false)
      }
    }
  }


  private static async robotDetection(state:IWorkflowState) {
    const userMessage = state.userMessage
    const robotDetectionNodeName = 'RobotDetected'
    // 当用户聊天记录中出现两次转人工，机器人，ai时，转人工
    if (userMessage.includes('转人工') || userMessage.includes('机器人') || userMessage.toLowerCase().includes('ai')) {
      ChatStateStore.increNodeCount(state.chat_id, robotDetectionNodeName)
      const robotDetectedNum = ChatStateStore.getNodeCount(state.chat_id, robotDetectionNodeName)
      console.log('robotDetectedNum', robotDetectedNum)
      if (robotDetectedNum >= 2) {
        try {
          await HumanTransfer.transfer(state.user_id, state.chat_id, HumanTransferType.RobotDetected)
          return true
        } catch (error) {
          logger.error('用户识别到AI，但转人工失败:', error)
        }
        ChatStateStore.update(state.chat_id, { nodeInvokeCount: { [robotDetectionNodeName]: 0 } })
      }
    }

    return false
  }


  private static async redPacketActivity(state: IWorkflowState) {
    try {
      const redPacketKeyWord = '红包'
      if (!state.userMessage.includes(redPacketKeyWord)) {
        return false
      }

      const now = new Date()

      const currentDate = now.getDate()
      const currentHour = now.getHours()

      const isSendRedPacket = ChatStateStore.getStatus(state.chat_id).is_send_red_packet

      // // 非17号
      // if (currentDate < 17) {
      //   const isRepeated = await ChatHistoryService.isRepeatedMsg(
      //     state.chat_id,
      //     '暴叔专属红包封面活动，17号晚暴叔新年答谢会惊喜上线，敬请期待！'
      //   )
      //   if (isRepeated) return false
      //
      //   await WechatMessageSender.sendById({
      //     user_id: state.user_id,
      //     chat_id: state.chat_id,
      //     ai_msg: '暴叔专属红包封面活动，17号晚暴叔新年答谢会惊喜上线，敬请期待！'
      //   })
      //   return true
      // }
      //
      // // 18点前
      // if (currentDate > 17 || currentHour < 18) {
      //   const isRepeated = await ChatHistoryService.isRepeatedMsg(
      //     state.chat_id,
      //     '红包活动还没开始哦，请在今天晚上18:00-22:00期间再来领取~ 发送【红包】关键词，即可领取'
      //   )
      //   if (isRepeated) return false
      //
      //   await WechatMessageSender.sendById({
      //     user_id: state.user_id,
      //     chat_id: state.chat_id,
      //     ai_msg: '红包活动还没开始哦，请在今天晚上18:00-22:00期间再来领取~ 发送【红包】关键词，即可领取'
      //   })
      //   return true
      // }
      //
      // // 22点后
      // if (currentHour >= 22) {
      //   const isRepeated = await ChatHistoryService.isRepeatedMsg(
      //     state.chat_id,
      //     '活动已经结束啦，谢谢大家的支持！欢迎大家关注暴叔后续活动！'
      //   )
      //   if (isRepeated) return false
      //
      //   await WechatMessageSender.sendById({
      //     user_id: state.user_id,
      //     chat_id: state.chat_id,
      //     ai_msg: '活动已经结束啦，谢谢大家的支持！欢迎大家关注暴叔后续活动！'
      //   })
      //   return true
      // }

      // 18-22点间的红包发送
      if (!isSendRedPacket) {
        await WechatMessageSender.sendById({
          user_id: state.user_id,
          chat_id: state.chat_id,
          ai_msg: '[答谢直播，红包封面二维码，扫码领取]',
          send_msg: {
            type: IWecomMsgType.Image,
            url: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/baoshu/24851736855158_.pic_hd.jpg'
          }
        })

        await sleep(3000)

        await WechatMessageSender.sendById({
          user_id: state.user_id,
          chat_id: state.chat_id,
          ai_msg: '数量有限，先到先得哦！'
        })

        ChatStateStore.update(state.chat_id, {
          status: {
            is_send_red_packet: true
          }
        })
        return true
      }

      return false
    } catch (error) {
      logger.error({ chat_id: state.chat_id }, '红包活动处理错误:', error)
      return false
    }
  }

  private static async hongKongPromotion(state: IWorkflowState) {
    const keyWords = ['香港线下见面会', '香港教育展', '门票', '香港门票', '香港活动', '线下见面会', '见面会', '教育展', '展位']
    const isSendPromotionPoster = ChatStateStore.getStatus(state.chat_id).is_send_promotion_poster

    async function sendHongKongPromotionPoster() {
      ChatStateStore.update(state.chat_id, {
        status: {
          is_send_promotion_poster: true
        }
      })

      await sleep(5 * 1000)

      await WechatMessageSender.sendById({
        user_id: state.user_id,
        chat_id: state.chat_id,
        ai_msg: '[香港线下见面会，二维码海报]',
        send_msg: {
          type: IWecomMsgType.Image,
          url: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/baoshu/%E9%A6%99%E6%B8%AF%E9%97%A8%E7%A5%A8.jpg'
        }
      })

      await sleep(5 * 1000)

      await WechatMessageSender.sendById({
        user_id: state.user_id,
        chat_id: state.chat_id,
        ai_msg: '微信或者支付宝扫描海报上的二维码，领取免费门票。扫描后按照页面显示的信息填写和选择就可以了'
      })
    }

    for (const word of keyWords) {
      if (state.userMessage.includes(word) && !isSendPromotionPoster) {
        await sendHongKongPromotionPoster()
        return true
      }
    }

    const chatHistory = await ChatHistoryService.getRecentConversations(state.chat_id, 3)
    const formatChatHistory = ChatHistoryService.formatHistoryHelper(chatHistory.filter((item) => item.role === 'user'))

    // 模型判断下，客户是否在提及香港活动
    if (formatChatHistory.includes('香港') && !isSendPromotionPoster && await this.isHongKongPromotion(formatChatHistory, { chat_id: state.chat_id, user_id: state.user_id, round_id: state.round_id })) {
      await sendHongKongPromotionPoster()
      return true
    }

    return false
  }

  public static async isHongKongPromotion(formatChatHistory: string, logInfo?: {chat_id: string; round_id: string; user_id: string}) {
    const prompt = `你需要根据以下客户的发言判断客户是否想要参加香港的线下活动。

规则说明：
1. 如果客户有询问香港的（线下）活动（如“我想去香港参加这个活动”、“香港的活动怎么参加”），则回答 "YES"。
2. 如果客户明确表示不想参加（如“我不想去香港的活动”、“我对现场不感兴趣”），则回答 "NO"。
3. 如果无法从对话中确定客户是否有参加意愿（对话中没有提及香港线下活动的意愿或模棱两可），则回答 "null"。

客户的发言如下：
<chat_log>
${formatChatHistory}
</chat_log>

请根据上述规则给出结论。输出格式如下：

[分析说明]
<answer>
YES/NO/null
</answer>`

    return await LLMXMLHelper.extractBooleanAnswer(prompt, { trueFlag: 'yes', falseFlag: 'no', model: 'gpt-4o-mini' }, logInfo)
  }
}