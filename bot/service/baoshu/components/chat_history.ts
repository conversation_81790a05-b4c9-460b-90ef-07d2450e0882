import { Config } from '../../../config/config'

import { HumanMessage, AIMessage as BotMessage } from '@langchain/core/messages'
import chalk from 'chalk'
import { PrismaMongoClient } from '../../../model/mongodb/prisma'
import logger from '../../../model/logger/logger'

export interface IDBBaseMessage extends IBaseMessage {
    id: string
    created_at: Date
    chat_id: string
}

export interface IBaseMessage {
    role: 'user' | 'assistant'
    content: string
}

export class BaseMessage {
  private _role: 'user' | 'assistant'
  private _content: string
  constructor(role: 'user' | 'assistant', content: string) {
    this._role = role
    this._content = content
  }
  get role() {
    return this._role
  }

  get content() {
    return this._content
  }
}

// 这里 Mock 一下 UserMessage 和 AIMessage 的类
export class UserMessage extends BaseMessage {
  constructor(content: string) {
    super('user', content)
  }

}

export class AIMessage extends BaseMessage {
  constructor(content: string) {
    super('assistant', content)
  }
}

export class ChatHistoryService {
  static archiveFlag = '[archived]'

  public static async getChatHistory(chat_id: string) {
    try {
      let chatHistory = await PrismaMongoClient.getInstance().chat_history.findMany({
        where: {
          chat_id: chat_id
        },
        orderBy: {
          created_at: 'asc'
        },
      }) as IDBBaseMessage[]

      // 如果有 archive flag，取 archive flag 往后的记录
      let latestArchiveIndex = -1

      for (let i = chatHistory.length - 1; i >= 0; i--) {
        if (chatHistory[i].role === 'assistant' && chatHistory[i].content === ChatHistoryService.archiveFlag) {
          latestArchiveIndex = i
          break
        }
      }

      if (latestArchiveIndex !== -1) {
        chatHistory = chatHistory.slice(latestArchiveIndex + 1)
      }

      return chatHistory
    } catch (error) {
      console.error('Error fetching chat records:', error)
      return []
    }
  }

  public static async getLLMChatHistory(chat_id: string) {
    const chatHistory = await this.getChatHistory(chat_id)
    return chatHistory.map((message) => {
      if (message.role === 'user') {
        return new HumanMessage(message.content)
      } else {
        return new BotMessage(message.content)
      }
    })
  }



  public static async clearChatHistory(chat_id: string, archive = true) {
    try {
      // 假删除
      if (archive) {
        await this.addBotMessage(chat_id, ChatHistoryService.archiveFlag)
      } else {
        const result = await PrismaMongoClient.getInstance().chat_history.deleteMany({
          where: {
            chat_id
          },
        })
        logger.debug(chat_id, `${result.count} chat records with chatId '${chat_id}' have been cleared.`)
        return result.count
      }
    } catch (error) {
      console.error('Error clearing chat records:', error)
    }
  }


  public static async addBotMessage(chat_id: string, message: string) {
    if (message) {
      await PrismaMongoClient.getInstance().chat_history.create({
        data: {
          chat_id: chat_id,
          role: 'assistant',
          content: message,
          created_at: new Date()
        }
      })
    }

    if (message !== ChatHistoryService.archiveFlag) {
      logger.log({ chat_id }, chalk.greenBright(`${Config.setting.BOT_NAME}: ${message}`))
    }
  }

  public static async addUserMessage(chat_id: string, message: string) {
    await PrismaMongoClient.getInstance().chat_history.create({
      data: {
        chat_id: chat_id,
        role: 'user',
        content: message,
        created_at: new Date()
      }
    })
    logger.log({ chat_id }, chalk.blueBright(`用户: ${message}`))
  }

  public static async formatHistory(chat_id: string) {
    const chatHistory = await this.getChatHistory(chat_id)
    const formattedHistory = chatHistory.map((message) => {
      return `${message.role === 'user' ? '客户' : Config.setting.BOT_NAME}: ${message.content}`
    })

    return formattedHistory.join('\n')
  }

  public static async formatHistoryOnRole(chat_id: string, role: 'user' | 'assistant') {
    let chatHistory = await this.getChatHistory(chat_id)
    chatHistory = chatHistory.filter((message) => message.role === role)

    const formattedHistory = chatHistory.map((message) => {
      return `${message.role === 'user' ? '客户' : Config.setting.BOT_NAME}: ${message.content}`
    })

    return formattedHistory.join('\n')
  }

  public static formatHistoryHelper(messages: IDBBaseMessage[]) {
    return messages.map((message) => {
      return `${message.role === 'user' ? '客户' : Config.setting.BOT_NAME}: ${message.content}`
    }).join('\n')
  }


  // TODO 使用 Token 裁剪，并使用刚好的裁剪算法
  private static truncateHistory(history: string, tokens = 6000) {
    return history.slice(-tokens)
  }

  static async repeatLastMessage(chat_id: string, s: string): Promise<boolean> {
    const chatHistory = await this.getChatHistory(chat_id)
    if (chatHistory.length === 0) {
      return false
    }

    const lastMessage = chatHistory[chatHistory.length - 1]
    return lastMessage.content === s
  }

  static async setChatHistory(chat_id: string, chatHistory: IBaseMessage[] | BaseMessage[]) {
    await this.clearChatHistory(chat_id)

    await PrismaMongoClient.getInstance().chat_history.createMany({
      data: chatHistory.map((message: IBaseMessage | BaseMessage) => {
        return {
          chat_id: chat_id,
          role: message.role,
          content: message.content,
          created_at: new Date()
        }
      })
    })
  }

  static async getUserMessageCount(chat_id: string): Promise<number> {
    const chatHistory = await this.getChatHistory(chat_id)

    return chatHistory.filter((message) => message.role === 'user').length
  }

  static async getLastRoundHistory(chat_id: string) {
    const chatHistory = await this.getChatHistory(chat_id)
    let res: IDBBaseMessage[] = []

    // 倒序获取一轮对话
    for (let i = chatHistory.length - 1; i >= 0; i--) {
      if (chatHistory[i].role === 'user') {
        res = chatHistory.slice(i, chatHistory.length)
        break
      }
    }

    return res
  }

  public static async getRecentConversations (chat_id: string, rounds: number): Promise<IDBBaseMessage []> {
    const chatHistory = await this.getChatHistory (chat_id)
    let res: IDBBaseMessage [] = []
    let roundCount = 0

    // 从后向前遍历
    for (let i = chatHistory.length - 1; i >= 0; i--) {
      // 如果是用户消息，增加轮次计数
      if (chatHistory [i].role === 'user') {
        roundCount++

        // 如果达到指定轮次数，结束遍历
        if (roundCount === rounds) {
          res = chatHistory.slice (i)
          break
        }
      }

      // 如果遍历到开头仍未达到指定轮次，返回全部历史
      if (i === 0) {
        res = chatHistory.slice ()
      }
    }

    return res
  }

  static async getLastAIMessage(chat_id: string) {
    const chatHistory = await this.getChatHistory(chat_id)

    for (let i = chatHistory.length - 1; i >= 0; i--) {
      if (chatHistory[i].role === 'assistant') {
        return chatHistory[i].content
      }
    }
    return ''
  }

  static async countRemainingMsgAfterMsg(chat_id: string, message: string) {
    const chatHistory = await this.getChatHistory(chat_id)
    const msgIndex = chatHistory.findIndex((msg) => msg.content === message)

    if (msgIndex === -1) {
      return -1
    }

    return chatHistory.length - msgIndex - 1
  }

  static async getLastMessage(chat_id: string) {
    const chatHistory = await this.getChatHistory(chat_id)

    return chatHistory[chatHistory.length - 1]
  }

  static async getUserMessages(chat_id: string) {
    const chatHistory = await this.getChatHistory(chat_id)

    return chatHistory.filter((message) => message.role === 'user').map((message) => message.content).join('\n')
  }

  static async isRepeatedMsg(chat_id: string, message: string) {
    const chatHistory = await this.formatHistoryOnRole(chat_id, 'assistant')

    // 分句检查是否重复
    // Split the sentence into clauses based on punctuation marks like period, comma, semicolon, etc.
    const clauses = message.split(/[,;!?，。；！？]/).map((clause) => clause.trim()).filter((clause) => clause.length > 6)

    // Check if each clause is contained within the search string
    return clauses.some((clause) => chatHistory.includes(clause)) || (chatHistory.includes(message) && message.length >= 7) || (message === '你好，请说' && chatHistory.includes('你好，请说'))
  }

  static async getLastUserMessages(chat_id: string) {
    const chatHistory = await this.getChatHistory(chat_id)

    // 初始化一个空数组来保存用户的聊天记录
    const userMessages: IDBBaseMessage[] = []

    // 倒序遍历聊天记录
    for (let i = chatHistory.length - 1; i >= 0; i--) {
      const message = chatHistory[i]

      // 如果遇到第一个 assistant 的记录，停止遍历
      if (message.role === 'assistant') {
        break
      }

      // 如果是用户的消息，加入到 userMessages 数组中
      if (message.role === 'user') {
        userMessages.push(message)
      }
    }

    // 返回用户的聊天记录列表
    return userMessages.reverse() // reverse() 确保返回的消息顺序是从旧到新
  }

  static async deleteByIds(ids: string[]) {
    await PrismaMongoClient.getInstance().chat_history.deleteMany({
      where: {
        id: {
          in: ids, // 使用 `in` 操作符匹配多个 ID
        },
      },
    })
  }
}