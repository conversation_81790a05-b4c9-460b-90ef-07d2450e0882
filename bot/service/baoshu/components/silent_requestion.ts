import { DelayedTask } from '../../../lib/schedule/delayed_task'
import { HashMessagesHandler } from './interrupt_handler'
import { ChatHistoryService } from './chat_history'

/**
 * 追问
 * 注意：
 * 1. 每个对话都有独立的追问计时
 * 2. 每次添加新的追问，都要清楚之前的追问任务，同时只能有一个追问任务在执行
 */
export class SilentReAsk {
  private static taskMap: Map<string, DelayedTask> = new Map<string, DelayedTask>()

  /**
   * 添加追问任务
   * @param chat_id
   * @param task
   * @param waiting_time
   * @param auto_retry 被新发送的用户消息打断后，是否进行自动重试，true 的话，直到没有新消息，后续会再次重试执行。如果为 false, 被打断后不再执行
   */
  public static async schedule(chat_id: string, task: () => Promise<any>, waiting_time: number, auto_retry: boolean = false) {
    if (this.taskMap.has(chat_id)) {
      this.taskMap.get(chat_id)?.cancel() // 同一个 chat_id 下只能有一个延时任务
    }

    const newMessageChecker = new HashMessagesHandler(await ChatHistoryService.getChatHistory(chat_id))

    const checkCondition = async () => {
      return !(await newMessageChecker.hasNewUserMessage(chat_id))
    }

    const delayedTask = new DelayedTask(waiting_time, task, checkCondition)

    delayedTask.start()

    this.taskMap.set(chat_id, delayedTask)
  }
}