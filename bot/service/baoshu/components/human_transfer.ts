import { ChatDB } from '../database/chat'

import { GroupNotification } from '../notification/group'

import { ObjectUtil } from '../../../lib/object'

import { JuziAPI } from '../../../lib/juzi/api'
import { Config } from '../../../config/config'
import { ChatStateStore } from '../storage/chat_state_store'
import { EventTracker } from '../../../model/logger/data_driven'
import logger from '../../../model/logger/logger'

interface UpdateAliasParams {
  userId: string
  alias: string
  leading?: boolean
  replace?: RegExp // 用于替换原有备注
}

export enum HumanTransferType {
    UnknownMessageType = 1,
    ProcessImage = 2,
    JoinedGroup = 3,
    FailedToJoinGroup = 4,
    MessageSendFailed = 6,
    DoctorCounselor = 7,
    SchoolTransferInquiry = 8,
    End = 9,
    QueryGroup = 10,
    NonStudyAbroad  = 11,
    CareerPlan = 12,
    RobotDetected = 13,
    FindCounselor =  14,
}

export enum WecomTag {
    AIOn = 'AI on',
    AIOff = 'AI off',
    JoinedGroup = '进群',
}

export class HumanTransfer {
  public static tagsMap = new Map<string, string>() // tagName => tagId

  /**
     * 转交人工，toBot为true时，表示转交机器人
     * @param chatId
     * @param contactId
     * @param type
     * @param toBot
     */
  public static async transfer(chatId: string, contactId: string, type: HumanTransferType, toBot = false) {
    EventTracker.track(chatId, '转交人工', { reason: ObjectUtil.enumValueToKey(HumanTransferType, type) })

    let tag = toBot ? WecomTag.AIOn : WecomTag.AIOff
    if (type == HumanTransferType.JoinedGroup) {
      tag = WecomTag.JoinedGroup
    }

    try {
      await this.updateTags(contactId, tag)
    } catch (e) {
      logger.error('更新标签失败', e)
    }

    if (await ChatDB.getById(chatId)) {
      await ChatDB.setHumanInvolvement(chatId, !toBot)
    } else {
      const currentSender = await JuziAPI.getCustomerInfo(Config.setting.wechatConfig?.id as string, contactId)
      await ChatDB.create({
        id: chatId,
        round_ids: [],
        contact: {
          wx_id: contactId,
          wx_name: currentSender ? currentSender.name : contactId,
        },
        wx_id: Config.setting.wechatConfig?.id as string,
        created_at: new Date(),
        chat_state: ChatStateStore.get(chatId)
      })
    }

    // 包装通知
    const contact = await JuziAPI.getCustomerInfo(Config.setting.wechatConfig?.id as string, contactId)
    let contactName = contactId
    if (!contact) {
      throw new Error('联系人ID不存在')
    }
    contactName = contact.name

    if (type === HumanTransferType.End) { // 不通知
      logger.trace({ chatId }, '结束对话')
      return
    }

    // 通知类型
    const notificationMessages = {
      [HumanTransferType.UnknownMessageType]: '客户发了一个文件，无法处理，请及时介入处理',
      [HumanTransferType.ProcessImage]: '客户发了一张【图片】，请人工观察',
      [HumanTransferType.SchoolTransferInquiry]: '转学问题，请人工处理',
      [HumanTransferType.JoinedGroup]: '已加入群聊',
      [HumanTransferType.FailedToJoinGroup]: '邀请加入群聊失败，请及时介入处理',
      [HumanTransferType.MessageSendFailed]: 'Bot 消息发送失败，请及时介入处理',
      [HumanTransferType.DoctorCounselor] : '博士项目，已发送付费咨询',
      [HumanTransferType.QueryGroup]: '用户想主动加群，请及时介入处理',
      [HumanTransferType.NonStudyAbroad]: '非留学或非国内身份，请及时介入处理',
      [HumanTransferType.CareerPlan]: '已发送职业规划',
      [HumanTransferType.RobotDetected]: '用户识别到AI，转人工处理',
      [HumanTransferType.FindCounselor]: '客户想要对接特定老师，请及时介入处理'
    }
    let notificationMessage = notificationMessages[type]
    notificationMessage =  `${contactName} ${notificationMessage}`


    logger.log(chatId, '通知人工接入：', notificationMessage)

    if ([HumanTransferType.FindCounselor, HumanTransferType.QueryGroup, HumanTransferType.UnknownMessageType, HumanTransferType.SchoolTransferInquiry, HumanTransferType.NonStudyAbroad].includes(type)) {
      await GroupNotification.notify(notificationMessage) // 顾问群
    } else {
      // 发送通知
      await GroupNotification.notify(notificationMessage, 'R:10815661152480532')  // 问题通知群
    }
  }


  public static async updateTags(wechatId: string, tag: WecomTag) {
    try {
      const userId = await JuziAPI.wxIdToExternalUserId(wechatId)
      if (!userId) {
        logger.error('获取 ExternalUserId 失败')
        return
      }

      // AI on 或者 AI off 只能存在一个
      if (this.tagsMap.size === 0) {
        // 拉取标签，更新上去
        const tagsGroups = await JuziAPI.getTags()
        if (tagsGroups) {
          tagsGroups.forEach((group) => {
            group.tags.forEach((tag) => {
              this.tagsMap.set(tag.name, tag.tagId)
            })
          })
        }
      }

      if (!this.tagsMap.has(tag)) {
        throw new Error(`找不到对应的标签：${tag}`)
      }

      const tagId = this.tagsMap.get(tag) as string
      if (tag === 'AI on') {
        const offId = this.tagsMap.get('AI off') as string
        await JuziAPI.updateUserTags(userId, Config.setting.wechatConfig?.botUserId as string, [tagId], [offId])
      } else if (tag === 'AI off') {
        const onId = this.tagsMap.get('AI on') as string
        await JuziAPI.updateUserTags(userId, Config.setting.wechatConfig?.botUserId as string, [tagId], [onId])
      } else if (tag === '进群') {
        await JuziAPI.updateUserTags(userId, Config.setting.wechatConfig?.botUserId as string, [tagId], [])
      }
    } catch (e) {
      logger.warn('更新客户标签失败', e)
    }
  }
}