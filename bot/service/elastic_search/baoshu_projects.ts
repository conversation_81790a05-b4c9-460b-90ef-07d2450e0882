import { ObjectUtil } from '../../lib/object'
import { IExtractUserSlotsResult, IStoredUserSlots } from '../../service/baoshu/components/search/search_system'
import logger from '../../model/logger/logger'
import { UserEducationGroup } from '../../service/baoshu/components/flow/nodes/intentionCalculate'
import ElasticSearchService from '../../model/elastic_search/elastic_search'

export interface IProject {
  educationStage: string
  isStudyAbroad: string
  budgetLowerBound: string
  budgetUpperBound: string
  annualBudgetLowerBound: string
  annualBudgetUpperBound: string
  projectName: string
  projectDescription: string
  projectAdvantages: string
  userStory: string
  academicYears: string
  isJapaneseStudent: boolean
}

export interface IElasticProjectSearchRes {
  _index: string
  _type: string
  _id: string
  _score: number
  _source: IProject
}


/**
 * 暴叔项目反向索引表
 */
export class BaoshuProjectSearch {
  public static async search(query: any) {
    // 校验 esJSON 格式
    const preEsQuery = this.getPreQuery(query)

    if (!preEsQuery) {
      throw new Error('模型返回格式校验失败')
    }

    if (ObjectUtil.isEmptyObject(preEsQuery)) {
      // 空查询
      return {
        projects: [],
        userSlots: {}
      }
    }

    const esQuery = this.constructESQuery(preEsQuery)
    logger.debug('esQuery:', JSON.stringify(esQuery, null, 4))

    // 返回文档内容
    return {
      projects: await ElasticSearchService.search('projects_index', esQuery) as IElasticProjectSearchRes[],
      userSlots: preEsQuery as IStoredUserSlots
    }
  }

  private static getPreQuery(esObject: Record<string, any>) {
    // 预处理对象
    // 高中以后阶段， isStudyAbroad later 改为 true
    const preQuery = {}
    const educationGroups = ['初中', '高中', '本科', '大专']

    if (esObject.education_group) {
      preQuery['education_group'] = esObject.education_group

      if (esObject.education_group === UserEducationGroup.JapanHighSchool || esObject.education_group === UserEducationGroup.Gaokao) {
        preQuery['educationStage'] = '高中'
      } else if (esObject.education_group < 1 || esObject.education_group > 4) {
        preQuery['educationStage'] = '本科' // 超出范围，默认返回为 本科年级
        logger.warn(`education_group 超出范围${ esObject.education_group}`)
      } else if (esObject.education_group === UserEducationGroup.College) {
        preQuery['educationStage'] = '大专'
      } else {
        preQuery['educationStage'] = educationGroups[esObject.education_group - 1]
      }
    }

    if (esObject.application_stage && esObject.education_group !== UserEducationGroup.College) {
      // 如果申请年级是跨年级的，初中考虑大学，高中考虑硕士，最后的 educationGroups 应该是 申请阶段降一档
      const applicationStages = [ '高中', '大专', '本科', '硕士']
      if (esObject.application_stage && applicationStages.includes(esObject.application_stage)) {
        const applicationStageIndex = {
          '高中': UserEducationGroup.HighSchool,
          '本科': UserEducationGroup.University,
          '硕士': UserEducationGroup.Master
        }

        const downGradeMap = {
          '高中': '初中',
          '本科': '高中',
          '硕士': '本科',
          '大专': '大专'
        }

        // 注意 application_stage 不一定准确，必须 > 当前教育阶段才算有效
        if (applicationStageIndex[esObject.application_stage] > esObject.education_group || esObject.application_stage === '硕士') {
          preQuery['educationStage'] = downGradeMap[esObject.application_stage]
        }
      }
    }

    if (!esObject.budget) {
      preQuery['budget'] = 9999
    } else {
      if (Array.isArray(esObject.budget)) {
        preQuery['budget'] = Math.max(...esObject.budget)
      } else {
        preQuery['budget'] = esObject.budget
      }

      preQuery['budget_is_per_year'] = esObject.budget_is_per_year
    }

    if (!esObject.is_study_abroad) {
      preQuery['isStudyAbroad'] = '可以出国'
    } else {
      if (esObject.is_study_abroad === 'true') {
        preQuery['isStudyAbroad'] = '可以出国'
      } else if (esObject.is_study_abroad === 'false') {
        preQuery['isStudyAbroad'] = '不要出国'
      } else if (esObject.is_study_abroad === 'later') {
        if (esObject.education_group <= 2) {
          preQuery['isStudyAbroad'] = '缓缓再出国'
        } else { // 本科以上基本上都是读研的了
          preQuery['isStudyAbroad'] = '可以出国'
        }
      } else {
        throw new Error(`is_study_abroad 超出范围: ${esObject.is_study_abroad}`)
      }
    }

    if (esObject.is_japanese_student) {
      preQuery['isJapaneseStudent'] = true
    } else {
      preQuery['isJapaneseStudent'] = false
    }

    return preQuery
  }

  private static constructESQuery(preEsQuery: Record<string, any>) {
    const query: Record<string, any> = {
      bool: {
        must: [],
        should: []
      }
    }

    // 查询的时候只使用 budget, 教育阶段, 什么时候出国 字段进行匹配

    // 添加预算下限的范围查询（如果存在）
    if (preEsQuery.budget) {
      if (preEsQuery.budget_is_per_year || preEsQuery.education_group === UserEducationGroup.College) {
        query.bool.must.push ({
          range: {
            annualBudgetLowerBound: {
              lte: preEsQuery.budget
            }
          }
        })
      } else {
        query.bool.must.push ({
          range: {
            budgetLowerBound: {
              lte: preEsQuery.budget
            }
          }
        })
      }
    }

    if (preEsQuery.educationStage) {
      query.bool.must.push ({
        match: {
          educationStage: preEsQuery.educationStage
        }
      })
    }

    if (preEsQuery.isJapaneseStudent) {
      query.bool.must.push ({
        match: {
          isJapaneseStudent: true
        }
      })
    } else {
      query.bool.must.push ({
        match: {
          isJapaneseStudent: false
        }
      })
    }

    if (preEsQuery.educationStage === '高中') {
      // 高中阶段，直接全放开查询
    } else if (preEsQuery.isStudyAbroad) {
      if (preEsQuery.isStudyAbroad === '缓缓再出国') {
        query.bool.should.push({
          match: {
            isStudyAbroad: '缓缓再出国'
          }
        })
        query.bool.should.push({
          match: {
            isStudyAbroad: '不要出国'
          }
        })

        query.bool.minimum_should_match = 1
      } else {
        query.bool.must.push ({
          match: {
            isStudyAbroad: preEsQuery.isStudyAbroad
          }
        })
      }
    }

    return query
  }
}