import { BaoshuProjectSearch } from './baoshu_projects'
import { OpenAIClient } from '../../lib/ai/llm/client'
import { IntentionCalculateNode, UserEducationGroup } from '../../service/baoshu/components/flow/nodes/intentionCalculate'
import { BaoshuUserInfoSuggestionSearch } from './baoshu_user_info'
import { LLMNode } from '../../service/baoshu/components/flow/nodes/llm'
import { Config } from '../../config/config'
import { BaoshuBudgetSearch } from './baoshu_budget'

describe('Test', function () {
  beforeAll(() => {

  })

  it('项目预算确认', async () => {
    console.log(JSON.stringify(await BaoshuBudgetSearch.isBudgetEnough({
      education_group: UserEducationGroup.College,
      projectNames: ['韩国'],
      budget: 100,
      // budget_is_per_year: true
    }), null, 4))
  }, 60000)

  it('构建 Query', async () => {
    console.log(JSON.stringify(await (BaoshuProjectSearch as any).constructESQuery({
      budget: 1000,
      educationStage: '高中',
      isStudyAbroad: '缓缓再出国'
    }), null, 4))
  }, 60000)


})