import { IExtractUserSlotsResult, IStoredUserSlots } from '../../service/baoshu/components/search/search_system'
import { ObjectUtil } from '../../lib/object'
import logger from '../../model/logger/logger'

import { z } from 'zod'
import ElasticSearchService from '../../model/elastic_search/elastic_search'



export interface IElasticSuggestionSearchRes {
    _index: string
    _type: string
    _id: string
    _score: number
    _source: {
        user_summary: string
        consult_advice: string
    }
}

export class BaoshuUserInfoSuggestionSearch {
  public static async search(query: IExtractUserSlotsResult) {
    // 校验 esJSON 格式
    const preEsQuery = this.getPreQuery(query)

    if (!preEsQuery) {
      throw new Error('模型返回格式校验失败')
    }

    if (ObjectUtil.isEmptyObject(preEsQuery)) {
      // 空查询
      return {
        suggestions: [],
        userSlots: {}
      }
    }

    const esQuery = this.constructESQuery(preEsQuery)
    logger.debug('esQuery:', JSON.stringify(esQuery, null, 4))

    // 返回文档内容
    return {
      suggestions: (await ElasticSearchService.search('user_info_index', esQuery) as IElasticSuggestionSearchRes[]).map((res) => { return { user_summary: res._source.user_summary, consult_advice: res._source.consult_advice } }),
      userSlots: preEsQuery as IStoredUserSlots
    }
  }


  private static getPreQuery(esObject: object) {
    // 预处理对象
    for (const key in esObject) {
      if (key === 'budget') {
        if(typeof esObject[key] === 'string') {
          esObject[key] = parseFloat(esObject[key])
        }

        if (Array.isArray(esObject[key]) && esObject[key].length > 1) {
          esObject[key] = Math.max(...esObject[key]) // 后续查询的时候，查询 lowerBound < 最大预算即可
        }

        if (esObject[key] === 0) {
          // eslint-disable-next-line @typescript-eslint/no-dynamic-delete
          delete esObject [key]
        } else if (esObject [key] >= 10000) {
          esObject [key] = Math.round (esObject [key] / 10000)
        }

      } else if (typeof esObject [key] === 'string') {
        esObject [key] = esObject [key].trim ()
      } else if (key === 'country') {
        if (Array.isArray(esObject [key])) {
          esObject [key] = esObject [key].join(',')
        }
      } else {
        esObject [key] = esObject [key]
      }
    }

    // 查询对象校验
    const esObjectSchema = z.object ({
      budget: z.number ().optional(),
      budget_is_per_year: z.boolean ().optional(),
      gpa: z.string().optional(),
      languageTestScore: z.string ().optional(),
      applicationStage: z.enum (['高中', '大专', '本科', '硕士', '博士']).optional(),
      currentLevelOfEducation: z.enum (['初中', '职高', '中专', '高中', '大专', '本科', '硕士', '博士']).optional(),
      currentEducationBackground: z.string().optional(),
      country: z.string().optional(),
      requirementType: z.string().optional(),
    })

    try {
      // Now validate using the Zod schema
      const validationResult =  esObjectSchema.safeParse(esObject)
      if (validationResult.success) {
        return validationResult.data
      } else {
        logger.error ('PreQuery construct Validation failed!', validationResult.error)
        return esObject
      }
    } catch (error) {
      console.error ('Validation failed!', error)
      return null
    }
  }

  private static constructESQuery(params: any) {
    const query: any = {
      bool: {
        must: [],
        should: [],
        filter: []
      }
    }

    // 必须匹配的条件
    if (params.currentEducationBackground) {
      query.bool.must.push({
        match: {
          current_education_background: params.currentLevelOfEducation
        }
      })
    }

    if (params.applicationStage) {
      query.bool.must.push({
        match: {
          application_stage: params.applicationStage
        }
      })
    }

    // 预算范围过滤
    if (params.budget) {
      if (Array.isArray(params.budget)) {
        params.budget = Math.max(...params.budget)
      }

      query.bool.filter.push({
        range: {
          budget: {
            gte: params.budget - 50,
            lte: params.budget + 50
          }
        }
      })
    }

    // 可选的模糊匹配条件，这几个字段的重要度提高
    const importantFields = ['major', 'school', 'user_intended_country', 'grade']
    importantFields.forEach((field) => {
      if (params[field]) {
        query.bool.should.push({
          multi_match: {
            query: params[field],
            fields: [field],
            fuzziness: 'AUTO',
            boost: 2 // 提高重要度
          }
        })
      }
    })

    const optionalFields = ['goal', 'gpa', 'language_score', 'location', 'is_parent']
    optionalFields.forEach((field) => {
      if (params[field]) {
        if (field === 'is_parent') {
          query.bool.should.push({
            term: {
              [field]: params[field]
            }
          })
        } else {
          query.bool.should.push({
            multi_match: {
              query: params[field],
              fields: [field],
              fuzziness: 'AUTO'
            }
          })
        }
      }
    })

    // 如果没有可选字段，添加一个匹配所有文档的查询条件
    if (query.bool.should.length === 0) {
      query.bool.should.push({
        match_all: {}
      })
    }

    query.bool.minimum_should_match = 1

    return query
  }

}