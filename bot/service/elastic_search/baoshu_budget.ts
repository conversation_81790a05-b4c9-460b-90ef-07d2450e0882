import { ObjectUtil } from '../../lib/object'
import logger from '../../model/logger/logger'
import { UserEducationGroup } from '../../service/baoshu/components/flow/nodes/intentionCalculate'
import ElasticSearchService from '../../model/elastic_search/elastic_search'

export interface IProjectBudget {
    educationStage: string
    projectName: string
    budgetLowerBound: number
    budgetUpperBound: number
    annualBudgetLowerBound: number
    annualBudgetUpperBound: number
}

export interface IElasticProjectSearchRes {
    _index: string
    _type: string
    _id: string
    _score: number
    _source: IProjectBudget
}

interface IBudgetsSearchParam {
  application_stage?: string // 申请阶段
  education_group: number
  projectNames: string[]
  budget: number
  budget_is_per_year?: boolean
}

interface IBudgetSearchParam {
  application_stage?: string // 申请阶段
  education_group: number
  projectName: string
  budget: number
  budget_is_per_year?: boolean
}


/**
 * 暴叔项目反向索引表
 */
export class BaoshuBudgetSearch {
  public static async listProjectsByEducationGroup(educationGroup: string) {
    const index = 'budget_index'
    const query =  {
      'bool': {
        'must': [
          {
            'match': {
              'educationStage': educationGroup
            }
          }
        ]
      }
    }

    return await ElasticSearchService.search(index, query, 20)
  }



  public static async search(query: IBudgetSearchParam) {
    // 校验 esJSON 格式
    const preEsQuery = this.getPreQuery(query)

    if (!preEsQuery || ObjectUtil.isEmptyObject(preEsQuery)) {
      return null
    }

    const esQuery = this.constructESQuery(preEsQuery)
    logger.debug('esQuery:', JSON.stringify(esQuery, null, 4))

    // 返回文档内容
    const searchRes =  await ElasticSearchService.search('budget_index', esQuery) as IElasticProjectSearchRes[]

    if (searchRes.length > 0) {
      return searchRes[0]._source
    }

    return null
  }

  private static getPreQuery(esObject: IBudgetSearchParam) {
    // 预处理对象
    // 高中以后阶段， isStudyAbroad later 改为 true
    const preQuery = {}
    // 默认往前进一阶
    const nextEducationLevel = {
      [UserEducationGroup.JuniorTwoToFour]: '高中',
      [UserEducationGroup.HighSchool]: '本科',
      [UserEducationGroup.College]: '大专',
      [UserEducationGroup.University]: '硕士',
      [UserEducationGroup.Gaokao]: '本科',
      [UserEducationGroup.JapanHighSchool]: '本科',
      [UserEducationGroup.Master]: '硕士'
    }

    if (esObject.education_group) {
      if (!nextEducationLevel[esObject.education_group]) {
        logger.error('超出范围: education_group', esObject.education_group)
        return preQuery
      }

      preQuery['educationStage'] = nextEducationLevel[esObject.education_group]
    }

    if (esObject.application_stage) {
      // 注意 esObject.application_stage 不一定准确，必须 > 当前教育阶段才算有效
      if (['本科', '硕士', '高中'].includes(esObject.application_stage)) {
        const applicationStageIndex = {
          '高中': UserEducationGroup.HighSchool,
          '本科': UserEducationGroup.University,
          '硕士': UserEducationGroup.Master
        }

        if (applicationStageIndex[esObject.application_stage] > esObject.education_group || esObject.application_stage === '硕士') { // 进入到这里的硕士，应该是申请二硕
          preQuery['educationStage'] = esObject.application_stage
        }
      }
    }

    preQuery['projectName'] = esObject.projectName
    if (!esObject.budget) {
      esObject.budget = 9999
    }

    if (Array.isArray(esObject.budget)) {
      esObject.budget = Math.max(...esObject.budget)
    }

    if (esObject.budget > 10000) {
      esObject.budget /= 10000
    }

    preQuery['budget'] = esObject.budget
    preQuery['budget_is_per_year'] = Boolean(esObject.budget_is_per_year)

    return preQuery
  }

  private static constructESQuery(preEsQuery: Record<string, any>) {
    const query: Record<string, any> = {
      bool: {
        must: []
      }
    }

    if (preEsQuery.educationStage) {
      query.bool.must.push ({
        match: {
          educationStage: preEsQuery.educationStage
        }
      })
    }

    if (preEsQuery.projectName) {
      query.bool.must.push ({
        match: {
          projectName: preEsQuery.projectName
        }
      })
    }

    return query
  }

  public static async isBudgetEnough(param: IBudgetsSearchParam) {
    // 并行搜索，找到匹配的项目预算
    let projectBudgets = await Promise.all(param.projectNames.map((projectName) => {
      return this.search({
        application_stage: param.application_stage,
        education_group: param.education_group,
        projectName: projectName,
        budget: param.budget,
        budget_is_per_year: param.budget_is_per_year
      })
    }))

    projectBudgets = projectBudgets.filter((project) => project !== null)

    if (!projectBudgets.length) { // 找不到对应的国家下的预算
      return {
        isBudgetEnough: true,
        projectBudgets: []
      }
    }

    // 只要有一个在范围就返回 true
    const isBudgetEnough = projectBudgets.some((project) => {
      if (!project) {
        return false
      }

      // 专升本只考虑一年的预算即可
      const isCollege = param.education_group === UserEducationGroup.College

      if (param.budget_is_per_year || isCollege) { // 专升本考虑一年预算
        return project.annualBudgetLowerBound <= param.budget
      }

      return project.budgetLowerBound <= param.budget
    })

    return {
      isBudgetEnough,
      projectBudgets
    }
  }
}