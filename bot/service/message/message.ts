import { Config } from '../../config/config'


// TODO 定义消息类型，重新封装消息

export enum IMessageType {
    Text = 'text',
    Image = 'image',
    CustomEmoticon = 'custom_emoticon',
    MiniProgram = 'miniprogram',
    ContactCard = 'contact_card',
    GroupAt = 'group_at',
}

export interface IMessage {
    type: IMessageType
    content?: string
    file_path?: string
    url?: string
    at_id?: string
}

export type MessageType = string | IMessage