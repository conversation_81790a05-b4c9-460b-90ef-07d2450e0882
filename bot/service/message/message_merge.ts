import { MessageReplyService } from './message_reply'
import { Config } from '../../config/config'
import { getChatId } from '../../config/chat_id'
import { HumanTransfer, HumanTransferType } from '../baoshu/components/human_transfer'
import { ChatHistoryService } from '../baoshu/components/chat_history'
import { ChatDB } from '../baoshu/database/chat'
import {
  IReceivedMessage,
  IReceivedMessageSource,
  IReceivedTextMsg,
  IReceivedImageMsg,
  IReceivedVoiceMsg,
  IWecomReceivedMsgType
} from '../../lib/juzi/type'
import { JuziAPI } from '../../lib/juzi/api'
import { LRUCache } from 'lru-cache'
import AsyncLock from 'async-lock'

import { isPastUser } from '../baoshu/components/flow/nodes/helper/pastUser'
import logger from '../../model/logger/logger'
import XunfeiASR from '../../model/nls/xunfei'
import { LLM } from '../../lib/ai/llm/LLM'
import { Queue, Worker } from 'bullmq'
import { RedisCacheDB } from '../../model/redis/redis_cache'
import { UUID } from '../../lib/uuid/uuid'


/**
 * 用于管理全局消息队列，为每个用户设置单独的计时器，合并处理消息
 */
export class GlobalMessageHandlerService {
  private static readonly messageSet = new LRUCache<string, any>({ max: 3000 })

  private static messageQueueBullMQ: Queue | undefined
  private static messageQueueWorker: Worker | undefined

  private static getQueueName() {
    const accountId = Config.setting.wechatConfig?.id ?? UUID.short()

    return `user-message-${accountId}` // 绑上账号 id 防止被其他账号抢占任务
  }

  // 为每个用户生成一个独立的 Redis Key 用于暂存消息
  private static getMessageStoreKey(userId: string): string {
    return `message_store:${getChatId(userId)}`
  }

  // 获取 BullMQ 队列的单例
  private static getMessageQueue(): Queue {
    if (!this.messageQueueBullMQ) {
      this.messageQueueBullMQ = new Queue(this.getQueueName(), {
        connection: RedisCacheDB.getInstance()
      })
    }
    return this.messageQueueBullMQ as Queue
  }


  public static async addMessage (message: IReceivedMessage) {
    if (!message.imContactId) {
      return
    }

    if (message.messageId && this.messageSet.has(message.messageId)) {
      return
    }

    // 不处理群消息
    if (message.roomWecomChatId || message.imRoomId) {
      return
    }

    if (message.messageId) {
      this.messageSet.set(message.messageId, 1)
    }

    if (message.payload && 'text' in message.payload && message.payload.text) {
      logger.debug('接收消息', message.payload.text, message.imContactId)
    }

    const senderId = message.imContactId
    const messageId = message.messageId

    try {
      // 1. 将收到的消息持久化到 Redis Set 中，替代内存中的 Map
      //    使用 RedisCacheDB 封装对 Redis 的操作
      const messageStore = new RedisCacheDB(this.getMessageStoreKey(senderId))
      await messageStore.addSet(JSON.stringify(message))

      // 2. 计算延迟时间，逻辑与原 resetUserTimer 一致
      let delay: number
      if (Config.setting.wechatConfig?.id === senderId) { // 自己发的消息立即处理
        delay = 0
      } else {
        delay = Config.setting.waitingTime.messageMerge //xx 秒后处理消息
      }

      // 3. 添加一个 Job 到 BullMQ。
      //    - 不再使用自定义 jobId，让 bullmq 自动生成唯一 ID。
      //    - Job 的数据中必须包含 messageId，用于后续在 worker 中判断。
      await this.getMessageQueue().add(
        'process-user-message', // Job 类型名
        { userId: senderId, messageId: messageId }, // 传递 userId 和 messageId
        {
          delay
        }
      )

    } catch (e) {
      logger.error('消息添加到持久化队列失败', { error: e, userId: senderId })
    }
  }

  /**
   *  检查当前 job 关联的消息是否是该用户最新的消息
   */
  private static async isLatestMessage(userId: string, messageId: string): Promise<boolean> {
    const messageStore = new RedisCacheDB(this.getMessageStoreKey(userId))
    const messages = await messageStore.getSetMembers()

    if (!messages || messages.length === 0) {
      // 如果消息已经被处理并清空，那么任何 job 都不应再继续执行
      return false
    }

    // 按时间戳排序找到最新的消息
    messages.sort((msgA, msgB) => msgA.timestamp - msgB.timestamp)

    // 比较最新的消息 ID 是否与当前 Job 携带的 ID 一致
    return messages[messages.length - 1].messageId === messageId
  }

  /**
   * 启动 Worker
   * 这个方法必须在你的应用启动时调用一次
   */
  public static startWorker() {
    if (this.messageQueueWorker) {
      logger.warn('消息处理 Worker 已经启动，无需重复启动。')
      return
    }

    logger.trace('正在启动消息处理 Worker...', this.getQueueName())

    this.messageQueueWorker = new Worker(
      this.getQueueName(),
      async (job) => {
        const { userId, messageId } = job.data

        // 关键：只处理最新的消息，旧的 job 会在这里被安全地跳过
        const isLatest = await this.isLatestMessage(userId, messageId)
        if (!isLatest) {
          // logger.debug(`跳过非最新消息触发的 Job: ${messageId} for user ${userId}`)
          return
        }

        // 直接调用消息处理函数，就像以前 setTimeout 的回调一样
        await GlobalMessageHandlerService.processUserMessages(userId)
      },
      {
        connection: RedisCacheDB.getInstance(),
        concurrency: 20 // 可以处理20个用户的消息并发，可根据服务器性能调整
      }
    )

    this.messageQueueWorker.on('failed', (error) => {
      logger.error('消息处理 Worker failed', error)
    })

    this.messageQueueWorker.on('error', (error) => {
      logger.error('消息处理 Worker 遇到错误', error)
    })

    logger.trace('消息处理 Worker 已成功启动。')
  }

  /**
   * 核心处理逻辑
   * 从 Redis 获取消息，而不是内存 Map
   */
  private static async processUserMessages(userId: string) {
    try {
      // 1. 获取该用户在这段时间内暂存的所有消息
      const messageStore = new RedisCacheDB(this.getMessageStoreKey(userId))
      const messagesJson = await messageStore.getSetMembers()

      if (!messagesJson || messagesJson.length === 0) {
        logger.warn(`准备处理用户 ${userId} 的消息，但在 Redis 中未找到消息，可能已被处理。`)
        return
      }

      // 2. 从 Redis 中删除这些消息，防止重复处理
      await messageStore.del()

      // 3. 将 JSON 字符串反序列化为消息对象
      const userMessages: IReceivedMessage[] = messagesJson.map((msgStr) => msgStr as IReceivedMessage)

      // 4. 按时间戳排序，逻辑保持不变
      userMessages.sort((msgA, msgB) => msgA.timestamp - msgB.timestamp)

      // 5. 调用后续的处理和回复逻辑，完全保持不变
      const texts = await this.getTextsFromMessages(userMessages, userId)
      await this.replyMessage(userId, texts)

    } catch (e) {
      console.error(`处理用户 ${userId} 的消息出错：`, e)
    }
  }


  private static async getTextsFromMessages(messages: IReceivedMessage[], userId: string): Promise<string[]> {
    const texts : string[] = []
    const ignoreMessage = false // 是否忽略当前消息
    for (const message of messages) {
      try {
        let text = ''
        const isSendByOpenAPI = message.source === IReceivedMessageSource.APIMessage
        if (isSendByOpenAPI) {
          // 忽略当前 AI 发送的消息
          continue
        }

        // 忽略系统通知
        if ([IWecomReceivedMsgType.SystemMessage, IWecomReceivedMsgType.WeChatWorkSystemMessage].includes(message.messageType)) {
          continue
        }

        // 忽略欢迎语
        if (message.messageType === IWecomReceivedMsgType.Text && this.isWelcomeMessage((message.payload as IReceivedTextMsg).text as string) && await this.isFirstMessage(userId) && !await isPastUser(userId)) {
          continue
        }

        switch (message.messageType) {
          case IWecomReceivedMsgType.Text:
            const payload = message.payload as IReceivedTextMsg

            if (payload.quoteMessage && payload.quoteMessage.content.text) { // 引用非文本，查一下 对应的附件的注释
              text =  `对 "${payload.quoteMessage.content.text}" 的回复是:\n ${payload.text}`
            }

            text = payload.text
            break

          case IWecomReceivedMsgType.Image:
            text = await this.handleImageMessage(userId, getChatId(userId), message) ?? text
            break

          case IWecomReceivedMsgType.Emoticon:
            console.log(JSON.stringify(message, null, 4))
            text = '[[表情]]'
            break

          case IWecomReceivedMsgType.Voice:
            // 语音转文字
            const msg = message.payload as IReceivedVoiceMsg
            if (msg.text) {
              text = msg.text
            } else {
              const xunfei = new XunfeiASR({
                appId: Config.setting.xunfeiASR.appId,
                secretKey: Config.setting.xunfeiASR.secretKey,
                uploadFileUrl: msg.voiceUrl
              })

              text = await xunfei.getResult()
            }

            break

          case IWecomReceivedMsgType.Unknown:
          case IWecomReceivedMsgType.MessageRecall:
          case IWecomReceivedMsgType.VoiceOrVideoCall:
            // 忽略这些消息
            break


          default:
            console.log(JSON.stringify(message, null, 4))
            await this.handleUnknownMessageType(message)
        }

        // 客服或者手机端客服侧人工回复的消息，不处理，但是需要存一下
        if (message.isSelf && message.source !== undefined && [IReceivedMessageSource.MobilePush, IReceivedMessageSource.TeamConsoleManual].includes(message.source)) {
          const userId = await JuziAPI.externalIdToWxId(message.customerExternalUserId as string)
          const chatId = getChatId(userId as string)

          await ChatHistoryService.addBotMessage(chatId, text)
        } else {
          texts.push(text)
        }
      } catch (e) {
        // 避免消息处理过程中出错导致程序崩溃
        console.error('单条消息解析出错：', e)
      }
    }

    if (ignoreMessage) {
      return []
    }

    return texts
  }

  private static async handleImageMessage(userId: string, chatId: string, message: IReceivedMessage) {
    if (message.isSelf || message.coworker || userId.startsWith('1688')) { // 自己发送的图片或员工发送的都不处理
      return
    }

    const originalImageResponse = await JuziAPI.getOriginalImage(message.chatId, message.messageId as string)
    let originalImageUrl: string

    if (originalImageResponse.code !== 0) {
      originalImageUrl = (message.payload as IReceivedImageMsg).imageUrl
    } else {
      originalImageUrl = originalImageResponse.data.url
    }

    return await this.imageCaptioning(originalImageUrl, chatId)
  }

  private static async imageCaptioning(imageUrl: string, chatId: string) {
    const response = await new LLM({
      temperature: 0,
      max_tokens: 200,
      meta: {
        promptName: 'image_caption',
        chat_id: chatId,
        description: '图片转文本'
      }
    }).imageChat(imageUrl, '你是一个专业的留学顾问名字叫暴叔，正与客户聊天。客户发来一张图片，请对这张图片进行简要解释，要求言简意赅，只输出一段话')


    return `【图片】${response}`
  }

  public static async handleUnknownMessageType(message: IReceivedMessage) {
    if (!message.imContactId)
      return

    const chat_id = getChatId(message.imContactId)
    logger.log(chat_id, '非文本消息类型', message.messageType)

    if (await ChatDB.isHumanInvolvement(chat_id)) { // 已经人工参与了，不再处理
      return
    }

    if (await isPastUser(message.imContactId)) {
      return
    }
    await HumanTransfer.transfer(chat_id, message.imContactId, HumanTransferType.UnknownMessageType)
  }

  private static isWelcomeMessage(msg: string) {
    if (msg.includes('我通过了你的联系人验证请求，现在我们可以开始聊天了') || msg.includes('我已经添加了你，现在我们可以开始聊天了。') || msg.includes('请求添加你为朋友') || msg.includes('很荣幸认识你，期待与你合作'))
      return true

    const welcomeMsgRegex = /^我是.{1,10}$/
    if (welcomeMsgRegex.test(msg))
      return true

    return false
  }

  private static async isFirstMessage(userId: string) {
    const chatId = getChatId(userId)

    return await ChatHistoryService.getUserMessageCount(chatId) === 0
  }

  private static async replyMessage(userId: string, texts: string[]) {
    const lock = new AsyncLock()
    try {
      await lock.acquire(userId, async () => {
        return await MessageReplyService.reply(texts, userId)
      }, { timeout: 30 * 1000 })
    } catch (e) {
      // ignore
    }
  }
}