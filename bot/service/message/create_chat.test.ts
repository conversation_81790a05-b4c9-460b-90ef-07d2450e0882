import { MessageReplyService } from './message_reply'
import { Config } from '../../config/config'
import { ObjectUtil } from '../../lib/object'

describe('Test', function () {
  beforeAll(() => {
    Config.setting.wechatConfig = {
      orgToken: 'e0d70927040a4efa92b79b7279ecb1c1',
      id: '1688855135509802',
      botUserId: 'momoLiaoLiuXue',
      name: '暴叔',
      notifyGroupId: '',
      counselorIds: []
    }
  })

  it('should pass', async () => {
    await MessageReplyService.saveChat('7881302252046367_1688855135509802', '7881302252046367')
  }, 60000)

  it('', async () => {
    console.log(JSON.stringify(ObjectUtil.merge({ fk: 'u' }, {}), null, 4))
  }, 60000)
})