import { DevelopConfigure } from './develop'
import { ProductConfigure } from './prod'
import { IConfig } from './interface'

export class Config {
  private static _setting: IConfig | undefined

  public static get setting(): IConfig {
    if (!this._setting) {
      let config
      if (process.env.NODE_ENV === 'prod') {
        config = ProductConfigure
      } else if (process.env.NODE_ENV === 'dev') {
        config = DevelopConfigure
      } else {
        config = DevelopConfigure // 默认使用开发环境配置
      }

      this._setting = {
        // 加入启动 bot 时候的环境变量
        ...config,
        AGENT_ID: process.env.AGENT_ID ?? '',
        BOT_NAME: process.env.BOT_ID ?? '暴叔',
        CREATE_AT: process.env.CREATE_AT ?? '',
        onlyReceiveMessage: process.env.ONLY_RECEIVE_MESSAGE === 'true'
      }

      // 本地测试开发，设置下 localTest 参数
      if (process.env.NODE_ENV === 'test') {
        (this._setting as IConfig).localTest = true
      }
    }

    return this._setting as IConfig
  }

  public static get salesperson_name(): string {
    const person_name =  this._setting?.botConfig?.person_name
    if (!person_name) {
      throw new Error('person_name 读取失败， 检查是否初始化完成')
    }
    return person_name
  }

  public static isDev() {
    return process.env.NODE_ENV !== 'prod'
  }
}