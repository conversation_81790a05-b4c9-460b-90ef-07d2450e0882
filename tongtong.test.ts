import { Config } from './bot/config/config'
import { getChatId } from './bot/config/chat_id'
import { BaoshuFlow } from './bot/service/baoshu/components/flow/flow'
import { ChatHistoryService } from './bot/service/baoshu/components/chat_history'
import { ChatStatStoreManager } from './bot/service/baoshu/storage/chat_state_store'
import { LogStoreService } from './bot/service/baoshu/components/log_store'
import { ChatDB } from './bot/service/baoshu/database/chat'
import { ObjectUtil } from './bot/lib/object'


Config.setting.localTest = true
const user_id = '7881300846030208'
const chat_id = getChatId(user_id)

async function chat(messages: string[]) {
  for (const message of messages) {
    await BaoshuFlow.step(chat_id, user_id, message)
  }
}
let messages: string[] = []

describe('Test', function () {
  beforeAll(async () => {
    Config.setting.wechatConfig =   {
      orgToken: 'e0d70927040a4efa92b79b7279ecb1c1',
      id: '1688854546332791',
      botUserId: 'ShengYueQing',
      name: '暴叔',
      counselorIds: [],
      notifyGroupId: 'R:10735753744477170'
    }
  })


  beforeEach(async () => {
    await ChatHistoryService.clearChatHistory(chat_id)
    await LogStoreService.clearChatLog(chat_id)
    await ChatStatStoreManager.clearState(chat_id)
    if (await ChatDB.getById(chat_id)) {
      await ChatDB.setHumanInvolvement(chat_id, false)
    }
  }, 1E8)

  afterEach(async () => {
    await chat(messages)
  }, 5 * 60 * 1000)

  it('线下沟通', async () => {
    messages = ['你好，能约线下么']
  }, 60000)

  it('人机1', async () => {
    messages = [
      '暴叔在吗',
      `好的
新加坡专升本`,
      `有什么学制较短的院校推荐吗
是的
大三
工程造价专业`,
      `我看暴叔视频上说最快8个月
这是什么院校？`,
      `好的
快捷课程是什么意思呢
新加坡`,
      `我现在上的就是中外合办的3➕2，我们学校就是和韩国、泰国、新西兰之类合作的。所以不想去这些国家。
新加坡啊`,
      `哦哦好
那这样的院校有哪些呢
20w左右吧`,
      '那需要多少呢？有什么院校推荐吗？最好是一年之内快速能拿到本科'
    ]
  }, 60000)


  it('大一计算机 进群', async () => {
    messages = [
      '你好',
      '我大一，学的是计算机，但是感觉学的东西不是很实用，想转专业，有什么建议吗？',
    ]
  })

  it('非经营范围的国家1', async () => {
    messages = [
      '暴叔, 菲律宾怎么规划'
    ]
  })

  it('非经营范围的国家2', async () => {
    messages = [
      '暴叔, 缅甸怎么规划',
    ]
  })

  it('马来西亚中文', async () => {
    messages = [
      '暴叔, 我学古汉语的，马来有推荐么',
      '我英语不好，可以中文授课么',
      '哪些地方有呢'
    ]
  })

  it('不是意向客户', async () => {
    messages = [
      '暴叔\n' +
      '不打算留学\n' +
      '我就想问问物理，化学，地理\n' +
      '选什么专业好啊？\n' +
      '我有点迷茫',
    ]
  })
  it('不是意向客户2', async () => {
    messages = [
      '就是问一下职高\n' +
      '怎么规划一下留学会对家庭没什么负担\n' +
      '嗯，意思是负担不那么大',
      '每年几万块吧'
    ]
  })

  it('大一计算机 进群', async () => {
    messages = [
      '你好',
      '我大一，学的是计算机，但是感觉学的东西不是很实用，想转专业，有什么建议吗？',
    ]
  })

  it('推荐项目测试', async () => {
    messages = [
      '暴叔你好，我是高三学生，成绩平时80左右，英语也一样，家里预算每年20个左右。家在福州，想申请一个海外排名比较好的学校。有什么推荐吗',
    ]
  })

  it('单点问题测试', async () => {
    messages = [
      '暴叔，你好。想咨询一下，有没有适合教师考非全的专业对口的院校？港澳那边有非全的说法的吗？',
      '我本科',
      '嗯嗯，只是想那个硕士文凭',
      '具体有什么项目呀',
      '需要什么条件吗？暴叔'

    ]
  })

  it('已经有自己想法客户的测试', async () => {
    messages = [
      '暴叔，你好。想咨询一下，我想去加拿大留学，我现在本科，预算40，就读于青岛理工大学，gpa3.8，英语六级，福建人，就像读个未来可以留下来的硕士',
    ]
  })

  it('投诉', async () => {
    messages = [
      '暴叔，你给我拉的顾问不行呀，本天都没有回复我的',

    ]
  })

  it('初一以下', async () => {
    messages = [
      '暴叔，我今年初一，学校实在学不下去了，国外有什么可以推荐的路径吗',


    ]
  })
  it('初三', async () => {
    messages = [
      '我现在初三，我这成绩应该是没有什么好高中可以考了，家里也没有啥想法，有什么其他什么路吗',

    ]
  })

  it('推项目测试4', async () => {
    messages = [
      '暴叔你好，我现在大专大三，每年预算10左右，毕业了估计也找不到什么好工作，想去海外读书个， 排名还可以的，性价比比较高的有什么推荐吗。我读建筑工程专业，福建人。英语也就4级吧。',

    ]
  })

  it('高一测试2', async () => {
    messages = [
      '暴叔您好！我现在是高一 成绩也不是很理想，然后我想大学去留学，一般英语要考多少分呀',

    ]
  })

  it('重复拉群', async () => {
    messages = [
      '暴叔，我现在高三，感觉状态不好，应该考不好，很焦虑',
      '家里也没什么规划，让我自己的想想',
      '我是看看除了高考有没有其他的路可以走走',
      '也可的，有什么推荐吗，暴叔',
      '嗯，本科的话的有什么好的吗\n我在杭州',
      '杭州不错，资源多\n平时英语成绩怎么样？',
      '还不错，考虑英美澳加吗？',
      '大概需要多少钱呀\n太贵估计我不大行',
      '我没什么概念诶。一般要多少',
      '那有点贵了\n可能每年 10w 最多了\n我们家',
      '需要什么条件申请暴叔',
      '就是感觉马来西亚回来是不是认可度一般',
      '不知道马来西亚有什么好学校'
    ]
  })

  it('重复提问', async () => {
    messages = [
      '你好，暴叔，我工作两年了，滑铁卢毕业，想去读个研，咋整',
      '哈哈，不牛啊。我已经回国工作一段时间了，我还没啥自己的想法，继续读计算机吧',
      '读完，继续工作吧',
      '100w吧',
      '75分',
      //       '75分，100w 够么，美国很贵吧',
      // '山东的'
    ]
  }, 60000)

  it('重复询问1', async () => {
    messages = [
      '暴叔你好，我想去新加坡可以吗？目前我是高二，雅思4.5-5.5，均分70%',
      '我想去新加坡国立有机会吗\n或者暴叔有什么新加坡其他院校的建议',
      '如果我就先去新加坡国立有什么机会吗？有什么办法吗'
    ]
  }, 60000)

  it('推荐院校', async () => {
    messages = [
      '您好，我的高考成绩在630到650之间，想去新加坡，我可以去什么学校呢，暴叔？',
      '我是一名河南考生',
      '我想去南方的学校，而且想学前景高一点的，我对化学比较感兴趣'

    ]
  }, 60000)

  it('推荐院校2', async () => {
    messages = [
      '您好，我的高考成绩在630到650之间，您能帮我看看有什么学校推荐吗？',
      '可以看看有什么学校吗',
      '我想去南方的学校，而且想学前景高一点的，我对化学比较感兴趣' +
      '我是学生'


    ]
  }, 60000)

  it('是否主动暴露暴叔（已经测试没问题）', async () => {
    messages = [
      '您好，我的高考成绩在630到650之间，您能帮我看看有什么学校推荐吗？',
      'tongtong，17xxxxxxx，高三  630，100，杭州，国际本科优先考虑。没什么概念。艺术类吧，我妈妈比较艺术',
      '想让暴叔帮我看看有什么学校可以走',
      '40-50吧',
      '我是学生',

    ]
  }, 60000)

  it('体育生往届规划）', async () => {
    messages = [
      '暴叔你好', '想留学', '想请您帮忙规划一下', '我是练体育的', '前面一年没有考上', '高三毕业', '学习成绩比较差', '您有什么建议吗', '我想去有体育专业的学校', '自己比较感兴趣', '可以听您说说吗', '不是很了解这些', '性价比高一点的', '在上海', '主要看我出去能读什么学校', '主要我英语不太好', '如果是 韩国 马来西亚', '也要过语言吗', '我没有高考成绩', '我还得读预科吧', '雅思最低要求是', '我英语实在是太差了', '要学韩语吗', '从学习到去那上课', '我明白的', '大概要多久呢',

    ]
  }, 60000)

  it('询问保录取）', async () => {
    messages = [
      '你好，我想了解一下，留学的事情。', '我这边的话，我现在是大三，马上暑假结束之后就是大四了，然后我是在一个2本，浙江万里学院，专业是生物工程，绩点在3.3往上的样子，然后比较麻烦的是，我语言是学的日语，高考的时候也是考的日语，但是也是因为我不是太喜欢日语', '所以说我也不想去日本，后现在基本上就是处于一个英语真的没什么基础的情况，从高一的时候开始就是日语了，所以可能我这个语言成绩真的是个问题', '那我能不能，先保录到英国100左右的大学，比如诺丁汉这种，然后距离我毕业还有1年多，这期间我补一下英语基础，后面在英国够我毕业就行，这样可以吗', '预算一百多都可以', '预算一般没问题', '浙江', '为啥', '完全小白，主要是临时想出去', '这种保录算是造价吗', '这样是吧', '澳洲也可以', '那是，因为有机构是给我这样说的', '稍等吧，我再问问，需要的话喊你', '澳洲的话，大概是哪些学校', '因为有好多个机构都是说，可以这样保', '然后支持背调',

    ]
  }, 60000)

  it('日语生规划', async () => {
    messages = [
      '孩子今年高考完学日语的预估4百分左右', '是想问问有啥好专业可选', '或者中外合作办学的', '江西', '赣州', '日语生', '100上下', '正常100-110', '哪国', '孩子英语啦下3年', '高中就没学英语', '是一年5-60还是两年', '那高考填报志愿是先报吗', '你那边电话多少', '电话先咨询下', '有些什么专业',

    ]
  }, 60000)
  it('89年大哥本科推荐大专项目错误', async () => {
    messages = [
      '你好', '我89年，毕业10年了，双非本科', '年轻走了弯路，现在没有什么才能，工作环境也很“牛马”，感觉自己没有价值感、存在感，就是一个牛马', '喜欢心理学，想要增长自己才干，提升学历', '求指点，谢谢', '这方面我不懂', '预算一年10万', '嗯嗯，谢谢你', '都可以，只要能改命', '中外合作办学硕士可以', '谢谢你', '好多年了，我忘记了，但是应该挺低的，因为我本科阶段，挂科留级过', '目前双证在手', '是这样，我本科水利水电工程', '想学心理学', '甘肃兰州的', '专升本？', '暴叔，我已经是全日制本科毕业了', '麻烦您看准一点', '我是全日制本科毕业，水利水电工程，双证已经在手', '89年的', '嗯嗯', '好的，谢谢你', '您是暴叔秘书？', '好的',

    ]
  }, 60000)

  it('国际本科专学分', async () => {
    messages = [
      '哈喽你好', '我目前就读于一所国内大学的国际本科，读大一。但是我昨天才发现我学校的这个项目是没有中留服备案的', '请问这个有什么风险吗', '还有一个就是', '我还有一个选择，就是读另外一个新加坡项目，下半年直接去，对接的是詹姆斯库克大学新加坡校区', '请问暴叔这里有项目吗', '就是我能直接对接的', '是这样的，我想直接对接到大二', '有这种可能吗', '华东交通大学', '我去新加坡的话大概要花45', '绩点这个还不清楚，学校说会帮我们润色一下', '我这个是国际本科，如果转的话也看中这个吗', '两年', '因为新加坡我只要去读两年', '江苏人', '对了我想问一下', '因为我读的是学校自己去对接的国际本科', '因为它没办案', '备案', '所以回来认证还是有风险对吗', '没备案和有备案的区别在哪里啊', '然后就是有备案的话会有什么优势', '我现在看中的其实就是，回来认证的本科学历',

    ]
  }, 60000)

  it('语言培训班级', async () => {
    messages = [
      '想在上海学英语，有好的推荐嘛', '上班族', '每天上班时间点比较固定，没有特殊原因都是18点下班，周末双休', '好的好的',

    ]
  }, 60000)

  it('预算少，日语', async () => {
    messages = [
      '真的是暴叔吗？', '爆叔大专学无人机，应用技术怎么样？', '想找你规划一下', '想去', '家里没钱', '加拿大', '6万—10万', '国内有没有什么中外联合办的学校？', '我高中日语', '安徽', '关键是我日语成绩也不怎么样', '在国内读个大专学习无人机应用技术', '这个专业在国外发展怎么样？', '在国内学习玩，然后再去国外搞刀乐', '然后再加上好好学习英语是吧？',

    ]
  }, 60000)

  it('浙大复读班级', async () => {
    messages = [
      '老师，我今年想出国读本科，然后咨询了新航道他给我推荐的是OSSD课程他们那边说他们给我小改一个成绩单然后学校盖章，也就是说我拿过去申请的是新航道给我的成绩', '老师这种可信度高吗', '新航道说大一之前的成绩都没有记录', '高三毕业', '新西兰', '他们就是说OSSD课程需要高中三年成绩，然后他们说他们给我做出一个成绩，然后让我拿到我上高中那个地方，让他给我盖章', '也就说难听一点就是他伪造一个成绩单，然后让我去盖章', '就是想去新西兰', '奥克兰大学', '河北沧州', '平时成绩不怎么好', '英语成绩100多', '300-400吧太低了', '您是暴叔本人吗', '我关注他很久了', '今天刚问完新航道', '他给我推荐的就是注册加拿大的学籍，然后走OSSD的国际课程，然后以国际生的身份申请新西兰的大学', '不过他说的就是高中三年成绩他们给小改一下，然后去盖章', '您好还在吗？', '问过两个机构后呢', '他们说我从学到准备好怎么着得26年出去', '时间太长', '您细说', '对', '我是河北人的', '我想的是', '不要那么晚才出去上大一，但是走的那个流程还必须要靠谱', '因为之前家里人了解到伪造成绩去上，然后到快要临毕业之后学校查成绩单，然后不合格或者蒙混过关的退学', '当然没有', '感觉不靠谱', '这个说好听点是小改（他们这么说）不好听就是伪造成绩', '他们还说大一之前的成绩没有记录让我们不用担心', '老师您办日本或者韩国这边的吗', '像新西兰这种时间太长', '那像韩国或者日本的呢？会好一点呢', '不是不是非要去新西兰', '老师有3＋1', '吗？', '老师那荷兰或者匈牙利呢？', '我们学校发过一个册子', '上面有',

    ]
  }, 60000)

  it('着急家长', async () => {
    messages = [
      '您好！在吗？', '想咨询一下留学的事', '看到请回复一下哦', '您好！情况是这样，我们是在新疆，孩子这次高考成绩不理想，估计280分-300分左右，这种情况，出国怎样报合适', '是不是太忙了，怎么没有人回复啊？', '您好！情况是这样，我们是在新疆，孩子这次高考成绩不理想，估计280分-300分左右，这种情况，出国怎样报合适', '理解，我们这孩子这情况，能上个什么学校？', '估计今年新疆的二本都要在280分左右了', '普通家庭', '不知道孩子出去上学，有没有双留服认证的', '费用能理解，就看是多少', '孩子英语成绩在100分左右', '就是不知道有怎样的学校适合孩子的', '在抖音上看到暴叔说的有一个浙大复读班，这个是这样的，可以介绍下吗？', '马来不太理想', '您刚才说的50-80万是出国一年的费用还是3+1的费用？', '最早我们都看好西郊利物浦这所学校，无奈，孩子不争气', '我不太懂', '可以的', '是啊，我挺着急的，24号分就出来了，我必须做决定，到底怎么上', '有没有专业的老师可以电话沟通的', '那麻烦帮我约一下可以吗？',

    ]
  }, 60000)

  it('单点问题多', async () => {
    messages = [
      '你好，现在是不是有些学校出国六个月后就可以回国了', '其他的全部托管', '专科想套研究生', '今年毕业', '想出去六个月就回来，然后可以网课', '20以内', '我看的白俄和大俄', '来是come，去是go，is跟着他她它这种水平', '全日制', '西班牙是不是可以出去一年就毕业', '什么价格和语言要求呢，而且我现在没有本科学历', '我已经在工作了', '我这个月毕业，但是已经买了一年的公积金和社保', '如果中间有个本科学历缺口，国内企业认可吗', '这是西班牙的哇', '不包含住宿生活费和车马费哇', '不包含后期自己开销的生活费，路费住宿', '学费+中介费杂七杂八的的多少呢',

    ]
  }, 60000)



  it('文文哥', async () => {
    messages = [
      '在吗', '中考考不上普高，有什么推荐', '家长', '想走个捷径', '没有大致规划', '是的', '没有预算', '合适点最好', '因为是双胞胎', '价格合适可以考虑', '打算高中', '呼和浩特', '英语基础很差', '四年拿本科毕业证是吧', '新西兰有啥排名吗',

    ]
  }, 60000)


  it('替朋友问问', async () => {
    messages = [
      '暴叔我想问一下有没有什么中外合办可以出国价格便宜的啊', '高考差不多一本线往上', '我朋友，他复读一年了', '他想读中外合办', '费用要低', '10w', '一年', '暴叔有推荐吗', '嗯嗯', '哪里可以呢', '好吧', '那我就先不管他了', '暴叔我想问问卡尔加里大学怎么样呢', '先不管他了，他恼火', '资金问题', '嗯嗯，我九月份去卡尔加里', '我就是想问问那个学校怎么样呢', '因为我gap year了一年', '好的', '谢了暴叔',

    ]
  }, 60000)


  it('识别体制外问题', async () => {
    messages = [
      '暴叔我g了，可以约个电话的，看看我怎么稳吗啊，', '我现在高二，', '可以打电话问问吗，', '没，我现在高二，', '明年高考，', '也想看看出国的路线，', '有不用高考就可以出去的吗，', '可以打电话吗，',

    ]
  }, 60000)

  it ('W.H', async () => {
    messages = [
      '英国，香港，新加坡留学哪里好？',
      '硕士费用多少钱一年呢？',
      '大四毕业',
    ]
  }, 60000)

  it ('W.H1', async () => {
    messages = [
      '英国，香港，新加坡留学哪里好？',
      '硕一复读了',
    ]
  }, 60000)

  it ('Eren Jaeger（进击版）', async () => {
    messages = [
      '我已经废了\n但是我太爽了',
      '想听吗',
      '打电话要不要\n不然你不懂我',
      '当然想逆袭 但是已经废了',
    ]
  }, 60000)

  it ('冰镇桃香乌龙（夏日版）', async () => {
    messages = [
      '暴叔晚上好，土木类的专升本学历，现在失业快两个月了，有没有什么好的方向啊？在南昌找不到工作。[流泪]，人太迷茫了，每天都失眠。好想学点什么，但是现在的就业环境真的太难了，学历本来就低，还是转行[苦涩]，想找准个方向死啃。',
      '能嘎嘎挣钱的国家和专业',
      '前期啥成本\n怎么苟着发育',
      '如果能嘎嘎挣钱，先苟着攒点钱冲了',
      '暂时不知道咋发育啊',
      '香港有用吗',
      '才存了不到十万，还没发育好，想香港吧，离家近点，广东的',
      '土木的，英语就那样，四级三百多分，请问您是暴叔本人嘛？',
      '想暴叔教一手咋稳的野路子，苟一两年冲香港',
      '转行的话，土木可以读点啥搞钱专业',
      '企业还认',
      '数据分析咋说',
      '6.5咋搞\n好难啊',
    ]
  }, 60000)

  it('高考生', async () => {
    messages = [
      '暴叔你好的，我预计上不来本科了。不想上国内专科。暴叔有什么指导吗',
      '家庭条件一般，希望费用低一点\n是的',
      '还没呢，就想看看暴叔有什么建议'
    ]
  }, 60000)

  it('12岁', async () => {
    messages = [
      '12岁女孩',
      '学习一般，未来如何规划\n北京' +
      '不多',
      '没概念呢\n性价比高的吧',
      '大概有8万',
      '好的'
    ]
  }, 60000)

  it('obj merge', async () => {
    function deepMerge (target: any, source: any): any {
      const output = { ...target }
      if (Array.isArray(target) && Array.isArray(source)) {
        return source
      } else if (typeof target === 'object' && typeof source === 'object') {
        for (const key in source) {
          if (source [key] instanceof Object && target instanceof Object && key in target) {
            output [key] = deepMerge (target [key], source [key])
          } else {
            output [key] = source [key]
          }
        }
      }
      return output
    }

    const obj1 = {
      a: 1,
      b: {
        c: 2
      },
      c: 3,
      d: [1, 2, 3]
    }
    const obj2 = {
      a: 2,
      b: {
        c: 3
      },
      d: [4, 5]
    }

    console.log(JSON.stringify(ObjectUtil.merge(obj1, obj2), null, 4))
  }, 60000)

})