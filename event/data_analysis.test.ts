import { FileHelper } from '../bot/lib/file'
import path from 'path'
import { AoChuangWechatContact } from '../bot/lib/auchuang/openapi/contact'
import { CacheDecorator } from '../bot/lib/cache/cache'
import { JSONHelper } from '../bot/lib/json/json'
import { Config } from '../bot/config/config'
import { ChatHistoryService } from '../bot/service/baoshu/components/chat_history'

export async function readLogFile(path: string) {
  const content = await FileHelper.readFile(path)
  const lines = content.split('\n').filter((line) => line.trim().length > 0)

  return lines.map((line) => {
    try {
      return JSON.parse(line)
    } catch (e) {
      console.log('line', line, path)
    }
  })
}

export async function readLogFiles(folder: string) {
  const paths = await FileHelper.listFiles(folder)

  const results = await Promise.all(paths.map((path) => readLogFile(path)))
  return results.flat()
}

export async function getUserName(chat_id: string) {
  try {
    const key = chat_id.split('_')[0]
    return await AoChuangWechatContact.getContactById(key)
  } catch (e) {
    return null
  }
}

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    for (let i = 25; i < 30; i++) {
      const people: string[] = []
      const date = i

      AoChuangWechatContact.getContactById = CacheDecorator.decorateAsync(AoChuangWechatContact.getContactById)
      // 数据分析
      // 聚合不同事件下的错误
      const rawData = await readLogFiles(path.join(__dirname, `2024-03-${date}`))
      // 替换 chat_id 为人名
      // 按照事件分类
      const eventMap = new Map<string, any[]>()

      for (const data of rawData) {
        if (!eventMap.has(data.chat_id)) {
          eventMap.set(data.chat_id, [])
        }
        eventMap.get(data.chat_id)?.push(data.event)
      }

      // 统计不同事件的数量
      const ziliaoCount = 0
      const reply1Count = 0
      const conversationRound = 0
      const conversationPushGroupCount = 0
      const silentAskGroupPushCount = 0
      const inGroupCount = 0

      const userNeedCount = 0
      const sendGroupInviteCount = 0

      for (const [chat_id, events] of eventMap) {
        // 资料发送，通过聊天 id
        try {
          if (chat_id.includes('local')) {
            eventMap.delete(chat_id)
            continue
          }
          const user = await getUserName(chat_id)
          if (!user) {
            continue
          }

          const alias = user.conRemark
          if (!alias) {
            eventMap.delete(chat_id)
            continue
          }

          if (alias.includes('麦子') || alias.includes('SYQ') || alias.includes('鸣') || !alias.includes(`03${date}`)) {
            eventMap.delete(chat_id)
            continue
          }

          people.push(user.nickname)
        } catch (e) {
          continue
        }
      }

      console.log(date, people)
    }
    // // 单独统计 资料发送 和 进群人数
    // for (let [chat_id, events] of eventMap) {
    //   // 资料发送，通过聊天 id
    //   try {
    //     const chatHistory = await ChatHistoryService.getChatHistory(chat_id)
    //     for (let item of chatHistory) {
    //       if (item.role == 'assistant' && item.content.includes('pan.baidu.com')) {
    //         ziliaoCount += 1
    //         break
    //       }
    //     }
    //
    //     for (let item of chatHistory) {
    //       if (item.content.includes('回复1') && chatHistory.indexOf(item) < chatHistory.length - 1) {
    //         // if last user message index > current index, it means the user has replied to the previous message
    //         const lastUserMessageIndex = chatHistory.slice(chatHistory.indexOf(item) + 1).findIndex(item => item.role == 'user')
    //
    //         if (lastUserMessageIndex > -1) {
    //           reply1Count += 1
    //         }
    //
    //         break
    //       }
    //     }
    //
    //     // 聊天轮数，以用户发送消息为准
    //     for (let item of chatHistory) {
    //       if (item.role == 'user') {
    //         conversationRound += 1
    //       }
    //     }
    //   } catch (e) {
    //     console.log(e)
    //   }
    // }
    //
    // for (let [chat_id, events] of eventMap) {
    //   // 资料发送，通过聊天 id
    //   try {
    //     const user = await getUserName(chat_id)
    //     if (!user) {
    //       continue
    //     }
    //
    //
    //     const alias = user.conRemark
    //     console.log(alias)
    //
    //     if (alias.includes('[B]') || alias.includes('vip'))
    //       inGroupCount += 1
    //
    //     // 对话推群，追问推群
    //     if (events.includes('对话拉群')) {
    //       conversationPushGroupCount += 1
    //     }
    //
    //     if (events.includes('追问拉群')) {
    //       silentAskGroupPushCount += 1
    //     }
    //
    //     if (events.includes('确认进群')) {
    //       sendGroupInviteCount += 1
    //     }
    //   } catch (e) {
    //     continue
    //   }
    // }
    //
    //
    //
    //  console.log(JSON.stringify({
    //    '日期': `03-${date}`,
    //    '总人数': eventMap.size,
    //    '资料发送': ziliaoCount,
    //    '进群人数': inGroupCount,
    //    '对话推群': conversationPushGroupCount,
    //    '追问推群': silentAskGroupPushCount,
    //    '确认进群': sendGroupInviteCount,
    //    '聊天轮数': (conversationRound / eventMap.size).toFixed(2),
    //    '回复1': reply1Count,
    //  }, null, 4))

    // 统计不同事件的数量
  }, 1E8)

  it('test', async () => {
    console.log(new Date('2024-03-25').getTime())
  }, 30000)
})