#%%
import path from "path";
import {AoChuangWechatContact} from "../bot/lib/auchuang/openapi/contact";
import {CacheDecorator} from "../bot/lib/cache/cache";
import {JSONHelper} from "../bot/lib/json/json";
import {init<PERSON>iliao, ZiliaoExecutor} from "../bot/service/diantou/components/user_trust_simulator/ziliao";
import {Config} from "../bot/config/config";


AoChuangWechatContact.getContactById = CacheDecorator.decorateAsync(AoChuangWechatContact.getContactById)
// 数据分析
// 聚合不同事件下的错误
const rawData = await readLogFiles(path.join(__dirname, '2024-03-25'))
// 替换 chat_id 为人名
// 按照事件分类
const eventMap = new Map<string, any[]>()

for (let data of rawData) {
  try {
    data.user_name = await getUserName(data.chat_id)
  } catch (e) {
    console.log(e)
  }
  if (!data.user_name) {
    continue
  }

  if (data.user_name.includes('麦子') || data.user_name.includes('SYQ') || data.user_name.includes('鸣'))
    continue

  if (!eventMap.has(data.user_name)) {
    eventMap.set(data.user_name, [])
  }
  eventMap.get(data.user_name)?.push(data.event)
}
#%%
