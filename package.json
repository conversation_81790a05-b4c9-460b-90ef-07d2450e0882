{"name": "wechaty_initializer", "version": "1.0.0", "description": "initialize multiple wechaty bots", "main": "index.js", "scripts": {"dev:install": "install --force", "server": "export NODE_ENV=dev && nodemon --exec 'ts-node' bot_starter/dispatcher/server.ts", "prisma": "prisma generate", "admin": "nodemon --watch 'bot/**/*.ts' --exec 'ts-node' admin_backend/websocket.ts", "test": "cd auto_test && npm run test", "start:server": "ts-node bot_starter/dispatcher/server.ts", "start:prd:server": "export NODE_ENV=prod && ts-node bot_starter/dispatcher/server.ts", "client:syq": "export NODE_ENV=dev WECHAT_NAME=syq && ts-node bot_starter/client/client_server.ts", "start:client:baoshu": "export NODE_ENV=dev WECHAT_NAME=baoshu-test && ts-node bot_starter/client/client_server.ts", "start:client:baoshu:orm": "export NODE_ENV=dev WECHAT_NAME=baoshu-test ONLY_RECEIVE_MESSAGE=true && ts-node bot_starter/client/client_server.ts", "start:client:baoshu1:orm": "export NODE_ENV=dev WECHAT_NAME=欢乐斗地主AFM-ada ONLY_RECEIVE_MESSAGE=true && ts-node bot_starter/client/client_server.ts", "start:client:baoshu2:orm": "export NODE_ENV=dev WECHAT_NAME=躺着数钱-郜天蛟 ONLY_RECEIVE_MESSAGE=true && ts-node bot_starter/client/client_server.ts", "start:client:baoshu3:orm": "export NODE_ENV=dev WECHAT_NAME=财富自由-pingping白 ONLY_RECEIVE_MESSAGE=true && ts-node bot_starter/client/client_server.ts", "start:client:baoshu4:orm": "export NODE_ENV=dev WECHAT_NAME=百万年薪-Janice ONLY_RECEIVE_MESSAGE=true && ts-node bot_starter/client/client_server.ts", "start:client:baoshu1": "export NODE_ENV=dev WECHAT_NAME=欢乐斗地主AFM-ada && ts-node bot_starter/client/client_server.ts", "start:client:baoshu2": "export NODE_ENV=dev WECHAT_NAME=躺着数钱-郜天蛟 && ts-node bot_starter/client/client_server.ts", "start:client:baoshu3": "export NODE_ENV=dev WECHAT_NAME=财富自由-pingping白 && ts-node bot_starter/client/client_server.ts", "start:client:baoshu4": "export NODE_ENV=dev WECHAT_NAME=百万年薪-Janice && ts-node bot_starter/client/client_server.ts", "start:client:baoshu5": "export NODE_ENV=dev WECHAT_NAME=\"支持组2(暴叔)\" && ts-node bot_starter/client/client_server.ts", "start:client:baoshu6": "export NODE_ENV=dev WECHAT_NAME=baoshu4 && ts-node bot_starter/client/client_server.ts", "start:client:baoshu7": "export NODE_ENV=dev WECHAT_NAME=baoshu7 && ts-node bot_starter/client/client_server.ts", "start:client:baoshu8": "export NODE_ENV=dev WECHAT_NAME=baoshu8 && ts-node bot_starter/client/client_server.ts", "start:client:horus": "export NODE_ENV=dev WECHAT_NAME=Horus && ts-node bot_starter/client/client_server.ts", "start:feishu": "ts-node feishu/server.ts", "local": "ts-node local.ts", "client": "ts-node bot_starter/client/client_server.ts", "deploy": "ts-node bot/docker/deploy.ts"}, "author": "", "license": "ISC", "devDependencies": {"@types/ali-oss": "^6.0.8", "@types/express": "^4.17.19", "@types/jest": "^29.5.12", "@types/mime": "^3.0.3", "@types/node": "^20.7.0", "@typescript-eslint/eslint-plugin": "^7.8.0", "@typescript-eslint/parser": "^7.8.0", "ali-oss": "^6.17.1", "chalk": "^4.1.2", "csv-parse": "^4.16.3", "csv-writer": "^1.6.0", "esbuild": "^0.19.4", "eslint": "^8.50.0", "eslint-config-prettier": "^8.2.0", "fuzzy": "^0.1.3", "husky": "^7.0.1", "inquirer": "^8.2.0", "inquirer-autocomplete-prompt": "^1.4.0", "jest": "^29.7.0", "mongoose": "^6.12.3", "prettier": "^3.0.3", "prisma": "^5.8.0", "ts-jest": "^29.2.4", "ts-node": "^10.9.1", "typescript": "^5.2.2", "yaml": "^1.10.2"}, "dependencies": {"@elastic/elasticsearch": "^8.13.1", "@langchain/community": "^0.3.40", "@langchain/core": "^0.3.44", "@langchain/openai": "^0.5.5", "@prisma/client": "^5.8.0", "@types/async-lock": "^1.4.2", "alibabacloud-nls": "^1.0.2", "async-lock": "^1.4.1", "axios": "^1.5.1", "bullmq": "^5.14.0", "chalk": "^4.1.2", "crypto-js": "^4.2.0", "express": "^4.18.2", "fast-xml-parser": "^4.3.5", "inquirer": "^8.2.4", "jsonrepair": "^3.6.0", "langchain": "^0.3.21", "lru-cache": "^10.2.0", "markdown-to-text": "^0.1.1", "mime": "^3.0.0", "openai": "^4.26.0", "openai-zod-functions": "^0.1.2", "p-limit": "^6.1.0", "pino": "^8.17.2", "pino-pretty": "^10.3.1", "short-uuid": "^4.2.2", "string-width": "4.2.3", "xbb-api": "^0.0.4"}}