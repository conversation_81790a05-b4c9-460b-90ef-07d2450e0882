import { createInterface } from 'readline'
import { Config } from './bot/config/config'
import { UUID } from './bot/lib/uuid/uuid'
import { getChatId } from './bot/config/chat_id'
import chalk from 'chalk'
import { BaoshuFlow } from './bot/service/baoshu/components/flow/flow'
import { WechatMessageSender } from './bot/service/baoshu/components/message_send'
import { ChatHistoryService } from './bot/service/baoshu/components/chat_history'
import { ChatStatStoreManager } from './bot/service/baoshu/storage/chat_state_store'
import { ChatDB } from './bot/service/baoshu/database/chat'
import logger from './bot/model/logger/logger'

Config.setting.localTest = true
Config.setting.wechatConfig =  {
  id: '1688854546332791',
  orgToken: 'e0d70927040a4efa92b79b7279ecb1c1',
  name: '暴叔',
  botUserId: 'ShengYueQing',
  notifyGroupId: 'R:10829337560927503',
  counselorIds: ['Ming']
}

const user_id = UUID.short()
const chat_id = getChatId(user_id)

class CLI {
  private cli: any
  private commands: {
    [key: string]: (...args: string[]) => any
  }

  constructor() {
    this.cli = createInterface({
      input: process.stdin,
      output: process.stdout,
    })

    this.commands = {
      'help': () => {
        this.displayMessage(`Commands: ${  Object.keys(this.commands).join(', ')}`)
      },
      'exit': () => {
        this.displayMessage('Exiting the test...')
        this.cli.close()
      },
      'clear': async () => {
        await WechatMessageSender.sendById({
          user_id: user_id,
          chat_id: chat_id,
          ai_msg: '聊天已重置'
        })
        await ChatHistoryService.clearChatHistory(chat_id)

        await ChatStatStoreManager.clearState(chat_id)
        if (await ChatDB.getById(chat_id)) {
          await ChatDB.setHumanInvolvement(chat_id, false)
        }
      },
      // Add more commands as needed
    }

    this.cli.on('line', async (line) => {
      const trimmedLine = line.trim()

      if (trimmedLine === '') {
        logger.warn('Empty input')
        return
      }

      const commandNames = Object.keys(this.commands)
      if (commandNames.some((c) => trimmedLine.startsWith(c))) {
        const [command, ...args] = trimmedLine.split(' ')
        if (this.commands[command]) {
          this.commands[command](...args)
        } else {
          this.displayMessage(`Unknown command: ${command}`)
        }
      } else {
        try {
          await BaoshuFlow.step(chat_id, user_id, line)
        } catch (error) {
          console.error('An error occurred:', error)
        }
      }
      // 在每次输入后显示提示信息
      this.displayPrompt()
    })

    this.cli.on('close', () => {
      process.exit(0)
    })
    // 初始显示提示信息
    this.displayPrompt()
  }

  displayMessage(message) {
    console.log(chalk.redBright(message))
  }

  displayPrompt() {
    console.log(chalk.blue('Enter your input (type "clear" to restart or "exit" to quit):'))
  }
}

function runInteractiveTest() {
  new CLI()
}

runInteractiveTest()