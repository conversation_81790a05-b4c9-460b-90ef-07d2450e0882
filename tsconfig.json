{
  "include": [
    "bot/**/*",
    "bot_starter/**/*",
  ],
  "compileOnSave": true,
  "compilerOptions": {
    "strictPropertyInitialization": false,
    "outDir": "./dist/",
    "sourceMap": true,
    "pretty": true,
    "skipLibCheck": true,
    "noImplicitAny": false,
    "module": "commonjs",
    "target": "esnext",
    "strict": true,
    "moduleResolution": "node",
    "lib": [
      "es2020",
      "dom",
      "es5",
      "es6",
      "es2021"
    ],
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
  },
  "paths": {
    "~/config/*": [
      "config/*"
    ],
    "~/const/*": [
      "const/*"
    ],
    "~/function/*": [
      "function/*"
    ],
    "~/lib/*": [
      "lib/*"
    ],
    "~/locals/*": [
      "locals/*"
    ],
    "~/middleware/*": [
      "middleware/*"
    ],
    "~/model/*": [
      "model/*"
    ],
    "~/service/*": [
      "service/*"
    ]
  },
  "exclude": ["dst", "node_modules", "test"]
}
