import { spawn } from 'child_process'

interface Config {
  appId: string
}

export async function main(config: Config) {
  // TODO 存储数据到数据库
  // TODO Token 池获取 Token
  const tokens = {
    token1: 'puppet_padlocal_7157f1d9c5a54015a380995540b405d3',
    token2: 'puppet_padlocal_52b5b534a44d4c2fbfaac0d5c9fdf84b',
  }

  const token = tokens['token2']
  const scriptPath = './bot/index.ts'

  // 启动脚本并注入参数
  const spawnedProcess = spawn('ts-node', [scriptPath], {
    env: {
      ...process.env,
      appId: config.appId,
      PAD_LOCAL_TOKEN: token,
    },
  })

  console.log(`启动进程 ID 为：${spawnedProcess.pid}`)
  // TODO 存储进程 ID 也需要存储

  // 显示脚本输出
  spawnedProcess.stdout.on('data', (data: any) => {
    console.log(`stdout: ${data}`)
  })

  spawnedProcess.stderr.on('data', (data: any) => {
    console.error(`stderr: ${data}`)
  })

  spawnedProcess.on('close', (code: any) => {
    console.log(`child process exited with code ${code}`)
  })
}

// 示例使用
main({
  'appId': 'wxid_xxxxx',
})

