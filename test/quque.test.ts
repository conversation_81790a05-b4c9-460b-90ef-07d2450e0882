// test-bullmq.spec.ts

import { Queue } from 'bullmq'
import { RedisDB } from '../bot/model/redis/redis'
import { sleep } from '../bot/lib/schedule/schedule'


describe('BullMQ Sanity Check', () => {

  const QUEUE_NAME = 'bullmq-sanity-check-queue'
  let queue: Queue

  // 在所有测试开始前，初始化队列
  beforeAll(() => {
    queue = new Queue(QUEUE_NAME, {
      connection: RedisDB.getInstance() // 确保这是有效的 ioredis 连接
    })
  })

  // 在所有测试结束后，关闭连接，清理现场
  afterAll(async () => {
    await queue.close()
  })

  it('should add a job and then retrieve it', async () => {
    // 1. 清理现场：确保队列是干净的，排除旧数据干扰
    console.log('Step 1: Cleaning the queue...')
    await queue.obliterate({ force: true }) // obliterate 会彻底删除队列所有数据
    console.log('Queue cleaned.')

    // 2. 添加一个 Job
    console.log('Step 2: Adding a new job...')
    const jobData = { message: 'hello world', timestamp: Date.now() }
    const job = await queue.add('test-job', jobData)
    console.log('Job added successfully:', JSON.stringify(job, null, 2))

    // 确认 job 对象是有效的
    expect(job).toBeDefined()
    expect(job.id).toBeDefined()
    expect(job.data.message).toEqual('hello world')

    // 等待一小会，确保 Redis 操作同步完成
    await sleep(500)

    // 3. 查询 Job
    console.log('Step 3: Retrieving jobs from the queue...')
    // getJobs 需要一个类型数组来指定查询哪些状态的 job
    // 我们查询所有可能的状态
    const jobs = await queue.getJobs(['wait', 'active', 'completed', 'failed', 'delayed', 'paused'])

    console.log('Retrieved jobs:', JSON.stringify(jobs, null, 2))

    // 4. 断言
    expect(jobs.length).toBe(1)
    expect(jobs[0].id).toEqual(job.id)
    expect(jobs[0].data.message).toEqual('hello world')

  }, 60000) // 设置一个较长的超时时间
})