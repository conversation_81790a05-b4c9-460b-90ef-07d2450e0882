import AsyncLock from 'async-lock'
import { MessageReplyService } from '../bot/service/message/message_reply'
import { Config } from '../bot/config/config'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    // 创建 AsyncLock 实例
    const lock = new AsyncLock()

    // 共享资源
    let sharedResource = 0

    // 模拟异步操作
    function asyncTask() {
      return new Promise((resolve) => {
        const currentValue = sharedResource
        setTimeout(() => {
          sharedResource = currentValue + 1
          resolve(null)
        }, Math.random() * 100)
      })
    }

    // 没有锁的测试函数
    async function testWithoutLock() {
      sharedResource = 0 // 重置共享资源
      console.log('Test without lock:')

      // 创建多个并发任务
      const tasks = []
      for (let i = 0; i < 10; i++) {
        // @ts-ignore23123
        tasks.push(asyncTask())
      }

      await Promise.all(tasks)
      console.log('Final shared resource value without lock:', sharedResource)
    }

    // 使用锁的测试函数
    async function testWithLock() {
      sharedResource = 0 // 重置共享资源
      console.log('Test with lock:')

      // 创建多个并发任务
      const tasks = []
      for (let i = 0; i < 10; i++) {
        // @ts-ignore12321
        tasks.push(lock.acquire('resource', async () => {
          await asyncTask()
        }))
      }

      await Promise.all(tasks)
      console.log('Final shared resource value with lock:', sharedResource)
    }


    // 没有锁的测试
    await testWithoutLock()

    // 使用锁的测试
    await testWithLock()
  }, 60000)

  it('测试', async () => {
    const lock = new AsyncLock()
    Config.setting.localTest = true

    const userId = '123'
    const texts = ['你好', '我是流川枫']

    async function reply() {
      await lock.acquire(userId, async () => {
        return await MessageReplyService.reply(texts, userId)
      }, { timeout: 10 * 1000 * 2 })
    }

    const promises: Promise<any>[] = []
    for (let i = 0; i < 10; i++) {
      promises.push(reply())
    }

    await Promise.all(promises)
  }, 60000)
})