import AsyncLock from 'async-lock'
import { PrismaMongoClient } from '../bot/service/mongodb/prisma'
import { MessageReplyService } from '../bot/service/message/message_reply'
import { UUID } from '../bot/lib/uuid/uuid'
import { v4 as uuidv4 } from 'uuid'
import { ChatHistoryService } from '../bot/service/baoshu/components/chat_history'

async function incrementUserCountIfFirstMessage(chat_id: string) {
  const userMsgCount = await ChatHistoryService.getUserMessageCount(chat_id)
  if (userMsgCount == 1) {
    const lock = new AsyncLock()
    await lock.acquire(chat_id, async () => {
      await MessageReplyService.countUserMsgGreaterThanOne(chat_id)
    }, { timeout: 30000 })
  }
}

async function getUserCount() {
  try {
    const userCounts = await PrismaMongoClient.getInstance().user_count.count()
    return userCounts
  } catch (e) {
    console.error('统计用户数量出错:', e)
    return 0
  }
}


describe('Test', function () {
  beforeAll(async () => {
    // 清空数据库
    await PrismaMongoClient.getInstance().user_count.deleteMany()
  })

  it('should handle userMsgCount = 1', async () => {
    // 创建五个 case， userMsgCount = 1
    for (let i = 0; i < 5; i++) {
      const chat_id = uuidv4()
      await ChatHistoryService.addUserMessage(chat_id, 'mock test')
      await incrementUserCountIfFirstMessage(chat_id)
    }
  })

  it('should handle userMsgCount = 1 then = 2', async () => {

    for (let i = 0; i < 5; i++) {
      const chat_id = uuidv4()
      await ChatHistoryService.addUserMessage(chat_id, 'mock test3')
      await incrementUserCountIfFirstMessage(chat_id)
      await ChatHistoryService.addUserMessage(chat_id, 'mock test4')
      await incrementUserCountIfFirstMessage(chat_id)
    }
  })

  it('should handle userMsgCount = 2', async () => {
    // 创建五个 case， userMsgCount = 2
    for (let i = 0; i < 5; i++) {
      const chat_id = uuidv4()
      await ChatHistoryService.addUserMessage(chat_id, 'mock test1')
      await ChatHistoryService.addUserMessage(chat_id, 'mock test2')
      await incrementUserCountIfFirstMessage(chat_id)
    }
  })

  it('should return the total user count', async () => {
    const totalUserCount = await getUserCount()
    console.log(`总用户数: ${totalUserCount}`)
  })
})

//


// describe('Test', function () {
//   beforeEach(async () => {
//     await PrismaMongoClient.getInstance().user_count.deleteMany({})
//   })
//   afterAll(async () => {
//     await PrismaMongoClient.getInstance().$disconnect()
//   })
//
//   it('should pass', async () => {
//     const chat_id = UUID.short()
//     const lock = new AsyncLock()
//     const numberOfConcurrentUpdates = 100
//     const promises: Promise<any>[] = []
//
//     await PrismaMongoClient.getInstance().user_count.create({
//       data: {
//         id: chat_id,
//         count: 0,
//       }
//     })
//     for (let i = 0; i < numberOfConcurrentUpdates; i++) {
//       const promise = lock.acquire(chat_id, async () => {
//         await MessageReplyService.incrementUserCreat(chat_id)
//       }, { timeout: 30000 })
//       promises.push(promise)
//     }
//     await Promise.all(promises)
//     const finalUserCount = await PrismaMongoClient.getInstance().user_count.findUnique({
//       where: { id: chat_id }
//     })
//     expect(finalUserCount).not.toBeNull()
//     expect(finalUserCount?.count).toBe(numberOfConcurrentUpdates)
//   }, 1E8)
// })



// describe('Test', function () {
//   beforeAll(() => {
//
//   })
//
//   it('should pass', async () => {
//     const chat_id1 = UUID.short()
//     await ChatHistoryService.addUserMessage(chat_id, 'hihi')
//
//     if (usecount > 1) {
//
//     }
//
//     const chat_id2 = UUID.short()
//     await ChatHistoryService.addUserMessage(chat_id, 'hihi')
//
//     if (usecount > 1) {
//
//     }
//   }, 60000)
// })