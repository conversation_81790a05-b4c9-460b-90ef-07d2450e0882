import { IWechatConfig } from '../bot/config/interface'
import { PrismaMongoClient } from '../bot/model/mongodb/prisma'

interface IBaoshuEnterpriseConfig {
 counselors: string[]
 notifyGroupId: string
 xbbId: string
}

export async function loadConfigByAccountName(name: string): Promise<IWechatConfig> {
  const config = await PrismaMongoClient.getConfigInstance().config.findFirst(
    {
      where: {
        enterpriseName: 'baoshu',
        accountName: name
      }
    }
  )
  if (!config) {
    throw new Error('Config not found')
  }
  const enterpriseConfig = config.enterpriseConfig as unknown as IBaoshuEnterpriseConfig

  const account =  {
    orgToken: config.orgToken,
    nickname: config.accountName,
    wechatId: config.wechatId,
    botUserId: config.botUserId,
    address: config.address,
    port: Number(config.port),
  }

  return {
    orgToken: account.orgToken,
    id: account.wechatId,
    name: account.nickname,
    botUserId: account.botUserId,
    notifyGroupId: enterpriseConfig.notifyGroupId,
    counselorIds: enterpriseConfig.counselors,
    xbbId: enterpriseConfig.xbbId
  }
}


export async function loadConfigByWxId(id: string): Promise<IWechatConfig> {
  const config = await PrismaMongoClient.getConfigInstance().config.findFirst(
    {
      where: {
        enterpriseName: 'baoshu',
        wechatId: id
      }
    }
  )
  if (!config) {
    throw new Error('Config not found')
  }

  const enterpriseConfig = config.enterpriseConfig as unknown as IBaoshuEnterpriseConfig

  const account =  {
    orgToken: config.orgToken,
    nickname: config.accountName,
    wechatId: config.wechatId,
    botUserId: config.botUserId,
    address: config.address,
    port: Number(config.port),
  }

  return {
    orgToken: account.orgToken,
    id: account.wechatId,
    name: account.nickname,
    botUserId: account.botUserId,
    notifyGroupId: enterpriseConfig.notifyGroupId,
    counselorIds: enterpriseConfig.counselors,
    xbbId: enterpriseConfig.xbbId
  }
}