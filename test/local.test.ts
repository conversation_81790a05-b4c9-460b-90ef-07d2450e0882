import { AIMessage, ChatHistoryService, UserMessage } from '../bot/service/baoshu/components/chat_history'
import { ChatStateStore } from '../bot/service/baoshu/storage/chat_state_store'
import { ConversationRound, SourceType } from '../bot/service/baoshu/database/conversation_round'
import { BaoshuFlow } from '../bot/service/baoshu/components/flow/flow'
import { UUID } from '../bot/lib/uuid/uuid'
import { getChatId } from '../bot/config/chat_id'
import { Config } from '../bot/config/config'
import { updateEntrypointsFrom0_0_xTo0_1_x } from '@langchain/scripts/migrations'
import path from 'path'

describe('Test', function () {
  beforeAll(() => {

    Config.setting.BOT_NAME = '小爱'

    Config.setting.localTest = true
  })


  it('migration', async () => {
    await updateEntrypointsFrom0_0_xTo0_1_x({
      // Path to the local langchainjs repository
      localLangChainPath: path.join(__dirname, '..', 'node_modules', 'langchain'),
      // Path to the repository where the migration should be applied
      codePath: path.join(__dirname, '..', 'bot')
    })
  }, 30000)

  it('test bad cases', async () => {
    const userMap = {
      // 'test': ['不是这个', '已经发了', '不需要了']
      'test2': ['超稀缺！我发现一本书解决几乎任何代码问题', '超稀缺！我发现一本书解决几乎任何代码问题']
      // 'sunshine': ['1', '正在学习中，主要是想稍微了解一下，能看懂算法就可以了', '本科生', '不是ai专业的，是因为大创用到了联邦学习这个模型，老师说最好能有技术的实现，所以就自己学习学习，找找代码'],
      // '挖需信任感没建立好，太着急': ['好的感谢', '是的', '暂时先不需要，之后要是有需要的话我再联系您。'],
      // 'YGR': ['好的好的 感谢', '研究生阶段', '还没正式学', '好的好的', 'nlp吧', '先等等吧'],
      // 'yolo': ['1', '对', '还没有呢'],
      // '0318豆浆': ['1', '一般', '本科'],
      // 'Dream': ['1','目前想收集创意', '本科生哦\n 可以哦', '刚开始接触哦', '实际原理' ],
      // '历历万乡': ['1', '还好', '博士生', 'CV', '感谢资料分享！其余推荐不需要！谢谢！']
    }
    const promises: Promise<any>[] = []
    for (const user in userMap) {
      promises.push(localTest(userMap[user]))
    }

    await Promise.all(promises)
  }, 1E8)
})

export async function localTest(userInputs: string[]) {
  const chatHistory = [
    new AIMessage(`hihi，我是up老师的小助教，拥有up所有的资料库，叫我小爱就好
同学是来领资料的吧，可以复制【视频标题】给我，或一键转发B站视频，我好去给你找哦~`),
    new UserMessage('70万大学生研究生收藏的深度学习机器学习代码逐行解读'),
    new AIMessage(`【学习路线与pdf】链接: https://pan.baidu.com/s/1L7KD8Zu8FyGZFfDMPWpGvA?pwd=1pyc 提取码: 1pyc 
--来自百度网盘超级会员v3的分享`),
    new AIMessage('资料已经发给你了，没问题的话，给我回复个1哦')
  ]

  // 聊天状态回放
  // 设置聊天记录
  // 聊天记录 + 用户输入回放
  const user_id = UUID.short()
  const chat_id = getChatId(user_id)
  await ChatHistoryService.setChatHistory(chat_id, chatHistory)

  for (const user_input of userInputs) {
    const roundLogger = new ConversationRound(chat_id, '', SourceType.AdminTest)
    roundLogger.setInput(user_input)
    await ChatHistoryService.addUserMessage(chat_id, user_input)

    await BaoshuFlow.step(chat_id, user_id, user_input)
  }
}