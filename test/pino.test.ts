import pino from 'pino'
import { sleep } from '../bot/lib/schedule/schedule'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    const transport = pino.transport({
      targets: [
        {
          target: 'pino-mongodb',
          options: {
            uri: 'mongodb://root:free1234$spirit!@dds-bp183f68818ca1d4-pub.mongodb.rds.aliyuncs.com:3717/admin',
            database: 'freespirit',
            collection: 'chat_log',
          },
          level: 'info',
        }
      ]
    })

    const logger = pino(transport)
    logger.info({ 'content': 'hello' })

    await sleep(1000 * 5)
  })
})