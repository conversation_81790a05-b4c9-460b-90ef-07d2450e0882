import { PrismaClient } from '@prisma/client'
import { CommonRegexType, RegexHelper } from '../bot/lib/regex/regex'
import { ChatHistoryService } from '../bot/service/baoshu/components/chat_history'

describe('Test', function () {
  beforeAll(() => {

  })

  it('数字替换', async () => {
    function numberToChinese(text: string): string {
      // 阿拉伯数字到中文数字的映射
      const chineseNumbers = {
        '0': '零',
        '1': '一',
        '2': '二',
        '3': '三',
        '4': '四',
        '5': '五',
        '6': '六',
        '7': '七',
        '8': '八',
        '9': '九'
      }

      // 替换文本中的每个数字为对应的中文数字
      return text.replace(/\d+/g, (match) => {
        if (match.length === 2 && match[0] === '1') {
          // 如果是两位数并且第一位是 1，则将其替换为 '十'
          return `十${chineseNumbers[match[1]]}`
        }
        return match.split('').map((digit) => chineseNumbers[digit]).join('')
      })
    }

    console.log(numberToChinese('我有15个苹果'))

  }, 60000)

  it('should pass', async () => {
    const prisma = new PrismaClient()
    console.log(JSON.stringify(await prisma.chat_history.create({
      data: {
        chat_id: 'xxxx',
        role: 'assistant',
        content: 'hi',
        created_at: new Date()
      }
    }), null, 4))
  })

  it('', async () => {
    function extractSentence (text: string): [string, string] {
      const index = text.search (/[!?~。！？~\n]/)
      if (index !== -1) {
        return [text.substring (0, index + 1).trim (), text.substring (index + 1)]
      }
      return [text.trim (), '']
    }
    let currentSentence = `首先准备好本科成绩单和学位证书

然后考个雅思，目标6.5分以上

接下来就是选校和申请材料，咱们可以详细聊聊`
    while ((!RegexHelper.strIncludeRegex (currentSentence, CommonRegexType.URL)) && currentSentence.search (/[!?~。！？~\n]/) !== -1) {
      const [sentence, remaining] = extractSentence (currentSentence)
      if (sentence) {
        console.log(sentence)
      }
      currentSentence = remaining
    }

  }, 60000)


  it('1', async () => {
    console.log(RegexHelper.strIncludeRegex (`首先准备好本科成绩单和学位证书

然后考个雅思，目标6.5分以上

接下来就是选校和申请材料，咱们可以详细聊聊`, CommonRegexType.URL))
  }, 60000)

  it('12', async () => {
    console.log(JSON.stringify(await ChatHistoryService.getChatHistory('7881300846030208_1688854546332791'), null, 4))
  }, 60000)

  it('123123', async () => {
    const isAiCompleteSlotAskTask = /.*[？?么吗啥].*/.test(`先把绩点搞清楚，绩点很关键

然后大三大四多参加科研项目和实习

考研和申请美国研究生都需要这些背景`) ? 'true' : 'false' // 问句检查

    console.log(isAiCompleteSlotAskTask)
  }, 60000)
})