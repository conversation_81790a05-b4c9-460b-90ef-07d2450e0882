import { Client, ClientOptions } from '@elastic/elasticsearch'
import { Config } from '../bot/config/config'
import { ElasticClientArgs, ElasticVectorSearch } from '@langchain/community/vectorstores/elasticsearch'
import { Document } from '@langchain/core/documents'
import { AzureOpenAIEmbedding } from '../bot/lib/ai/llm/openai_embedding'
import { JSONHelper } from '../bot/lib/json/json'
import { FileHelper } from '../bot/lib/file'
import { execSync } from 'child_process'
import path from 'path'
import ElasticSearchService from '../bot/service/elastic_search1/elastic_search'
import { OpenAIClient } from '../bot/lib/ai/llm/client'
import fs from 'fs'
import { HumanMessage } from '@langchain/core/messages'

describe('Test', function () {
  beforeAll(() => {

  })

  it('remove index', async () => {
    await ElasticSearchService.deleteIndex('emoji_test')
  }, 60000)

  it('should pass', async () => {
    // 构建 embedding, 插入到 es 中
    const config: ClientOptions = {
      node: Config.setting.elasticSearch.url,
    }
    config.auth = {
      username: Config.setting.elasticSearch.username,
      password: Config.setting.elasticSearch.password,
    }
    const clientArgs: ElasticClientArgs = {
      client: new Client(config),
      indexName: 'emoji_test',
    }

    const emojiList = JSONHelper.parse(await FileHelper.readFile('/Users/<USER>/Downloads/emo-visual-data/data.json'))
    // Index documents
    const docs = emojiList.map((item) => {
      return new Document({
        metadata: { filename: item.filename },
        pageContent: item.content,
      })
    })

    const embeddings = AzureOpenAIEmbedding.getInstance()
    const vectorStore = new ElasticVectorSearch(embeddings, clientArgs)

    const batchSize = 500
    let ids: any[] = []

    for (let i = 0; i < docs.length; i += batchSize) {
      const batch = docs.slice (i, i + batchSize)
      const batchIds = await vectorStore.addDocuments (batch)
      ids = ids.concat (batchIds)
      console.log (`Inserted batch ${i / batchSize + 1}`)
    }

    console.log ('All documents inserted.')
  }, 1E8)

  it('search', async () => {
    const config: ClientOptions = {
      node: Config.setting.elasticSearch.url,
    }
    config.auth = {
      username: Config.setting.elasticSearch.username,
      password: Config.setting.elasticSearch.password,
    }
    const clientArgs: ElasticClientArgs = {
      client: new Client(config),
      indexName: 'emoji_test',
    }
    const embeddings = AzureOpenAIEmbedding.getInstance()
    const vectorStore = new ElasticVectorSearch(embeddings, clientArgs)

    /* Search the vector DB independently with meta filters */
    const results = await vectorStore.similaritySearch('打工是不可能打工的', 3)
    console.log(JSON.stringify(results, null, 2))

    for (const result of results) {
      execSync(`open ${path.join('/Users/<USER>/Downloads/emo-visual-data/emo', result.metadata.filename)}`)
    }
  }, 60000)

  it('gpt4o', async () => {
    const imageData = await FileHelper.readFileAsBuffer(path.join('/Users/<USER>/Downloads/emo-visual-data/emo', '2a9e2477-f38a-4a98-9bb5-ae4860a0f39a.jpg'))
    const chat = OpenAIClient.getClient()
    const message = new HumanMessage({
      content: [
        {
          type: 'text',
          text: '描述一下这张表情包',
        },
        {
          type: 'image_url',
          image_url: {
            url: `data:image/jpeg;base64,${imageData.toString('base64')}`,
          },
        },
      ],
    })

    const res = await chat.predictMessages([message])
    console.log(res.content)
  }, 60000)

  it('123', async () => {
    const text = '{"title":"","content":[[{"tag":"at","user_id":"@_user_1","user_name":"Smile, Be Happy :)","style":[]},{"tag":"text","text":" ","style":[]}],[{"tag":"img","image_key":"img_v3_02bt_0bac843a-83e3-403d-bb4a-98d3afeabc5g","width":440,"height":377}]]}'

    // 解析 JSON
    const data = JSON.parse(text)

    // 遍历 content 寻找第一个 img 标签并获取 image_key
    function getImageKey(content: any[]): string | null {
      for (const section of content) {
        for (const item of section) {
          if (item.tag === 'img' && item.image_key) {
            return item.image_key
          }
        }
      }
      return null
    }

    const imageKey = getImageKey(data.content)
    console.log(imageKey)  // 输出: img_v3_02bt_0bac843a-83e3-403d-bb4a-98d3afeabc5g
  }, 60000)
})