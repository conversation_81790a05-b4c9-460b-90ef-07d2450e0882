import { PrismaMongoClient } from '../bot/model/mongodb/prisma'


describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    // 数据库清洗
    // 拉出所有 chatState 数据，把 chat_state 的 roundAfterConfirmGroupPush 字段改为0
    const data = await PrismaMongoClient.getInstance().chat_state.findMany({})

    for (const chatState of data) {
      await PrismaMongoClient.getInstance().chat_state.update({
        where: {
          id: chatState.id
        },
        data: {
          chat_state: {
            ...chatState.chat_state,
            isSendZiliaoForwardImage: false
          }
        }
      })
    }

  }, 1E8)
})