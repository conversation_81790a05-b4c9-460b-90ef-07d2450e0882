// import { <PERSON><PERSON><PERSON><PERSON> } from 'langchain/vectorstores/milvus'
// import { FileHelper } from '../bot/lib/file'
// import path from 'path'
// import { Document } from 'langchain/document'
// import { FaissStore } from 'langchain/vectorstores/faiss'
// import { OpenaiEmbedding } from '../bot/lib/ai/llm/openai_embedding'
// import { MilvusClient } from '@zilliz/milvus2-sdk-node'
// import { OpenAIEmbeddings } from 'langchain/embeddings/openai'
// describe('Test', function () {
//   beforeAll(() => {
//
//   })
//
//   it('should pass', async () => {
//     const rawData = await FileHelper.readFile(path.join(process.cwd(), 'dev', 'data.json'))
//     const data = JSON.parse(rawData)
//
//     let maxLength = 0
//     let maxLengthIndex = 0
//     const textEncoder = new TextEncoder()
//     const getL = (str: string) => {
//       return textEncoder.encode(str).length
//     }
//
//     const docs = data.map((doc, index) => {
//       if (getL(doc['question']) > maxLength) {
//         maxLength = getL(doc['question'])
//         maxLengthIndex = index
//       }
//
//       if (getL(doc['answer']) > maxLength) {
//         maxLength = getL(doc['answer'])
//         maxLengthIndex = index
//       }
//
//       return new Document({
//         pageContent: doc['question'],
//         metadata: {
//           answer: doc['answer']
//         }
//       })
//     })
//
//     const vectorStore = await Milvus.fromDocuments(docs, OpenaiEmbedding.getInstance(), {
//       collectionName: 'qa',
//       clientConfig: {
//         address: 'https://in01-d1c034bfa918872.tc-ap-beijing.vectordb.zilliz.com.cn:443',
//         token: '09f45b589a5043055d36508a190de2bca084d88fcabc6384e6e0df7e6c89f9d207c8eec6701f81664203b2f597dae70ad090d5de'
//       }
//     })
//
//     // const vectorStore = await Milvus.fromExistingCollection(
//     //     OpenaiEmbedding.getInstance(),
//     //     {
//     //         collectionName: 'qa',
//     //         clientConfig: {
//     //             address: 'https://in01-d1c034bfa918872.tc-ap-beijing.vectordb.zilliz.com.cn:443',
//     //             token: '09f45b589a5043055d36508a190de2bca084d88fcabc6384e6e0df7e6c89f9d207c8eec6701f81664203b2f597dae70ad090d5de'
//     //         }
//     //     }
//     // )
//
//
//     console.log(JSON.stringify(await vectorStore.similaritySearchWithScore('你好', 2), null, 4))
//   }, 1E8)
// })