import { RedisDB } from '../bot/model/redis/redis'
import { Queue, Worker } from 'bullmq'
import { ChatHistoryService } from '../bot/service/baoshu/components/chat_history'
import { BaoshuFlow } from '../bot/service/baoshu/components/flow/flow'
import { getUserId } from '../bot/config/chat_id'
import { UUID } from '../bot/lib/uuid/uuid'
import logger from '../bot/model/logger/logger'
import { sleep } from '../bot/lib/schedule/schedule'

describe('Test', function () {
  beforeAll(() => {

  })

  async function createTask(chat_id: string) {
    const queue = new Queue('testQueue', {
      connection: RedisDB.getInstance(),
    })

    const currentTime = new Date()
    const tenAM = new Date()

    tenAM.setHours(currentTime.getHours(), currentTime.getMinutes(), currentTime.getSeconds() + 3, 0)

    const timeDifference = tenAM.getTime() - currentTime.getTime()

    if (timeDifference < 0) {
      return
    }

    console.log(`凌晨信息延迟 ${timeDifference} ms 处理`)

    await queue.add('lateNightDelayReply', { chat_id }, { jobId: chat_id, delay:  timeDifference }) // 使用 jobId 保证 同一个 chat_id 下的任务只被添加一次
  }


  function startWorker() {
    const worker = new Worker('testQueue', async (job) => {
      // 获取聊天记录，进行回复
      const chatHistory = await ChatHistoryService.getLastUserMessages(job.data.chat_id)
      if (chatHistory.length === 0) {
        return
      }

      // 删除最后一组聊天记录
      await ChatHistoryService.deleteByIds(chatHistory.map((message) => message.id))

      // 将最后一组，未回复的 用户信息进行回放
      // 把这些聊天记录删除
      const userMessage = chatHistory.map((message) => message.content).join('\n')
      const chat_id = job.data.chat_id
      const user_id = getUserId(chat_id)

      await BaoshuFlow.step(chat_id, user_id, userMessage)
    }, {
      connection: RedisDB.getInstance(),
      concurrency: 10, // 设置并发量为 10
      lockDuration: 10 * 10000 // 设置超时时间为 90 秒（以毫秒为单位）
    })

    worker.on('failed', (job, err) => {
      console.error(err)
      logger.error(err)
    })
  }

  it('1231sdada', async () => {
    const queue = new Queue('user-message-processor-queue-test', { connection: RedisDB.getInstance() })

    console.log(JSON.stringify(await queue.getJobs(), null, 4))
  }, 60000)

  it('should pass', async () => {
    startWorker()
    const chatId1 = UUID.short()
    await ChatHistoryService.addUserMessage(chatId1, 'hi')
    await createTask(chatId1)
    await ChatHistoryService.addUserMessage(chatId1, '暴叔你好，我要咨询下')
    await createTask(chatId1)
    await ChatHistoryService.addUserMessage(chatId1, '我现在是国内二本大三再读 预算大概100万左右，有什么推荐吗')
    await createTask(chatId1)

    // const chatId2 = UUID.short()
    //
    // await ChatHistoryService.addUserMessage(chatId2, '想去土耳其')
    // await createTask(chatId2)
    //
    // const chatId3 = UUID.short()
    //
    // await ChatHistoryService.addUserMessage(chatId3, '哦哦，暴叔有什么具体一点的建议么，因为我成绩一般，GPA 只有 70')
    // await createTask(chatId3)
    //
    // const chatId4 = UUID.short()
    //
    // await ChatHistoryService.addUserMessage(chatId4, '哦哦，暴叔有什么具体一点的建议么，因为我成绩一般，GPA 只有 70')
    // await createTask(chatId4)
    //
    await sleep(120 * 1000)
  }, 1E8)
})